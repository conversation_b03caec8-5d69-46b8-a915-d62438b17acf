{"language_models": {"bert_multilingual": "bert-base-multilingual-cased", "xlm_roberta": "xlm-roberta-base", "distilbert_multilingual": "distilbert-base-multilingual-cased"}, "sentence_encoders": {"mpnet_base": "all-mpnet-base-v2", "paraphrase_multilingual": "paraphrase-multilingual-mpnet-base-v2", "distiluse_multilingual": "distiluse-base-multilingual-cased"}, "model_paths": {"language_models": "models/language_models", "sentence_encoders": "models/sentence_encoders", "classification_models": "models/classification_models", "embeddings": "models/embeddings"}, "default_models": {"query_classification": "bert-base-multilingual-cased", "sentence_embedding": "paraphrase-multilingual-mpnet-base-v2", "intent_detection": "xlm-roberta-base"}}