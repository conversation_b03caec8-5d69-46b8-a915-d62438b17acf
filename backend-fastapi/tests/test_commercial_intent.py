import unittest
import asyncio
import os
import sys
from pathlib import Path

# 添加項目根目錄到 Python 路徑
project_root = Path(__file__).parents[1]
sys.path.append(str(project_root))

from app.services.processors.intent_classification_processor import IntentClassificationProcessor
from app.services.simple_model_trainer import BasicModelTrainer


class TestCommercialIntent(unittest.TestCase):
    """測試商業意圖分類和商業價值評分功能"""

    def setUp(self):
        self.intent_processor = IntentClassificationProcessor()
        self.model_trainer = BasicModelTrainer()
        
    def test_commercial_value_evaluation(self):
        """測試商業價值評分功能"""
        # 高商業價值的查詢
        high_commercial_queries = [
            "哪裡買Apple MacBook Pro最便宜",
            "想購買最新的iPhone 15，有什麼優惠",
            "比較Sony和Bose降噪耳機哪個性價比更高",
            "推薦台北最好的SEO公司",
            "提高網站轉換率和銷售額的方法"
        ]
        
        # 低商業價值的查詢
        low_commercial_queries = [
            "Python如何讀取JSON檔案",
            "台北今天天氣如何",
            "人工智能的發展歷史",
            "如何學習英語",
            "台灣的教育制度"
        ]
        
        # 測試高商業價值查詢
        for query in high_commercial_queries:
            result = self.intent_processor.process(query)
            self.assertGreaterEqual(result.get("commercial_value", 0), 0.6, 
                               f"高商業價值查詢評分過低: {query}, 分數: {result.get('commercial_value', 0)}")
            
        # 測試低商業價值查詢
        for query in low_commercial_queries:
            result = self.intent_processor.process(query)
            self.assertLessEqual(result.get("commercial_value", 1), 0.4, 
                             f"低商業價值查詢評分過高: {query}, 分數: {result.get('commercial_value', 0)}")
    
    def test_commercial_intent_classification(self):
        """測試商業意圖分類功能"""
        # 定義各類意圖的查詢
        intent_test_cases = {
            "purchase_intent": ["哪裡買Apple MacBook Pro最便宜", "想購買最新的iPhone 15，有什麼優惠"],
            "comparison_intent": ["iPhone 15 Pro和Samsung S23 Ultra哪個相機拍照更好", "小米和華為智能手環功能和價格比較"],
            "service_inquiry": ["推薦台北最好的SEO公司", "找尋能製作企業形象影片的製作公司"],
            "ecommerce": ["如何在蝦皮開店並增加曝光率", "電商網站如何優化購物車放棄率"],
            "informational": ["Python如何讀取JSON檔案", "人工智能的發展歷史"]
        }
        
        for expected_intent, queries in intent_test_cases.items():
            for query in queries:
                result = self.intent_processor.process(query)
                # 檢查意圖分類是否正確或相關
                if expected_intent == "informational":
                    self.assertFalse(result.get("is_commercial_intent", True), 
                                f"資訊性查詢被錯誤標記為商業意圖: {query}")
                else:
                    self.assertTrue(result.get("is_commercial_intent", False) or 
                               result.get("intent", "").startswith(expected_intent) or
                               expected_intent in result.get("intent", ""),
                               f"商業意圖分類錯誤: {query}, 預期: {expected_intent}, 實際: {result.get('intent')}")
    
    def test_commercial_value_weight(self):
        """測試商業價值權重對分類結果的影響"""
        # 選擇一些模糊的查詢案例，可能是商業也可能是信息查詢
        ambiguous_queries = [
            "網站SEO優化技巧",
            "如何設計高轉換率的著陸頁",
            "最佳WordPress插件推薦",
            "社群媒體行銷策略"
        ]
        
        async def run_test():
            # 使用不同權重進行預測
            low_weight_results = await self.model_trainer.predict(ambiguous_queries, apply_commercial_boost=True)
            no_weight_results = await self.model_trainer.predict(ambiguous_queries, apply_commercial_boost=False)
            
            # 檢查權重是否影響分類結果
            changes_count = 0
            for i, query in enumerate(ambiguous_queries):
                low_weight_label = low_weight_results["results"][i]["predicted_label"]
                no_weight_label = no_weight_results["results"][i]["predicted_label"]
                
                # 如果分類結果有變化，計數
                if low_weight_label != no_weight_label:
                    changes_count += 1
            
            # 檢查是否有至少一個查詢的分類結果因權重而改變
            self.assertGreater(changes_count, 0, "商業價值權重似乎沒有影響分類結果")
        
        # 執行非同步測試
        asyncio.run(run_test())


class TestModelTraining(unittest.TestCase):
    """測試模型訓練與商業價值整合"""
    
    def setUp(self):
        self.model_trainer = BasicModelTrainer()
    
    def test_feature_extraction(self):
        """測試特徵提取功能是否正確識別商業相關特徵"""
        commercial_text = "想購買最新的iPhone 15，有什麼優惠和折扣方案可以推薦"
        features = self.model_trainer._extract_features(commercial_text)
        
        # 檢查商業相關特徵
        self.assertGreater(features.get("keyword_purchase_count", 0), 0, "未能識別購買關鍵詞")
        self.assertGreater(features.get("keyword_commercial_count", 0), 0, "未能識別商業關鍵詞")
        self.assertGreater(features.get("commercial_ratio", 0), 0.2, "商業詞彙比例過低")
    
    def test_commercial_value_estimation(self):
        """測試商業價值估算功能"""
        queries = [
            "想購買最新的iPhone 15，有什麼優惠",  # 高商業價值
            "Python如何讀取JSON檔案"  # 低商業價值
        ]
        
        for query in queries:
            features = self.model_trainer._extract_features(query)
            commercial_value = self.model_trainer._estimate_commercial_value(query, features)
            
            if "購買" in query or "優惠" in query:
                self.assertGreaterEqual(commercial_value, 0.5, f"高商業價值查詢評分過低: {query}")
            else:
                self.assertLessEqual(commercial_value, 0.3, f"低商業價值查詢評分過高: {query}")


if __name__ == "__main__":
    unittest.main()
