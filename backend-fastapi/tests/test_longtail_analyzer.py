"""
長尾查詢識別系統 - 單元測試
測試長尾查詢分析器的各項功能
"""

import pytest
import asyncio
from unittest.mock import Mock, patch
from datetime import datetime

from app.services.longtail_query_analyzer import (
    LongTailQueryAnalyzer,
    BatchLongTailAnalyzer,
    QueryType,
    QueryIntent,
    QueryComplexity
)

class TestLongTailQueryAnalyzer:
    """長尾查詢分析器測試"""
    
    @pytest.fixture
    def analyzer(self):
        """創建分析器實例"""
        return LongTailQueryAnalyzer()
    
    @pytest.mark.asyncio
    async def test_analyze_simple_query(self, analyzer):
        """測試簡單查詢分析"""
        query = "AI工具"
        result = await analyzer.analyze_query(query)
        
        assert result.query == query
        assert result.query_type in [QueryType.HEAD, QueryType.MIDDLE]
        assert result.features.word_count == 1
        assert result.features.language == 'zh'
        assert 0 <= result.longtail_score <= 1
        assert 0 <= result.confidence <= 1
    
    @pytest.mark.asyncio
    async def test_analyze_complex_query(self, analyzer):
        """測試複雜查詢分析"""
        query = "如何選擇最適合小型企業的AI客服系統並進行有效整合"
        result = await analyzer.analyze_query(query)
        
        assert result.query == query
        assert result.query_type == QueryType.LONG_TAIL
        assert result.complexity == QueryComplexity.COMPLEX
        assert result.features.has_question_words == True
        assert result.longtail_score > 0.5
    
    @pytest.mark.asyncio
    async def test_analyze_english_query(self, analyzer):
        """測試英文查詢分析"""
        query = "best AI tools for content marketing automation"
        result = await analyzer.analyze_query(query)
        
        assert result.query == query
        assert result.features.language == 'en'
        assert result.query_type == QueryType.LONG_TAIL
        assert len(result.keywords) > 0
    
    @pytest.mark.asyncio
    async def test_analyze_commercial_query(self, analyzer):
        """測試商業意圖查詢"""
        query = "購買最便宜的AI寫作軟體價格比較"
        result = await analyzer.analyze_query(query)
        
        assert result.intent == QueryIntent.COMMERCIAL
        assert result.features.commercial_intent_score > 0.5
        assert "商業意圖" in str(result.optimization_suggestions)
    
    @pytest.mark.asyncio
    async def test_analyze_local_query(self, analyzer):
        """測試本地查詢"""
        query = "台北附近的AI培訓課程"
        result = await analyzer.analyze_query(query)
        
        assert result.intent == QueryIntent.LOCAL
        assert result.features.has_location == True
        assert "本地SEO" in str(result.optimization_suggestions)
    
    def test_extract_features(self, analyzer):
        """測試特徵提取"""
        query = "什麼是最好的AI工具？"
        features = analyzer._extract_features(query)
        
        assert features.length > 0
        assert features.word_count > 0
        assert features.has_question_words == True
        assert features.has_special_chars == True
        assert features.language == 'zh'
    
    def test_classify_query_type(self, analyzer):
        """測試查詢類型分類"""
        # 頭部查詢
        features_head = Mock()
        features_head.word_count = 1
        features_head.specificity_score = 0.2
        assert analyzer._classify_query_type("AI", features_head) == QueryType.HEAD
        
        # 長尾查詢
        features_longtail = Mock()
        features_longtail.word_count = 8
        features_longtail.specificity_score = 0.8
        assert analyzer._classify_query_type("長查詢", features_longtail) == QueryType.LONG_TAIL
    
    def test_detect_intent(self, analyzer):
        """測試意圖檢測"""
        # 信息型查詢
        features_info = Mock()
        features_info.has_question_words = True
        features_info.commercial_intent_score = 0.1
        features_info.has_location = False
        assert analyzer._detect_intent("什麼是AI", features_info) == QueryIntent.INFORMATIONAL
        
        # 商業型查詢
        features_commercial = Mock()
        features_commercial.has_question_words = False
        features_commercial.commercial_intent_score = 0.8
        features_commercial.has_location = False
        assert analyzer._detect_intent("AI價格", features_commercial) == QueryIntent.COMMERCIAL
    
    def test_calculate_longtail_score(self, analyzer):
        """測試長尾分數計算"""
        features = Mock()
        features.word_count = 6
        features.specificity_score = 0.7
        features.has_question_words = True
        features.has_numbers = True
        features.has_location = False
        
        score = analyzer._calculate_longtail_score("測試查詢", features)
        assert 0 <= score <= 1
        assert score > 0.5  # 應該是高長尾分數

class TestBatchLongTailAnalyzer:
    """批量長尾查詢分析器測試"""
    
    @pytest.fixture
    def batch_analyzer(self):
        """創建批量分析器實例"""
        analyzer = LongTailQueryAnalyzer()
        return BatchLongTailAnalyzer(analyzer)
    
    @pytest.mark.asyncio
    async def test_analyze_batch(self, batch_analyzer):
        """測試批量分析"""
        queries = [
            "AI工具",
            "如何選擇最好的AI寫作軟體",
            "人工智能在醫療領域的應用前景分析",
            "buy AI software",
            "best machine learning courses online"
        ]
        
        results = await batch_analyzer.analyze_batch(queries)
        
        assert len(results) == len(queries)
        assert all(hasattr(result, 'query') for result in results)
        assert all(hasattr(result, 'longtail_score') for result in results)
    
    def test_generate_batch_report(self, batch_analyzer):
        """測試批量報告生成"""
        # 創建模擬結果
        mock_results = []
        for i in range(10):
            result = Mock()
            result.query_type = QueryType.LONG_TAIL if i % 2 == 0 else QueryType.HEAD
            result.intent = QueryIntent.INFORMATIONAL
            result.complexity = QueryComplexity.MODERATE
            result.longtail_score = 0.7 if i % 2 == 0 else 0.3
            result.confidence = 0.8
            result.features = Mock()
            result.features.commercial_intent_score = 0.5 if i % 3 == 0 else 0.2
            result.features.has_question_words = i % 4 == 0
            result.features.has_location = i % 5 == 0
            result.semantic_category = "technology"
            mock_results.append(result)
        
        report = batch_analyzer.generate_batch_report(mock_results)
        
        assert "summary" in report
        assert "distributions" in report
        assert "high_value_longtail" in report
        assert "recommendations" in report
        assert report["summary"]["total_queries"] == 10

class TestQueryFeatureExtraction:
    """查詢特徵提取測試"""
    
    @pytest.fixture
    def analyzer(self):
        return LongTailQueryAnalyzer()
    
    def test_language_detection(self, analyzer):
        """測試語言檢測"""
        assert analyzer._detect_language("這是中文查詢") == 'zh'
        assert analyzer._detect_language("This is English query") == 'en'
        assert analyzer._detect_language("混合 mixed 查詢") == 'zh'  # 中文字符更多
    
    def test_question_words_detection(self, analyzer):
        """測試疑問詞檢測"""
        assert analyzer._has_question_words("什麼是AI", 'zh') == True
        assert analyzer._has_question_words("How to use AI", 'en') == True
        assert analyzer._has_question_words("AI工具推薦", 'zh') == False
        assert analyzer._has_question_words("AI tools recommendation", 'en') == False
    
    def test_location_detection(self, analyzer):
        """測試地理位置檢測"""
        assert analyzer._has_location("台北的AI公司", 'zh') == True
        assert analyzer._has_location("AI companies near me", 'en') == True
        assert analyzer._has_location("AI技術發展", 'zh') == False
    
    def test_commercial_intent_calculation(self, analyzer):
        """測試商業意圖計算"""
        commercial_score = analyzer._calculate_commercial_intent("購買AI軟體價格", 'zh')
        assert commercial_score > 0.3
        
        non_commercial_score = analyzer._calculate_commercial_intent("AI技術原理", 'zh')
        assert non_commercial_score < 0.3
    
    def test_keyword_extraction(self, analyzer):
        """測試關鍵詞提取"""
        keywords = analyzer._extract_keywords("如何選擇最好的AI寫作工具")
        assert len(keywords) > 0
        assert "選擇" in keywords
        assert "AI" in keywords
        assert "工具" in keywords
        assert "的" not in keywords  # 停用詞應該被過濾

class TestPerformanceAndEdgeCases:
    """性能和邊界情況測試"""
    
    @pytest.fixture
    def analyzer(self):
        return LongTailQueryAnalyzer()
    
    @pytest.mark.asyncio
    async def test_empty_query(self, analyzer):
        """測試空查詢"""
        with pytest.raises(Exception):
            await analyzer.analyze_query("")
    
    @pytest.mark.asyncio
    async def test_very_long_query(self, analyzer):
        """測試超長查詢"""
        long_query = "AI " * 100  # 200個字符
        result = await analyzer.analyze_query(long_query)
        
        assert result.query_type == QueryType.LONG_TAIL
        assert result.longtail_score > 0.8
    
    @pytest.mark.asyncio
    async def test_special_characters_query(self, analyzer):
        """測試特殊字符查詢"""
        special_query = "AI工具@#$%^&*()價格?"
        result = await analyzer.analyze_query(special_query)
        
        assert result.features.has_special_chars == True
        assert result.query is not None
    
    @pytest.mark.asyncio
    async def test_numeric_query(self, analyzer):
        """測試包含數字的查詢"""
        numeric_query = "2024年最新AI工具排行榜前10名"
        result = await analyzer.analyze_query(numeric_query)
        
        assert result.features.has_numbers == True
        assert result.query_type == QueryType.LONG_TAIL
    
    @pytest.mark.asyncio
    async def test_performance_batch_analysis(self, analyzer):
        """測試批量分析性能"""
        import time
        
        queries = [f"AI工具查詢{i}" for i in range(50)]
        batch_analyzer = BatchLongTailAnalyzer(analyzer)
        
        start_time = time.time()
        results = await batch_analyzer.analyze_batch(queries)
        end_time = time.time()
        
        assert len(results) == len(queries)
        assert (end_time - start_time) < 30  # 應該在30秒內完成
    
    def test_confidence_calculation(self, analyzer):
        """測試置信度計算"""
        features = Mock()
        features.word_count = 5
        features.specificity_score = 0.7
        
        confidence = analyzer._calculate_confidence(features, 0.8)
        assert 0 <= confidence <= 1

# 集成測試
class TestLongTailIntegration:
    """長尾查詢系統集成測試"""
    
    @pytest.mark.asyncio
    async def test_end_to_end_analysis(self):
        """端到端分析測試"""
        analyzer = LongTailQueryAnalyzer()
        
        # 測試不同類型的查詢
        test_queries = [
            ("AI", QueryType.HEAD),
            ("AI工具推薦", QueryType.MIDDLE),
            ("如何為中小企業選擇最適合的AI客服系統並進行有效整合", QueryType.LONG_TAIL)
        ]
        
        for query, expected_type in test_queries:
            result = await analyzer.analyze_query(query)
            assert result.query_type == expected_type
            assert result.longtail_score >= 0
            assert result.confidence >= 0
            assert len(result.keywords) >= 0
            assert result.semantic_category is not None
    
    @pytest.mark.asyncio
    async def test_multilingual_support(self):
        """多語言支持測試"""
        analyzer = LongTailQueryAnalyzer()
        
        queries = [
            ("什麼是最好的AI工具", 'zh'),
            ("What are the best AI tools", 'en'),
            ("AI工具 best tools 推薦", 'zh')  # 混合語言
        ]
        
        for query, expected_lang in queries:
            result = await analyzer.analyze_query(query)
            assert result.features.language == expected_lang

if __name__ == "__main__":
    pytest.main([__file__, "-v"])

# 性能測試
class TestLongTailPerformance:
    """長尾查詢系統性能測試"""

    @pytest.mark.asyncio
    async def test_concurrent_analysis(self):
        """並發分析測試"""
        analyzer = LongTailQueryAnalyzer()

        async def analyze_query_task(query):
            return await analyzer.analyze_query(f"AI工具{query}")

        # 創建10個並發任務
        tasks = [analyze_query_task(i) for i in range(10)]
        results = await asyncio.gather(*tasks)

        assert len(results) == 10
        assert all(result.query is not None for result in results)

    @pytest.mark.asyncio
    async def test_memory_usage(self):
        """內存使用測試"""
        import psutil
        import os

        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss

        analyzer = LongTailQueryAnalyzer()

        # 分析大量查詢
        for i in range(100):
            await analyzer.analyze_query(f"測試查詢{i}")

        final_memory = process.memory_info().rss
        memory_increase = final_memory - initial_memory

        # 內存增長應該在合理範圍內 (< 100MB)
        assert memory_increase < 100 * 1024 * 1024
