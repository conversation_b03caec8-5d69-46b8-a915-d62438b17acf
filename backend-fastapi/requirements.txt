# FastAPI 核心依賴
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
pydantic-settings==2.1.0

# 數據庫
sqlalchemy==2.0.23
asyncpg==0.29.0
alembic==1.13.1

# Supabase 客戶端
supabase==2.3.0
postgrest==0.13.2

# Redis 快取
redis==5.0.1
aioredis==2.0.1

# Kafka
aiokafka==0.12.0
kafka-python==2.0.2

# HTTP 客戶端
httpx==0.24.1
aiohttp==3.9.1

# AI 服務
openai==1.3.0

# 長尾查詢識別系統 - 機器學習框架
torch>=1.9.0
transformers>=4.20.0
sentence-transformers>=2.2.0
tokenizers>=0.13.0
datasets>=2.0.0
accelerate>=0.20.0
safetensors>=0.3.0

# 長尾查詢識別系統 - NLP處理
nltk>=3.8.0
spacy>=3.6.0
jieba>=0.42.1
langdetect>=1.0.9

# 長尾查詢識別系統 - 向量搜索與嵌入
faiss-cpu>=1.7.4
chromadb>=0.4.0

# 長尾查詢識別系統 - 模型服務
onnx>=1.14.0
onnxruntime>=1.15.0

# 認證和安全
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6
cryptography==41.0.7

# 背景任務
celery==5.3.4
kombu==5.3.4

# 數據處理
pandas==2.1.4
numpy==1.25.2
beautifulsoup4==4.12.2
lxml==4.9.3

# PDF 生成
reportlab==4.0.7
weasyprint==60.2

# 監控和日誌
prometheus-client==0.19.0
structlog==23.2.0
sentry-sdk[fastapi]==1.38.0

# 開發工具
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
black==23.11.0
isort==5.12.0
flake8==6.1.0
mypy==1.7.1

# 環境配置
python-dotenv==1.0.0

# CORS
fastapi-cors==0.0.6

# 限流
slowapi==0.1.9

# 健康檢查
fastapi-health==0.4.0

# OpenAPI 文檔增強
fastapi-users==12.1.2

# Elasticsearch 搜索引擎
elasticsearch==8.11.1
elasticsearch-async==6.2.0
