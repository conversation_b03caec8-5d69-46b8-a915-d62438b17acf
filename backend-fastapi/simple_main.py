"""
簡化的FastAPI主應用程式 - 用於長尾查詢識別系統測試
"""

import logging
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from app.api.v1.endpoints.longtail_simple import router as longtail_router

# 設置日誌
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_app() -> FastAPI:
    """創建簡化的FastAPI應用程式實例"""
    
    app = FastAPI(
        title="AI SEO 優化王 - 長尾查詢識別系統",
        version="1.0.0",
        description="長尾查詢識別和分析系統",
        docs_url="/docs",
        redoc_url="/redoc",
    )
    
    # 設置 CORS
    app.add_middleware(
        CORSMiddleware,
        allow_origins=[
            "http://localhost:3000",
            "http://localhost:3001", 
            "http://localhost:3002",
            "http://127.0.0.1:3000",
            "http://127.0.0.1:3001",
        ],
        allow_credentials=True,
        allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
        allow_headers=["*"],
    )
    
    # 包含長尾查詢路由
    app.include_router(longtail_router, prefix="/api/v1/longtail", tags=["長尾查詢識別"])
    
    # 健康檢查端點
    @app.get("/health")
    async def health_check():
        """健康檢查端點"""
        return {
            "status": "healthy",
            "service": "長尾查詢識別系統",
            "version": "1.0.0"
        }
    
    # 根路徑
    @app.get("/")
    async def root():
        """根路徑"""
        return {
            "message": "AI SEO 優化王 - 長尾查詢識別系統",
            "docs": "/docs",
            "health": "/health",
            "api": "/api/v1/longtail"
        }
    
    return app

# 創建應用程式實例
app = create_app()

if __name__ == "__main__":
    import uvicorn
    
    uvicorn.run(
        "simple_main:app",
        host="0.0.0.0",
        port=8001,
        reload=True,
        log_level="info",
    )
