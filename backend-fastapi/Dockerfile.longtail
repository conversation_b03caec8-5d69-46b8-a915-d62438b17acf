# 長尾查詢識別系統 - 專用Dockerfile
FROM python:3.11-slim

# 設置工作目錄
WORKDIR /app

# 安裝系統依賴
RUN apt-get update && apt-get install -y \
    build-essential \
    curl \
    git \
    wget \
    unzip \
    && rm -rf /var/lib/apt/lists/*

# 安裝Python依賴
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# 安裝額外的ML依賴
RUN pip install --no-cache-dir \
    torch>=1.9.0 \
    transformers>=4.20.0 \
    sentence-transformers>=2.2.0 \
    tokenizers>=0.13.0 \
    datasets>=2.0.0 \
    accelerate>=0.20.0 \
    safetensors>=0.3.0 \
    nltk>=3.8.0 \
    spacy>=3.6.0 \
    jieba>=0.42.1 \
    langdetect>=1.0.9 \
    faiss-cpu>=1.7.4 \
    onnx>=1.14.0 \
    onnxruntime>=1.15.0 \
    scikit-learn>=1.3.0

# 下載spaCy模型
RUN python -m spacy download zh_core_web_sm
RUN python -m spacy download en_core_web_sm

# 複製應用代碼
COPY . .

# 創建必要的目錄
RUN mkdir -p /app/models /app/logs /app/data

# 下載預訓練模型
RUN python download_models.py

# 設置環境變量
ENV PYTHONPATH=/app
ENV PYTHONUNBUFFERED=1
ENV MODEL_CACHE_DIR=/app/models
ENV LOG_LEVEL=INFO

# 創建非root用戶
RUN useradd --create-home --shell /bin/bash longtail
RUN chown -R longtail:longtail /app
USER longtail

# 健康檢查
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# 暴露端口
EXPOSE 8000

# 啟動命令
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000", "--workers", "4"]
