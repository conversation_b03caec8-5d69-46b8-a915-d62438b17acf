"""
長尾查詢識別系統 - Elasticsearch配置
定義索引映射和搜索配置
"""

from typing import Dict, Any

# 長尾查詢索引映射
LONGTAIL_QUERIES_INDEX_MAPPING = {
    "settings": {
        "number_of_shards": 2,
        "number_of_replicas": 1,
        "analysis": {
            "analyzer": {
                "chinese_analyzer": {
                    "type": "custom",
                    "tokenizer": "ik_max_word",
                    "filter": ["lowercase", "stop"]
                },
                "english_analyzer": {
                    "type": "custom", 
                    "tokenizer": "standard",
                    "filter": ["lowercase", "stop", "stemmer"]
                },
                "keyword_analyzer": {
                    "type": "custom",
                    "tokenizer": "keyword",
                    "filter": ["lowercase"]
                }
            },
            "tokenizer": {
                "ik_max_word": {
                    "type": "ik_max_word"
                }
            }
        }
    },
    "mappings": {
        "properties": {
            "id": {
                "type": "keyword"
            },
            "query_text": {
                "type": "text",
                "analyzer": "chinese_analyzer",
                "fields": {
                    "english": {
                        "type": "text",
                        "analyzer": "english_analyzer"
                    },
                    "keyword": {
                        "type": "keyword"
                    },
                    "raw": {
                        "type": "text",
                        "analyzer": "keyword_analyzer"
                    }
                }
            },
            "query_hash": {
                "type": "keyword"
            },
            "query_type": {
                "type": "keyword"
            },
            "intent": {
                "type": "keyword"
            },
            "complexity": {
                "type": "keyword"
            },
            "length": {
                "type": "integer"
            },
            "word_count": {
                "type": "integer"
            },
            "character_count": {
                "type": "integer"
            },
            "language": {
                "type": "keyword"
            },
            "longtail_score": {
                "type": "float"
            },
            "confidence": {
                "type": "float"
            },
            "specificity_score": {
                "type": "float"
            },
            "commercial_intent_score": {
                "type": "float"
            },
            "has_question_words": {
                "type": "boolean"
            },
            "has_brand_names": {
                "type": "boolean"
            },
            "has_location": {
                "type": "boolean"
            },
            "has_numbers": {
                "type": "boolean"
            },
            "has_special_chars": {
                "type": "boolean"
            },
            "keywords": {
                "type": "text",
                "analyzer": "chinese_analyzer",
                "fields": {
                    "keyword": {
                        "type": "keyword"
                    }
                }
            },
            "semantic_category": {
                "type": "keyword"
            },
            "search_volume_estimate": {
                "type": "integer"
            },
            "competition_level": {
                "type": "keyword"
            },
            "created_at": {
                "type": "date"
            },
            "updated_at": {
                "type": "date"
            },
            "analyzed_by": {
                "type": "keyword"
            },
            # 向量嵌入字段
            "query_embedding": {
                "type": "dense_vector",
                "dims": 768  # BERT base模型維度
            },
            "semantic_embedding": {
                "type": "dense_vector", 
                "dims": 384  # Sentence-BERT維度
            }
        }
    }
}

# 查詢關鍵詞索引映射
QUERY_KEYWORDS_INDEX_MAPPING = {
    "settings": {
        "number_of_shards": 1,
        "number_of_replicas": 1,
        "analysis": {
            "analyzer": {
                "keyword_analyzer": {
                    "type": "custom",
                    "tokenizer": "keyword",
                    "filter": ["lowercase"]
                }
            }
        }
    },
    "mappings": {
        "properties": {
            "id": {
                "type": "keyword"
            },
            "query_id": {
                "type": "keyword"
            },
            "keyword": {
                "type": "text",
                "analyzer": "keyword_analyzer",
                "fields": {
                    "raw": {
                        "type": "keyword"
                    }
                }
            },
            "relevance_score": {
                "type": "float"
            },
            "frequency": {
                "type": "integer"
            },
            "created_at": {
                "type": "date"
            }
        }
    }
}

# 查詢相似度索引映射
QUERY_SIMILARITIES_INDEX_MAPPING = {
    "settings": {
        "number_of_shards": 1,
        "number_of_replicas": 1
    },
    "mappings": {
        "properties": {
            "id": {
                "type": "keyword"
            },
            "query1_id": {
                "type": "keyword"
            },
            "query2_id": {
                "type": "keyword"
            },
            "query1_text": {
                "type": "text",
                "analyzer": "chinese_analyzer"
            },
            "query2_text": {
                "type": "text", 
                "analyzer": "chinese_analyzer"
            },
            "similarity_score": {
                "type": "float"
            },
            "similarity_type": {
                "type": "keyword"
            },
            "created_at": {
                "type": "date"
            }
        }
    }
}

# 搜索查詢模板
SEARCH_TEMPLATES = {
    "longtail_search": {
        "template": {
            "query": {
                "bool": {
                    "must": [
                        {
                            "multi_match": {
                                "query": "{{query_text}}",
                                "fields": [
                                    "query_text^2",
                                    "query_text.english",
                                    "keywords^1.5"
                                ],
                                "type": "best_fields",
                                "fuzziness": "AUTO"
                            }
                        }
                    ],
                    "filter": [
                        {
                            "range": {
                                "longtail_score": {
                                    "gte": "{{min_score|0.0}}",
                                    "lte": "{{max_score|1.0}}"
                                }
                            }
                        }
                    ]
                }
            },
            "sort": [
                {
                    "longtail_score": {
                        "order": "desc"
                    }
                },
                {
                    "_score": {
                        "order": "desc"
                    }
                }
            ],
            "size": "{{size|50}}",
            "from": "{{from|0}}"
        }
    },
    
    "semantic_search": {
        "template": {
            "query": {
                "script_score": {
                    "query": {
                        "bool": {
                            "filter": [
                                {
                                    "term": {
                                        "query_type": "{{query_type|long_tail}}"
                                    }
                                }
                            ]
                        }
                    },
                    "script": {
                        "source": "cosineSimilarity(params.query_vector, 'semantic_embedding') + 1.0",
                        "params": {
                            "query_vector": "{{query_embedding}}"
                        }
                    }
                }
            },
            "size": "{{size|10}}",
            "from": "{{from|0}}"
        }
    },
    
    "aggregation_stats": {
        "template": {
            "query": {
                "match_all": {}
            },
            "aggs": {
                "query_types": {
                    "terms": {
                        "field": "query_type",
                        "size": 10
                    }
                },
                "intents": {
                    "terms": {
                        "field": "intent",
                        "size": 10
                    }
                },
                "complexities": {
                    "terms": {
                        "field": "complexity",
                        "size": 10
                    }
                },
                "categories": {
                    "terms": {
                        "field": "semantic_category",
                        "size": 20
                    }
                },
                "languages": {
                    "terms": {
                        "field": "language",
                        "size": 10
                    }
                },
                "longtail_score_stats": {
                    "stats": {
                        "field": "longtail_score"
                    }
                },
                "confidence_stats": {
                    "stats": {
                        "field": "confidence"
                    }
                },
                "longtail_score_histogram": {
                    "histogram": {
                        "field": "longtail_score",
                        "interval": 0.1
                    }
                }
            },
            "size": 0
        }
    }
}

# 索引配置
ELASTICSEARCH_INDICES = {
    "longtail_queries": {
        "index": "longtail_queries",
        "mapping": LONGTAIL_QUERIES_INDEX_MAPPING,
        "aliases": ["queries", "longtail"]
    },
    "query_keywords": {
        "index": "query_keywords", 
        "mapping": QUERY_KEYWORDS_INDEX_MAPPING,
        "aliases": ["keywords"]
    },
    "query_similarities": {
        "index": "query_similarities",
        "mapping": QUERY_SIMILARITIES_INDEX_MAPPING,
        "aliases": ["similarities"]
    }
}

def get_index_config(index_name: str) -> Dict[str, Any]:
    """獲取索引配置"""
    return ELASTICSEARCH_INDICES.get(index_name, {})

def get_search_template(template_name: str) -> Dict[str, Any]:
    """獲取搜索模板"""
    return SEARCH_TEMPLATES.get(template_name, {})
