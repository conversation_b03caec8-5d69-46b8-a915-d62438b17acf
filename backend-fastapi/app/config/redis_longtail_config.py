"""
長尾查詢識別系統 - Redis緩存配置
定義緩存策略和鍵命名規範
"""

from typing import Dict, Any, Optional
from datetime import timedelta
import json
import hashlib

# 緩存鍵前綴
CACHE_PREFIXES = {
    "query_analysis": "longtail:analysis",
    "batch_analysis": "longtail:batch",
    "query_features": "longtail:features", 
    "query_embeddings": "longtail:embeddings",
    "similar_queries": "longtail:similar",
    "query_stats": "longtail:stats",
    "model_cache": "longtail:models",
    "session_cache": "longtail:sessions"
}

# 緩存過期時間 (秒)
CACHE_TTL = {
    "query_analysis": 3600,      # 1小時 - 查詢分析結果
    "batch_analysis": 7200,      # 2小時 - 批量分析結果
    "query_features": 1800,      # 30分鐘 - 查詢特徵
    "query_embeddings": 86400,   # 24小時 - 查詢嵌入向量
    "similar_queries": 3600,     # 1小時 - 相似查詢
    "query_stats": 1800,         # 30分鐘 - 統計數據
    "model_cache": 86400,        # 24小時 - 模型緩存
    "session_cache": 3600,       # 1小時 - 會話緩存
    "temp_results": 300,         # 5分鐘 - 臨時結果
    "user_preferences": 7200     # 2小時 - 用戶偏好
}

class LongTailCacheManager:
    """長尾查詢緩存管理器"""
    
    def __init__(self, redis_client):
        self.redis = redis_client
        
    def _generate_cache_key(self, prefix: str, identifier: str, **kwargs) -> str:
        """生成緩存鍵"""
        # 創建唯一標識符
        key_parts = [prefix, identifier]
        
        # 添加額外參數
        if kwargs:
            params_str = json.dumps(kwargs, sort_keys=True)
            params_hash = hashlib.md5(params_str.encode()).hexdigest()[:8]
            key_parts.append(params_hash)
        
        return ":".join(key_parts)
    
    def _get_ttl(self, cache_type: str) -> int:
        """獲取緩存過期時間"""
        return CACHE_TTL.get(cache_type, 3600)
    
    async def cache_query_analysis(
        self, 
        query: str, 
        analysis_result: Dict[str, Any],
        ttl: Optional[int] = None
    ) -> bool:
        """緩存查詢分析結果"""
        try:
            query_hash = hashlib.md5(query.encode()).hexdigest()
            cache_key = self._generate_cache_key(
                CACHE_PREFIXES["query_analysis"], 
                query_hash
            )
            
            ttl = ttl or self._get_ttl("query_analysis")
            
            await self.redis.setex(
                cache_key,
                ttl,
                json.dumps(analysis_result, default=str, ensure_ascii=False)
            )
            
            return True
        except Exception as e:
            print(f"緩存查詢分析失敗: {e}")
            return False
    
    async def get_cached_query_analysis(self, query: str) -> Optional[Dict[str, Any]]:
        """獲取緩存的查詢分析結果"""
        try:
            query_hash = hashlib.md5(query.encode()).hexdigest()
            cache_key = self._generate_cache_key(
                CACHE_PREFIXES["query_analysis"],
                query_hash
            )
            
            cached_data = await self.redis.get(cache_key)
            if cached_data:
                return json.loads(cached_data)
            
            return None
        except Exception as e:
            print(f"獲取緩存查詢分析失敗: {e}")
            return None
    
    async def cache_batch_analysis(
        self,
        session_id: str,
        batch_result: Dict[str, Any],
        ttl: Optional[int] = None
    ) -> bool:
        """緩存批量分析結果"""
        try:
            cache_key = self._generate_cache_key(
                CACHE_PREFIXES["batch_analysis"],
                session_id
            )
            
            ttl = ttl or self._get_ttl("batch_analysis")
            
            await self.redis.setex(
                cache_key,
                ttl,
                json.dumps(batch_result, default=str, ensure_ascii=False)
            )
            
            return True
        except Exception as e:
            print(f"緩存批量分析失敗: {e}")
            return False
    
    async def get_cached_batch_analysis(self, session_id: str) -> Optional[Dict[str, Any]]:
        """獲取緩存的批量分析結果"""
        try:
            cache_key = self._generate_cache_key(
                CACHE_PREFIXES["batch_analysis"],
                session_id
            )
            
            cached_data = await self.redis.get(cache_key)
            if cached_data:
                return json.loads(cached_data)
            
            return None
        except Exception as e:
            print(f"獲取緩存批量分析失敗: {e}")
            return None
    
    async def cache_query_embeddings(
        self,
        query: str,
        embeddings: Dict[str, Any],
        ttl: Optional[int] = None
    ) -> bool:
        """緩存查詢嵌入向量"""
        try:
            query_hash = hashlib.md5(query.encode()).hexdigest()
            cache_key = self._generate_cache_key(
                CACHE_PREFIXES["query_embeddings"],
                query_hash
            )
            
            ttl = ttl or self._get_ttl("query_embeddings")
            
            # 將numpy數組轉換為列表以便JSON序列化
            serializable_embeddings = {}
            for key, value in embeddings.items():
                if hasattr(value, 'tolist'):
                    serializable_embeddings[key] = value.tolist()
                else:
                    serializable_embeddings[key] = value
            
            await self.redis.setex(
                cache_key,
                ttl,
                json.dumps(serializable_embeddings, ensure_ascii=False)
            )
            
            return True
        except Exception as e:
            print(f"緩存查詢嵌入失敗: {e}")
            return False
    
    async def get_cached_query_embeddings(self, query: str) -> Optional[Dict[str, Any]]:
        """獲取緩存的查詢嵌入向量"""
        try:
            query_hash = hashlib.md5(query.encode()).hexdigest()
            cache_key = self._generate_cache_key(
                CACHE_PREFIXES["query_embeddings"],
                query_hash
            )
            
            cached_data = await self.redis.get(cache_key)
            if cached_data:
                return json.loads(cached_data)
            
            return None
        except Exception as e:
            print(f"獲取緩存查詢嵌入失敗: {e}")
            return None
    
    async def cache_similar_queries(
        self,
        query: str,
        similar_queries: List[Dict[str, Any]],
        similarity_threshold: float = 0.7,
        ttl: Optional[int] = None
    ) -> bool:
        """緩存相似查詢結果"""
        try:
            query_hash = hashlib.md5(query.encode()).hexdigest()
            cache_key = self._generate_cache_key(
                CACHE_PREFIXES["similar_queries"],
                query_hash,
                threshold=similarity_threshold
            )
            
            ttl = ttl or self._get_ttl("similar_queries")
            
            await self.redis.setex(
                cache_key,
                ttl,
                json.dumps(similar_queries, default=str, ensure_ascii=False)
            )
            
            return True
        except Exception as e:
            print(f"緩存相似查詢失敗: {e}")
            return False
    
    async def get_cached_similar_queries(
        self,
        query: str,
        similarity_threshold: float = 0.7
    ) -> Optional[List[Dict[str, Any]]]:
        """獲取緩存的相似查詢結果"""
        try:
            query_hash = hashlib.md5(query.encode()).hexdigest()
            cache_key = self._generate_cache_key(
                CACHE_PREFIXES["similar_queries"],
                query_hash,
                threshold=similarity_threshold
            )
            
            cached_data = await self.redis.get(cache_key)
            if cached_data:
                return json.loads(cached_data)
            
            return None
        except Exception as e:
            print(f"獲取緩存相似查詢失敗: {e}")
            return None
    
    async def cache_query_stats(
        self,
        stats_type: str,
        stats_data: Dict[str, Any],
        ttl: Optional[int] = None
    ) -> bool:
        """緩存查詢統計數據"""
        try:
            cache_key = self._generate_cache_key(
                CACHE_PREFIXES["query_stats"],
                stats_type
            )
            
            ttl = ttl or self._get_ttl("query_stats")
            
            await self.redis.setex(
                cache_key,
                ttl,
                json.dumps(stats_data, default=str, ensure_ascii=False)
            )
            
            return True
        except Exception as e:
            print(f"緩存查詢統計失敗: {e}")
            return False
    
    async def get_cached_query_stats(self, stats_type: str) -> Optional[Dict[str, Any]]:
        """獲取緩存的查詢統計數據"""
        try:
            cache_key = self._generate_cache_key(
                CACHE_PREFIXES["query_stats"],
                stats_type
            )
            
            cached_data = await self.redis.get(cache_key)
            if cached_data:
                return json.loads(cached_data)
            
            return None
        except Exception as e:
            print(f"獲取緩存查詢統計失敗: {e}")
            return None
    
    async def invalidate_cache(self, pattern: str) -> int:
        """清除匹配模式的緩存"""
        try:
            keys = await self.redis.keys(pattern)
            if keys:
                return await self.redis.delete(*keys)
            return 0
        except Exception as e:
            print(f"清除緩存失敗: {e}")
            return 0
    
    async def get_cache_info(self) -> Dict[str, Any]:
        """獲取緩存信息"""
        try:
            info = {}
            for cache_type, prefix in CACHE_PREFIXES.items():
                pattern = f"{prefix}:*"
                keys = await self.redis.keys(pattern)
                info[cache_type] = {
                    "count": len(keys),
                    "prefix": prefix,
                    "ttl": self._get_ttl(cache_type)
                }
            
            return info
        except Exception as e:
            print(f"獲取緩存信息失敗: {e}")
            return {}
