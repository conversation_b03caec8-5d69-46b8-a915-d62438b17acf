"""
長尾查詢識別系統 - 數據模型
定義PostgreSQL數據表結構和Pydantic模型
"""

from sqlalchemy import Column, String, Integer, Float, Boolean, DateTime, Text, JSON, Index
from sqlalchemy.dialects.postgresql import UUID, ARRAY
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.sql import func
from pydantic import BaseModel, Field
from typing import List, Dict, Any, Optional
from datetime import datetime
from enum import Enum
import uuid

Base = declarative_base()

class QueryTypeEnum(str, Enum):
    HEAD = "head"
    MIDDLE = "middle"
    LONG_TAIL = "long_tail"

class QueryIntentEnum(str, Enum):
    INFORMATIONAL = "informational"
    NAVIGATIONAL = "navigational"
    TRANSACTIONAL = "transactional"
    COMMERCIAL = "commercial"
    LOCAL = "local"

class QueryComplexityEnum(str, Enum):
    SIMPLE = "simple"
    MODERATE = "moderate"
    COMPLEX = "complex"

# SQLAlchemy 模型
class LongTailQuery(Base):
    """長尾查詢表"""
    __tablename__ = "longtail_queries"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    query_text = Column(Text, nullable=False, index=True)
    query_hash = Column(String(64), nullable=False, unique=True, index=True)
    
    # 分類信息
    query_type = Column(String(20), nullable=False, index=True)
    intent = Column(String(20), nullable=False, index=True)
    complexity = Column(String(20), nullable=False)
    
    # 特徵數據
    length = Column(Integer, nullable=False)
    word_count = Column(Integer, nullable=False)
    character_count = Column(Integer, nullable=False)
    language = Column(String(10), nullable=False, index=True)
    
    # 分數
    longtail_score = Column(Float, nullable=False, index=True)
    confidence = Column(Float, nullable=False)
    specificity_score = Column(Float, nullable=False)
    commercial_intent_score = Column(Float, nullable=False)
    
    # 特徵標記
    has_question_words = Column(Boolean, default=False)
    has_brand_names = Column(Boolean, default=False)
    has_location = Column(Boolean, default=False)
    has_numbers = Column(Boolean, default=False)
    has_special_chars = Column(Boolean, default=False)
    
    # 分析結果
    keywords = Column(ARRAY(String), nullable=True)
    semantic_category = Column(String(50), nullable=False, index=True)
    search_volume_estimate = Column(Integer, nullable=False)
    competition_level = Column(String(20), nullable=False)
    
    # 元數據
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    analyzed_by = Column(String(100), nullable=True)
    
    # 索引
    __table_args__ = (
        Index('idx_longtail_score_type', 'longtail_score', 'query_type'),
        Index('idx_intent_category', 'intent', 'semantic_category'),
        Index('idx_created_at', 'created_at'),
    )

class QueryAnalysisSession(Base):
    """查詢分析會話表"""
    __tablename__ = "query_analysis_sessions"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    session_name = Column(String(200), nullable=False)
    description = Column(Text, nullable=True)
    
    # 統計信息
    total_queries = Column(Integer, default=0)
    longtail_queries = Column(Integer, default=0)
    longtail_percentage = Column(Float, default=0.0)
    
    # 分析結果摘要
    analysis_summary = Column(JSON, nullable=True)
    
    # 狀態
    status = Column(String(20), default='pending')  # pending, processing, completed, failed
    
    # 時間戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    completed_at = Column(DateTime(timezone=True), nullable=True)
    
    # 索引
    __table_args__ = (
        Index('idx_session_status', 'status'),
        Index('idx_session_created', 'created_at'),
    )

class QueryKeywordMapping(Base):
    """查詢關鍵詞映射表"""
    __tablename__ = "query_keyword_mappings"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    query_id = Column(UUID(as_uuid=True), nullable=False, index=True)
    keyword = Column(String(100), nullable=False, index=True)
    relevance_score = Column(Float, nullable=False)
    
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # 索引
    __table_args__ = (
        Index('idx_keyword_relevance', 'keyword', 'relevance_score'),
    )

class QuerySimilarity(Base):
    """查詢相似度表"""
    __tablename__ = "query_similarities"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    query1_id = Column(UUID(as_uuid=True), nullable=False, index=True)
    query2_id = Column(UUID(as_uuid=True), nullable=False, index=True)
    similarity_score = Column(Float, nullable=False)
    similarity_type = Column(String(20), nullable=False)  # semantic, lexical, intent
    
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # 索引
    __table_args__ = (
        Index('idx_similarity_score', 'similarity_score'),
        Index('idx_similarity_type', 'similarity_type'),
    )

# Pydantic 模型
class QueryFeaturesModel(BaseModel):
    """查詢特徵模型"""
    length: int
    word_count: int
    character_count: int
    has_question_words: bool
    has_brand_names: bool
    has_location: bool
    has_numbers: bool
    has_special_chars: bool
    language: str
    specificity_score: float
    commercial_intent_score: float

class LongTailAnalysisResultModel(BaseModel):
    """長尾查詢分析結果模型"""
    query: str
    query_type: QueryTypeEnum
    intent: QueryIntentEnum
    complexity: QueryComplexityEnum
    features: QueryFeaturesModel
    longtail_score: float = Field(..., ge=0.0, le=1.0)
    confidence: float = Field(..., ge=0.0, le=1.0)
    keywords: List[str]
    semantic_category: str
    search_volume_estimate: int = Field(..., ge=0)
    competition_level: str
    optimization_suggestions: List[str]
    timestamp: datetime

class BatchAnalysisRequest(BaseModel):
    """批量分析請求模型"""
    queries: List[str] = Field(..., min_items=1, max_items=1000)
    session_name: Optional[str] = None
    description: Optional[str] = None
    include_similarities: bool = False
    similarity_threshold: float = Field(0.8, ge=0.0, le=1.0)

class BatchAnalysisResponse(BaseModel):
    """批量分析響應模型"""
    session_id: str
    total_queries: int
    processed_queries: int
    longtail_queries: int
    longtail_percentage: float
    results: List[LongTailAnalysisResultModel]
    summary: Dict[str, Any]
    recommendations: List[str]

class QueryAnalysisRequest(BaseModel):
    """單個查詢分析請求模型"""
    query: str = Field(..., min_length=1, max_length=500)
    include_keywords: bool = True
    include_suggestions: bool = True

class QuerySearchRequest(BaseModel):
    """查詢搜索請求模型"""
    query_text: Optional[str] = None
    query_type: Optional[QueryTypeEnum] = None
    intent: Optional[QueryIntentEnum] = None
    semantic_category: Optional[str] = None
    min_longtail_score: Optional[float] = Field(None, ge=0.0, le=1.0)
    max_longtail_score: Optional[float] = Field(None, ge=0.0, le=1.0)
    language: Optional[str] = None
    limit: int = Field(50, ge=1, le=1000)
    offset: int = Field(0, ge=0)

class QuerySearchResponse(BaseModel):
    """查詢搜索響應模型"""
    total: int
    queries: List[LongTailAnalysisResultModel]
    aggregations: Dict[str, Any]

class QueryStatsResponse(BaseModel):
    """查詢統計響應模型"""
    total_queries: int
    longtail_queries: int
    longtail_percentage: float
    intent_distribution: Dict[str, int]
    complexity_distribution: Dict[str, int]
    category_distribution: Dict[str, int]
    language_distribution: Dict[str, int]
    avg_longtail_score: float
    avg_confidence: float

class SimilarQueriesRequest(BaseModel):
    """相似查詢請求模型"""
    query: str = Field(..., min_length=1, max_length=500)
    similarity_threshold: float = Field(0.7, ge=0.0, le=1.0)
    limit: int = Field(10, ge=1, le=100)

class SimilarQueriesResponse(BaseModel):
    """相似查詢響應模型"""
    query: str
    similar_queries: List[Dict[str, Any]]
    total_found: int
