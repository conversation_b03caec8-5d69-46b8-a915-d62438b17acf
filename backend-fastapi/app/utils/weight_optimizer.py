import asyncio
import random
import json
import logging
from typing import List, Dict, Any, Tuple
from datetime import datetime
from pathlib import Path

# 設置日誌記錄
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class CommercialWeightOptimizer:
    """
    商業價值權重優化工具，通過 A/B 測試尋找最佳權重值
    """
    def __init__(self, model_trainer_instance, test_queries=None):
        self.model_trainer = model_trainer_instance
        self.test_queries = test_queries or []
        self.results_dir = Path("./data/weight_optimization")
        self.results_dir.mkdir(exist_ok=True, parents=True)
        self.baseline_accuracy = 0.0
        self.best_weight = 1.0
        self.weight_performances = {}
        
    async def load_test_data(self, file_path=None):
        """載入測試數據"""
        if file_path and Path(file_path).exists():
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                self.test_queries = data.get('queries', [])
                logger.info(f"已載入 {len(self.test_queries)} 個測試查詢")
                return True
            except Exception as e:
                logger.error(f"載入測試數據失敗: {e}")
                return False
        else:
            # 使用默認測試數據
            self.test_queries = self._generate_test_queries()
            logger.info(f"使用生成的 {len(self.test_queries)} 個測試查詢")
            return True
            
    def _generate_test_queries(self) -> List[Dict[str, Any]]:
        """生成用於測試的查詢和標準答案"""
        # 預定義的測試查詢和預期結果
        test_data = [
            {"query": "哪裡買Apple MacBook Pro最便宜", "expected_intent": "purchase_intent", "is_commercial": True},
            {"query": "iPhone和三星手機比較", "expected_intent": "comparison_intent", "is_commercial": True},
            {"query": "推薦台北最好的SEO公司", "expected_intent": "service_inquiry", "is_commercial": True},
            {"query": "如何在蝦皮開店並增加曝光率", "expected_intent": "ecommerce", "is_commercial": True},
            {"query": "提高網站轉換率的方法", "expected_intent": "conversion_intent", "is_commercial": True},
            {"query": "如何優化網站的robots.txt檔案", "expected_intent": "technical_seo", "is_commercial": True},
            {"query": "如何優化Google商家檔案", "expected_intent": "local_seo", "is_commercial": True},
            {"query": "撰寫吸引人的部落格標題", "expected_intent": "content_intent", "is_commercial": True},
            {"query": "Python如何讀取JSON檔案", "expected_intent": "informational", "is_commercial": False},
            {"query": "人工智能的發展歷史", "expected_intent": "informational", "is_commercial": False},
            {"query": "台北今天天氣如何", "expected_intent": "informational", "is_commercial": False},
            {"query": "Facebook登入頁面", "expected_intent": "navigational", "is_commercial": False},
            {"query": "Google地圖下載", "expected_intent": "navigational", "is_commercial": False},
            # 模糊的案例，可能受商業權重影響的查詢
            {"query": "網站SEO優化技巧", "expected_intent": "technical_seo", "is_commercial": True, "ambiguous": True},
            {"query": "如何設計高轉換率的著陸頁", "expected_intent": "conversion_intent", "is_commercial": True, "ambiguous": True},
            {"query": "最佳WordPress插件推薦", "expected_intent": "content_intent", "is_commercial": True, "ambiguous": True},
            {"query": "社群媒體行銷策略", "expected_intent": "content_intent", "is_commercial": True, "ambiguous": True},
            {"query": "Google Analytics分析指南", "expected_intent": "technical_seo", "is_commercial": True, "ambiguous": True},
        ]
        return test_data
        
    async def run_baseline_test(self) -> float:
        """執行基準測試（無商業價值權重）"""
        if not self.test_queries:
            await self.load_test_data()
            
        queries = [item["query"] for item in self.test_queries]
        try:
            results = await self.model_trainer.predict(queries, apply_commercial_boost=False)
            
            correct_count = 0
            for i, test_item in enumerate(self.test_queries):
                prediction = results["results"][i]
                predicted_intent = prediction.get("predicted_label", "unknown")
                expected_intent = test_item.get("expected_intent", "unknown")
                
                # 檢查是否正確分類
                is_correct = predicted_intent == expected_intent
                
                # 特別處理模糊案例
                if test_item.get("ambiguous", False):
                    # 模糊案例只要分類為商業或非商業正確即可
                    predicted_is_commercial = prediction.get("is_commercial_intent", False)
                    expected_is_commercial = test_item.get("is_commercial", False)
                    is_correct = predicted_is_commercial == expected_is_commercial
                
                if is_correct:
                    correct_count += 1
                    
            accuracy = correct_count / len(self.test_queries)
            self.baseline_accuracy = accuracy
            logger.info(f"基準測試完成，無權重準確率: {accuracy:.2%}")
            return accuracy
            
        except Exception as e:
            logger.error(f"基準測試失敗: {e}")
            return 0.0
    
    async def run_weight_test(self, weight: float) -> float:
        """測試指定的商業價值權重"""
        if not self.test_queries:
            await self.load_test_data()
            
        queries = [item["query"] for item in self.test_queries]
        try:
            # 訓練帶有指定權重的模型
            await self.model_trainer.train_model(force_retrain=True, commercial_value_weight=weight)
            
            # 進行預測
            results = await self.model_trainer.predict(queries, apply_commercial_boost=True)
            
            correct_count = 0
            ambiguous_correct = 0
            ambiguous_count = 0
            
            for i, test_item in enumerate(self.test_queries):
                prediction = results["results"][i]
                predicted_intent = prediction.get("predicted_label", "unknown")
                expected_intent = test_item.get("expected_intent", "unknown")
                
                # 檢查是否正確分類
                is_correct = predicted_intent == expected_intent
                
                # 特別處理模糊案例
                if test_item.get("ambiguous", False):
                    ambiguous_count += 1
                    # 模糊案例只要分類為商業或非商業正確即可
                    predicted_is_commercial = prediction.get("is_commercial_intent", False)
                    expected_is_commercial = test_item.get("is_commercial", False)
                    is_correct = predicted_is_commercial == expected_is_commercial
                    if is_correct:
                        ambiguous_correct += 1
                
                if is_correct:
                    correct_count += 1
                    
            accuracy = correct_count / len(self.test_queries)
            ambiguous_accuracy = ambiguous_correct / ambiguous_count if ambiguous_count > 0 else 0
            
            # 記錄權重性能
            self.weight_performances[weight] = {
                "overall_accuracy": accuracy,
                "ambiguous_accuracy": ambiguous_accuracy,
                "improvement": accuracy - self.baseline_accuracy
            }
            
            logger.info(f"權重 {weight} 測試完成，準確率: {accuracy:.2%}, 模糊案例準確率: {ambiguous_accuracy:.2%}")
            return accuracy
            
        except Exception as e:
            logger.error(f"權重 {weight} 測試失敗: {e}")
            return 0.0
    
    async def find_optimal_weight(self, weight_range=(0.5, 3.0), steps=6):
        """測試一系列權重值，尋找最佳商業價值權重"""
        # 首先執行基準測試
        await self.run_baseline_test()
        
        # 生成待測試的權重值
        weights = [round(weight_range[0] + i * (weight_range[1] - weight_range[0]) / (steps - 1), 1) 
                  for i in range(steps)]
        
        # 測試每個權重值
        best_accuracy = self.baseline_accuracy
        for weight in weights:
            accuracy = await self.run_weight_test(weight)
            if accuracy > best_accuracy:
                best_accuracy = accuracy
                self.best_weight = weight
        
        # 記錄測試結果
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        result_file = self.results_dir / f"weight_test_{timestamp}.json"
        
        with open(result_file, 'w', encoding='utf-8') as f:
            json.dump({
                "baseline_accuracy": self.baseline_accuracy,
                "best_weight": self.best_weight,
                "best_accuracy": best_accuracy,
                "improvement": best_accuracy - self.baseline_accuracy,
                "weight_performances": {str(k): v for k, v in self.weight_performances.items()}
            }, f, ensure_ascii=False, indent=2)
        
        logger.info(f"優化完成，最佳權重: {self.best_weight}，準確率: {best_accuracy:.2%}")
        logger.info(f"較基準提升: {best_accuracy - self.baseline_accuracy:.2%}")
        logger.info(f"詳細結果已保存至 {result_file}")
        
        return {
            "best_weight": self.best_weight,
            "best_accuracy": best_accuracy,
            "baseline_accuracy": self.baseline_accuracy,
            "improvement": best_accuracy - self.baseline_accuracy
        }
    
    async def run_ab_test(self, weight_a: float, weight_b: float, test_size: int = 50) -> Dict[str, Any]:
        """執行 A/B 測試比較兩個不同商業權重的效果"""
        if not self.test_queries:
            await self.load_test_data()
            
        # 隨機選擇測試查詢
        if len(self.test_queries) > test_size:
            test_samples = random.sample(self.test_queries, test_size)
        else:
            test_samples = self.test_queries
            
        queries = [item["query"] for item in test_samples]
        
        # 測試權重 A
        await self.model_trainer.train_model(force_retrain=True, commercial_value_weight=weight_a)
        results_a = await self.model_trainer.predict(queries, apply_commercial_boost=True)
        
        # 測試權重 B
        await self.model_trainer.train_model(force_retrain=True, commercial_value_weight=weight_b)
        results_b = await self.model_trainer.predict(queries, apply_commercial_boost=True)
        
        # 比較結果
        correct_a = 0
        correct_b = 0
        
        for i, test_item in enumerate(test_samples):
            prediction_a = results_a["results"][i]
            prediction_b = results_b["results"][i]
            expected_intent = test_item.get("expected_intent", "unknown")
            
            # 檢查每個權重的預測是否正確
            if prediction_a.get("predicted_label", "unknown") == expected_intent:
                correct_a += 1
                
            if prediction_b.get("predicted_label", "unknown") == expected_intent:
                correct_b += 1
        
        accuracy_a = correct_a / len(test_samples)
        accuracy_b = correct_b / len(test_samples)
        
        result = {
            "weight_a": weight_a,
            "weight_b": weight_b,
            "accuracy_a": accuracy_a,
            "accuracy_b": accuracy_b,
            "winner": "A" if accuracy_a > accuracy_b else "B" if accuracy_b > accuracy_a else "Tie",
            "difference": abs(accuracy_a - accuracy_b)
        }
        
        logger.info(f"A/B 測試完成:")
        logger.info(f"權重 A ({weight_a}): {accuracy_a:.2%}")
        logger.info(f"權重 B ({weight_b}): {accuracy_b:.2%}")
        logger.info(f"獲勝者: {result['winner']}, 差異: {result['difference']:.2%}")
        
        return result
