"""
少樣本學習模型
基於原型網絡和元學習的長尾查詢分類系統
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import logging
from typing import List, Dict, Tuple, Optional, Any
import numpy as np
from transformers import AutoModel, AutoTokenizer
from pathlib import Path
import json
import pickle

logger = logging.getLogger(__name__)

class MetaLearner(nn.Module):
    """元學習器 - 基於支持集調整查詢特徵"""
    
    def __init__(self, input_dim: int, hidden_dim: int = 128):
        super().__init__()
        self.adaptation_network = nn.Sequential(
            nn.Linear(input_dim * 2, hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, input_dim)
        )
        
        # 注意力機制
        self.attention = nn.MultiheadAttention(
            embed_dim=input_dim,
            num_heads=8,
            dropout=0.1,
            batch_first=True
        )
        
    def forward(self, query_features: torch.Tensor, support_set: Tuple[torch.Tensor, torch.Tensor]):
        """基於支持集調整查詢特徵"""
        support_features, support_labels = support_set
        
        # 計算查詢與支持集的關係
        support_mean = support_features.mean(dim=0, keepdim=True)
        support_mean = support_mean.expand_as(query_features)
        
        # 注意力機制
        attended_features, _ = self.attention(
            query_features.unsqueeze(1),
            support_features.unsqueeze(0).expand(query_features.size(0), -1, -1),
            support_features.unsqueeze(0).expand(query_features.size(0), -1, -1)
        )
        attended_features = attended_features.squeeze(1)
        
        # 特徵組合
        combined = torch.cat([query_features, support_mean], dim=1)
        
        # 生成適應性調整
        adaptation = self.adaptation_network(combined)
        
        # 應用調整
        adapted_features = query_features + adaptation + attended_features
        
        return adapted_features

class FewShotLongTailModel(nn.Module):
    """少樣本長尾查詢分類模型"""
    
    def __init__(self, 
                 model_name: str = 'bert-base-multilingual-cased',
                 n_classes: int = 10,
                 hidden_dim: int = 256):
        super().__init__()
        
        # BERT編碼器
        self.bert = AutoModel.from_pretrained(model_name)
        self.tokenizer = AutoTokenizer.from_pretrained(model_name)
        
        # 獲取BERT隱藏層維度
        self.bert_dim = self.bert.config.hidden_size
        
        # 特徵處理層
        self.dropout = nn.Dropout(0.1)
        self.feature_projection = nn.Linear(self.bert_dim, hidden_dim)
        
        # 原型網絡層
        self.prototype_layer = nn.Linear(hidden_dim, hidden_dim)
        self.output_layer = nn.Linear(hidden_dim, n_classes)
        
        # 元學習參數
        self.meta_learner = MetaLearner(input_dim=hidden_dim, hidden_dim=128)
        
        # 原型存儲
        self.prototypes = {}
        
    def encode_text(self, texts: List[str], max_length: int = 512) -> torch.Tensor:
        """編碼文本"""
        # 分詞
        encoded = self.tokenizer(
            texts,
            padding=True,
            truncation=True,
            max_length=max_length,
            return_tensors='pt'
        )
        
        # BERT編碼
        with torch.no_grad():
            outputs = self.bert(**encoded)
            # 使用[CLS]標記的表示
            features = outputs.last_hidden_state[:, 0, :]
        
        return features
    
    def forward(self, 
                input_ids: torch.Tensor, 
                attention_mask: torch.Tensor, 
                support_set: Optional[Tuple[torch.Tensor, torch.Tensor]] = None):
        """前向傳播"""
        
        # BERT編碼
        outputs = self.bert(input_ids=input_ids, attention_mask=attention_mask)
        pooled_output = outputs.pooler_output
        
        # 特徵投影
        features = self.feature_projection(self.dropout(pooled_output))
        
        # 如果提供了支持集，使用元學習
        if support_set is not None:
            adapted_features = self.meta_learner(features, support_set)
            features = adapted_features
        
        # 原型表示
        prototype_features = self.prototype_layer(self.dropout(features))
        
        # 分類
        logits = self.output_layer(prototype_features)
        
        return logits, prototype_features
    
    def compute_prototypes(self, support_features: torch.Tensor, support_labels: torch.Tensor) -> Dict[int, torch.Tensor]:
        """計算每個類別的原型"""
        prototypes = {}
        unique_labels = torch.unique(support_labels)
        
        for label in unique_labels:
            mask = support_labels == label
            class_features = support_features[mask]
            if len(class_features) > 0:
                prototype = class_features.mean(dim=0)
                prototypes[label.item()] = prototype
        
        return prototypes
    
    def few_shot_predict(self, 
                        query_features: torch.Tensor, 
                        prototypes: Dict[int, torch.Tensor],
                        distance_metric: str = 'euclidean') -> Tuple[torch.Tensor, torch.Tensor]:
        """基於原型的少樣本預測"""
        if not prototypes:
            raise ValueError("沒有可用的原型")
        
        distances = []
        labels = []
        
        for label, prototype in prototypes.items():
            if distance_metric == 'euclidean':
                dist = torch.norm(query_features - prototype.unsqueeze(0), dim=1)
            elif distance_metric == 'cosine':
                # 餘弦距離
                query_norm = F.normalize(query_features, p=2, dim=1)
                proto_norm = F.normalize(prototype.unsqueeze(0), p=2, dim=1)
                dist = 1 - torch.sum(query_norm * proto_norm, dim=1)
            else:
                # 默認使用歐幾里得距離
                dist = torch.norm(query_features - prototype.unsqueeze(0), dim=1)
            
            distances.append(dist)
            labels.append(label)
        
        # 堆疊距離
        distances = torch.stack(distances, dim=1)
        
        # 預測（距離最小的類別）
        predictions = torch.argmin(distances, dim=1)
        confidences = F.softmax(-distances, dim=1)
        
        # 轉換為實際標籤
        predicted_labels = torch.tensor([labels[p] for p in predictions])
        
        return predicted_labels, confidences
    
    def update_prototypes(self, new_features: torch.Tensor, new_labels: torch.Tensor):
        """更新原型（增量學習）"""
        new_prototypes = self.compute_prototypes(new_features, new_labels)
        
        for label, new_prototype in new_prototypes.items():
            if label in self.prototypes:
                # 使用移動平均更新現有原型
                alpha = 0.1  # 學習率
                self.prototypes[label] = (1 - alpha) * self.prototypes[label] + alpha * new_prototype
            else:
                # 添加新原型
                self.prototypes[label] = new_prototype
    
    def save_prototypes(self, path: str):
        """保存原型"""
        try:
            # 轉換為可序列化格式
            prototypes_data = {
                str(label): prototype.cpu().numpy().tolist()
                for label, prototype in self.prototypes.items()
            }
            
            with open(path, 'w', encoding='utf-8') as f:
                json.dump(prototypes_data, f, ensure_ascii=False, indent=2)
            
            logger.info(f"原型已保存到: {path}")
            
        except Exception as e:
            logger.error(f"保存原型失敗: {e}")
            raise
    
    def load_prototypes(self, path: str):
        """載入原型"""
        try:
            with open(path, 'r', encoding='utf-8') as f:
                prototypes_data = json.load(f)
            
            # 轉換回張量格式
            self.prototypes = {
                int(label): torch.tensor(prototype, dtype=torch.float32)
                for label, prototype in prototypes_data.items()
            }
            
            logger.info(f"原型已載入: {len(self.prototypes)} 個類別")
            
        except Exception as e:
            logger.error(f"載入原型失敗: {e}")
            raise

class FewShotTrainer:
    """少樣本學習訓練器"""
    
    def __init__(self, model: FewShotLongTailModel, device: str = 'cpu'):
        self.model = model
        self.device = device
        self.model.to(device)
        
    def train_episode(self, 
                     support_texts: List[str],
                     support_labels: List[int],
                     query_texts: List[str],
                     query_labels: List[int],
                     n_support: int = 5) -> Dict[str, float]:
        """訓練一個episode"""
        
        self.model.train()
        
        # 編碼支持集和查詢集
        support_features = self.model.encode_text(support_texts)
        query_features = self.model.encode_text(query_texts)
        
        support_labels_tensor = torch.tensor(support_labels, dtype=torch.long)
        query_labels_tensor = torch.tensor(query_labels, dtype=torch.long)
        
        # 移動到設備
        support_features = support_features.to(self.device)
        query_features = query_features.to(self.device)
        support_labels_tensor = support_labels_tensor.to(self.device)
        query_labels_tensor = query_labels_tensor.to(self.device)
        
        # 計算原型
        prototypes = self.model.compute_prototypes(support_features, support_labels_tensor)
        
        # 預測
        predictions, confidences = self.model.few_shot_predict(query_features, prototypes)
        
        # 計算準確率
        accuracy = (predictions == query_labels_tensor).float().mean().item()
        
        # 計算損失（交叉熵）
        loss = F.cross_entropy(confidences, query_labels_tensor)
        
        return {
            'loss': loss.item(),
            'accuracy': accuracy,
            'predictions': predictions.cpu().numpy(),
            'true_labels': query_labels_tensor.cpu().numpy()
        }
    
    def evaluate(self, test_episodes: List[Dict]) -> Dict[str, float]:
        """評估模型"""
        self.model.eval()
        
        total_accuracy = 0
        total_loss = 0
        num_episodes = len(test_episodes)
        
        with torch.no_grad():
            for episode in test_episodes:
                result = self.train_episode(
                    episode['support_texts'],
                    episode['support_labels'],
                    episode['query_texts'],
                    episode['query_labels']
                )
                total_accuracy += result['accuracy']
                total_loss += result['loss']
        
        return {
            'avg_accuracy': total_accuracy / num_episodes,
            'avg_loss': total_loss / num_episodes
        }
