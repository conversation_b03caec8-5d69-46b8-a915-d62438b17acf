"""
增強長尾查詢分析器
集成向量搜索、概念擴展和少樣本學習的高級分析系統
"""

import logging
from typing import List, Dict, Optional, Tuple, Any
from datetime import datetime
import asyncio
import numpy as np
import torch

# 項目依賴
from .enhanced_matcher import EnhancedLongTailMatcher
from .few_shot_learning import FewShotLongTailModel, FewShotTrainer
from ..api.v1.endpoints.longtail_simple import SimpleLongTailAnalyzer
from ..models.longtail_models import LongTailAnalysisResultModel, QueryFeaturesModel

logger = logging.getLogger(__name__)

class EnhancedLongTailAnalyzer:
    """增強長尾查詢分析器"""
    
    def __init__(self):
        # 基礎分析器
        self.base_analyzer = SimpleLongTailAnalyzer()
        
        # 增強匹配器
        self.matcher = EnhancedLongTailMatcher()
        
        # 少樣本學習模型
        self.few_shot_model = None
        
        # 初始化標記
        self.initialized = False
        
        # 配置
        self.similarity_threshold = 0.7
        self.max_similar_queries = 5
        
    async def initialize(self):
        """異步初始化所有組件"""
        if self.initialized:
            return
            
        try:
            logger.info("開始初始化增強長尾查詢分析器...")
            
            # 初始化匹配器
            await self.matcher.initialize()
            
            # 初始化少樣本學習模型
            try:
                self.few_shot_model = FewShotLongTailModel()
                logger.info("少樣本學習模型初始化完成")
            except Exception as e:
                logger.warning(f"少樣本學習模型初始化失敗，將使用基礎分析: {e}")
                self.few_shot_model = None
            
            self.initialized = True
            logger.info("增強長尾查詢分析器初始化完成")
            
        except Exception as e:
            logger.error(f"增強分析器初始化失敗: {e}")
            raise
    
    async def analyze_query_enhanced(self, 
                                   query: str, 
                                   include_similar: bool = True,
                                   include_suggestions: bool = True) -> Dict[str, Any]:
        """增強查詢分析"""
        if not self.initialized:
            await self.initialize()
        
        try:
            # 1. 基礎分析
            base_result = self.base_analyzer.analyze_query(query)
            
            # 2. 相似查詢搜索
            similar_queries = []
            if include_similar:
                similar_queries = await self.matcher.get_similar_queries(
                    query, k=self.max_similar_queries
                )
            
            # 3. 少樣本學習增強（如果可用）
            enhanced_predictions = None
            if self.few_shot_model and similar_queries:
                enhanced_predictions = await self._apply_few_shot_learning(
                    query, similar_queries
                )
            
            # 4. 概念擴展分析
            expanded_concepts = self.matcher.concept_expander.extract_concepts(query)
            related_concepts = []
            for concept in expanded_concepts:
                related = self.matcher.concept_expander.get_related_concepts(concept)
                related_concepts.extend(related[:2])  # 取前2個相關概念
            
            # 5. 組合結果
            enhanced_result = {
                # 基礎分析結果
                'query': base_result.query,
                'query_type': base_result.query_type,
                'intent': base_result.intent,
                'complexity': base_result.complexity,
                'features': base_result.features.dict(),
                'longtail_score': base_result.longtail_score,
                'confidence': base_result.confidence,
                'keywords': base_result.keywords,
                'semantic_category': base_result.semantic_category,
                'search_volume_estimate': base_result.search_volume_estimate,
                'competition_level': base_result.competition_level,
                'optimization_suggestions': base_result.optimization_suggestions,
                'timestamp': base_result.timestamp,
                
                # 增強功能
                'similar_queries': similar_queries,
                'related_concepts': related_concepts,
                'enhanced_predictions': enhanced_predictions,
                'analysis_method': 'enhanced'
            }
            
            # 6. 調整置信度（基於相似查詢）
            if similar_queries:
                enhanced_result['confidence'] = self._adjust_confidence(
                    base_result.confidence, similar_queries
                )
            
            # 7. 增強優化建議
            if include_suggestions:
                enhanced_result['optimization_suggestions'] = self._generate_enhanced_suggestions(
                    enhanced_result
                )
            
            return enhanced_result
            
        except Exception as e:
            logger.error(f"增強查詢分析失敗: {query}, 錯誤: {e}")
            # 降級到基礎分析
            base_result = self.base_analyzer.analyze_query(query)
            return {
                **base_result.dict(),
                'similar_queries': [],
                'related_concepts': [],
                'enhanced_predictions': None,
                'analysis_method': 'fallback'
            }
    
    async def _apply_few_shot_learning(self, 
                                     query: str, 
                                     similar_queries: List[Dict]) -> Optional[Dict]:
        """應用少樣本學習"""
        try:
            if not self.few_shot_model or len(similar_queries) < 3:
                return None
            
            # 準備支持集
            support_texts = [sq['query'] for sq in similar_queries]
            support_labels = [self._map_query_type_to_label(sq['query_type']) for sq in similar_queries]
            
            # 編碼查詢
            query_features = self.few_shot_model.encode_text([query])
            
            # 編碼支持集
            support_features = self.few_shot_model.encode_text(support_texts)
            support_labels_tensor = torch.tensor(support_labels, dtype=torch.long)
            
            # 計算原型
            prototypes = self.few_shot_model.compute_prototypes(support_features, support_labels_tensor)
            
            # 預測
            predictions, confidences = self.few_shot_model.few_shot_predict(query_features, prototypes)
            
            return {
                'predicted_type': self._map_label_to_query_type(predictions[0].item()),
                'confidence': confidences[0].max().item(),
                'support_queries_count': len(similar_queries)
            }
            
        except Exception as e:
            logger.error(f"少樣本學習應用失敗: {e}")
            return None
    
    def _map_query_type_to_label(self, query_type: str) -> int:
        """將查詢類型映射到數字標籤"""
        mapping = {
            'head': 0,
            'middle': 1,
            'long_tail': 2
        }
        return mapping.get(query_type, 1)
    
    def _map_label_to_query_type(self, label: int) -> str:
        """將數字標籤映射到查詢類型"""
        mapping = {
            0: 'head',
            1: 'middle',
            2: 'long_tail'
        }
        return mapping.get(label, 'middle')
    
    def _adjust_confidence(self, base_confidence: float, similar_queries: List[Dict]) -> float:
        """基於相似查詢調整置信度"""
        if not similar_queries:
            return base_confidence
        
        # 計算相似查詢的平均分數
        avg_similarity = np.mean([sq['score'] for sq in similar_queries])
        
        # 如果相似度高，增加置信度
        if avg_similarity > self.similarity_threshold:
            boost = min(0.2, (avg_similarity - self.similarity_threshold) * 0.5)
            return min(1.0, base_confidence + boost)
        
        return base_confidence
    
    def _generate_enhanced_suggestions(self, analysis_result: Dict) -> List[str]:
        """生成增強的優化建議"""
        suggestions = list(analysis_result.get('optimization_suggestions', []))
        
        # 基於相似查詢的建議
        similar_queries = analysis_result.get('similar_queries', [])
        if similar_queries:
            # 分析相似查詢的特點
            longtail_queries = [sq for sq in similar_queries if sq.get('query_type') == 'long_tail']
            if len(longtail_queries) >= 3:
                suggestions.append("發現多個相似的長尾查詢，建議創建專門的內容頁面來覆蓋這些查詢")
            
            # 分析意圖分布
            intents = [sq.get('intent', '') for sq in similar_queries]
            if intents.count('commercial') >= 2:
                suggestions.append("相似查詢顯示商業意圖，建議優化產品頁面和購買流程")
        
        # 基於相關概念的建議
        related_concepts = analysis_result.get('related_concepts', [])
        if related_concepts:
            high_relevance_concepts = [concept for concept, score in related_concepts if score > 0.8]
            if high_relevance_concepts:
                concepts_str = ', '.join(high_relevance_concepts[:3])
                suggestions.append(f"建議在內容中包含相關概念: {concepts_str}")
        
        # 基於少樣本學習的建議
        enhanced_predictions = analysis_result.get('enhanced_predictions')
        if enhanced_predictions and enhanced_predictions['confidence'] > 0.8:
            predicted_type = enhanced_predictions['predicted_type']
            if predicted_type != analysis_result['query_type']:
                suggestions.append(f"機器學習模型建議將此查詢歸類為 {predicted_type} 類型，建議重新評估分類策略")
        
        return suggestions
    
    async def batch_analyze_enhanced(self, 
                                   queries: List[str],
                                   progress_callback: Optional[callable] = None) -> List[Dict]:
        """批量增強分析"""
        if not self.initialized:
            await self.initialize()
        
        results = []
        total = len(queries)
        
        for i, query in enumerate(queries):
            try:
                result = await self.analyze_query_enhanced(query)
                results.append(result)
                
                # 進度回調
                if progress_callback:
                    progress = (i + 1) / total
                    await progress_callback(progress, len(results))
                    
            except Exception as e:
                logger.error(f"批量分析失敗: {query}, 錯誤: {e}")
                continue
        
        return results
    
    async def update_index_with_new_query(self, query_data: Dict):
        """將新查詢添加到索引中"""
        try:
            await self.matcher.add_document(query_data)
            logger.info(f"新查詢已添加到索引: {query_data.get('query_text', '')}")
        except Exception as e:
            logger.error(f"添加查詢到索引失敗: {e}")
    
    async def rebuild_index(self):
        """重建索引"""
        try:
            await self.matcher.build_index()
            logger.info("索引重建完成")
        except Exception as e:
            logger.error(f"索引重建失敗: {e}")
            raise
    
    def get_stats(self) -> Dict[str, Any]:
        """獲取分析器統計信息"""
        return {
            'initialized': self.initialized,
            'matcher_available': self.matcher is not None,
            'few_shot_model_available': self.few_shot_model is not None,
            'index_size': len(self.matcher.id_mapping) if self.matcher else 0,
            'similarity_threshold': self.similarity_threshold,
            'max_similar_queries': self.max_similar_queries
        }
