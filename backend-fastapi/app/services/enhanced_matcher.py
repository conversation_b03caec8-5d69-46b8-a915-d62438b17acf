"""
增強長尾查詢匹配系統
基於向量搜索、概念擴展和語義理解的高級匹配算法
"""

import os
import json
import pickle
import logging
from typing import List, Dict, Tuple, Optional, Any
from pathlib import Path
import numpy as np
import asyncio
from datetime import datetime

# ML 和 NLP 依賴
import torch
from sentence_transformers import SentenceTransformer
import faiss
from transformers import AutoTokenizer, AutoModel

# 項目依賴
from ..core.config import settings
from ..models.longtail_models import LongTailQuery
from ..core.database import get_db

logger = logging.getLogger(__name__)

class ConceptExpander:
    """概念擴展器 - 基於知識圖譜和詞嵌入的概念關聯"""
    
    def __init__(self, knowledge_graph_path: Optional[str] = None):
        self.knowledge_graph = {}
        self.word_embeddings = {}
        self.concept_cache = {}
        
        # 載入知識圖譜
        if knowledge_graph_path and os.path.exists(knowledge_graph_path):
            self.load_knowledge_graph(knowledge_graph_path)
        else:
            self._build_default_knowledge_graph()
    
    def load_knowledge_graph(self, path: str):
        """載入知識圖譜"""
        try:
            with open(path, 'r', encoding='utf-8') as f:
                self.knowledge_graph = json.load(f)
            logger.info(f"載入知識圖譜: {len(self.knowledge_graph)} 個概念")
        except Exception as e:
            logger.error(f"載入知識圖譜失敗: {e}")
            self._build_default_knowledge_graph()
    
    def _build_default_knowledge_graph(self):
        """構建默認知識圖譜"""
        self.knowledge_graph = {
            # SEO 相關概念
            "SEO": [("搜索引擎優化", "synonym"), ("網站優化", "related"), ("關鍵詞優化", "part_of")],
            "搜索引擎優化": [("SEO", "synonym"), ("Google優化", "related"), ("排名優化", "related")],
            "關鍵詞": [("keywords", "synonym"), ("搜索詞", "related"), ("查詢詞", "related")],
            "長尾關鍵詞": [("long tail keywords", "synonym"), ("長尾查詢", "related"), ("特定關鍵詞", "related")],
            
            # 技術相關概念
            "AI": [("人工智能", "synonym"), ("機器學習", "related"), ("深度學習", "part_of")],
            "人工智能": [("AI", "synonym"), ("智能系統", "related"), ("自動化", "related")],
            "機器學習": [("ML", "synonym"), ("算法", "related"), ("模型訓練", "part_of")],
            
            # 商業相關概念
            "電商": [("電子商務", "synonym"), ("網購", "related"), ("在線購物", "related")],
            "營銷": [("市場營銷", "synonym"), ("推廣", "related"), ("廣告", "related")],
            "轉換": [("轉化", "synonym"), ("成交", "related"), ("購買", "related")],
        }
        logger.info(f"構建默認知識圖譜: {len(self.knowledge_graph)} 個概念")
    
    def expand(self, text: str) -> str:
        """擴展文本中的概念"""
        concepts = self.extract_concepts(text)
        expanded_text = text
        
        for concept in concepts:
            related = self.get_related_concepts(concept)
            if related:
                # 添加最相關的概念
                top_related = related[0][0]
                if top_related not in expanded_text:
                    expanded_text += f" {top_related}"
        
        return expanded_text
    
    def extract_concepts(self, text: str) -> List[str]:
        """從文本中提取概念"""
        concepts = []
        text_lower = text.lower()
        
        # 檢查知識圖譜中的概念
        for concept in self.knowledge_graph.keys():
            if concept.lower() in text_lower:
                concepts.append(concept)
        
        # 簡單的詞語提取
        words = text.split()
        for word in words:
            if len(word) > 2 and word not in concepts:
                concepts.append(word)
        
        return concepts
    
    def get_related_concepts(self, concept: str) -> List[Tuple[str, float]]:
        """獲取相關概念及其相似度"""
        if concept in self.concept_cache:
            return self.concept_cache[concept]
        
        related = []
        
        # 從知識圖譜獲取
        if concept in self.knowledge_graph:
            for rel_concept, relation_type in self.knowledge_graph[concept]:
                weight = self._get_relation_weight(relation_type)
                related.append((rel_concept, weight))
        
        # 緩存結果
        self.concept_cache[concept] = related
        return related
    
    def _get_relation_weight(self, relation_type: str) -> float:
        """獲取關係類型的權重"""
        weights = {
            "synonym": 0.9,
            "related": 0.7,
            "part_of": 0.6,
            "similar": 0.5,
            "broader": 0.4,
            "narrower": 0.4
        }
        return weights.get(relation_type, 0.3)

class EnhancedLongTailMatcher:
    """增強長尾查詢匹配器"""
    
    def __init__(self, 
                 model_name: str = 'all-mpnet-base-v2',
                 index_path: Optional[str] = None,
                 knowledge_graph_path: Optional[str] = None):
        
        # 初始化組件
        self.encoder = None
        self.concept_expander = ConceptExpander(knowledge_graph_path)
        self.index = None
        self.id_mapping = {}
        self.documents = []
        self.model_name = model_name
        
        # 路徑配置
        self.index_path = index_path or "data/faiss_index"
        self.mapping_path = f"{self.index_path}_mapping.pkl"
        
        # 初始化標記
        self.initialized = False
    
    async def initialize(self):
        """異步初始化"""
        if self.initialized:
            return
        
        try:
            # 載入句子轉換器模型
            logger.info(f"載入句子轉換器模型: {self.model_name}")
            self.encoder = SentenceTransformer(self.model_name)
            
            # 嘗試載入現有索引
            if os.path.exists(self.index_path) and os.path.exists(self.mapping_path):
                await self.load_index()
            else:
                logger.info("未找到現有索引，將在首次使用時構建")
            
            self.initialized = True
            logger.info("增強匹配器初始化完成")
            
        except Exception as e:
            logger.error(f"增強匹配器初始化失敗: {e}")
            raise
    
    async def build_index(self, documents: List[Dict] = None):
        """構建向量索引"""
        if not self.initialized:
            await self.initialize()
        
        try:
            if documents is None:
                # 從數據庫載入文檔
                documents = await self._load_documents_from_db()
            
            if not documents:
                logger.warning("沒有文檔可用於構建索引")
                return
            
            logger.info(f"開始構建索引，文檔數量: {len(documents)}")
            
            # 編碼所有文檔
            embeddings = []
            self.documents = documents
            
            for i, doc in enumerate(documents):
                # 擴展文檔概念
                content = doc.get('content', doc.get('query_text', ''))
                expanded_text = self.concept_expander.expand(content)
                
                # 編碼
                embedding = self.encoder.encode(expanded_text)
                embeddings.append(embedding)
                self.id_mapping[i] = doc.get('id', i)
            
            # 創建FAISS索引
            embeddings = np.array(embeddings).astype('float32')
            dimension = embeddings.shape[1]
            
            # 使用內積索引（適合歸一化向量）
            self.index = faiss.IndexFlatIP(dimension)
            
            # 歸一化向量
            faiss.normalize_L2(embeddings)
            self.index.add(embeddings)
            
            # 保存索引
            await self.save_index()
            
            logger.info(f"索引構建完成，維度: {dimension}, 文檔數: {len(documents)}")
            
        except Exception as e:
            logger.error(f"構建索引失敗: {e}")
            raise
    
    async def search(self, query: str, k: int = 10, threshold: float = 0.5) -> List[Tuple[str, float]]:
        """執行增強搜索"""
        if not self.initialized:
            await self.initialize()
        
        if self.index is None:
            logger.warning("索引未構建，返回空結果")
            return []
        
        try:
            # 查詢擴展
            expanded_queries = self.expand_query(query)
            
            # 對每個擴展查詢進行搜索
            all_results = []
            for exp_query, weight in expanded_queries:
                # 編碼查詢
                query_embedding = self.encoder.encode(exp_query)
                query_embedding = query_embedding.reshape(1, -1).astype('float32')
                
                # 歸一化
                faiss.normalize_L2(query_embedding)
                
                # FAISS搜索
                distances, indices = self.index.search(query_embedding, k)
                
                # 加權結果
                for i, (dist, idx) in enumerate(zip(distances[0], indices[0])):
                    if idx != -1 and dist >= threshold:  # 過濾低相似度結果
                        doc_id = self.id_mapping.get(idx, idx)
                        score = float(dist * weight)
                        all_results.append((doc_id, score))
            
            # 合併和排序結果
            return self.merge_results(all_results, k)
            
        except Exception as e:
            logger.error(f"搜索失敗: {e}")
            return []

    def expand_query(self, query: str) -> List[Tuple[str, float]]:
        """擴展查詢"""
        expansions = [(query, 1.0)]  # 原始查詢權重最高

        # 概念擴展
        concepts = self.concept_expander.extract_concepts(query)
        for concept in concepts:
            related = self.concept_expander.get_related_concepts(concept)
            for rel_concept, similarity in related[:3]:  # 取前3個相關概念
                expanded = query.replace(concept, rel_concept)
                if expanded != query and len(expanded.strip()) > 0:
                    expansions.append((expanded, similarity * 0.8))

        # 同義詞擴展（簡化版）
        synonyms = self._get_synonyms(query)
        for syn_query in synonyms[:2]:
            if syn_query not in [exp[0] for exp in expansions]:
                expansions.append((syn_query, 0.7))

        return expansions

    def _get_synonyms(self, query: str) -> List[str]:
        """獲取同義詞（簡化實現）"""
        synonyms = []

        # 基本同義詞映射
        synonym_map = {
            "AI": ["人工智能", "智能", "AI技術"],
            "SEO": ["搜索引擎優化", "網站優化", "排名優化"],
            "優化": ["改善", "提升", "增強"],
            "分析": ["解析", "研究", "檢測"],
            "查詢": ["搜索", "檢索", "查找"],
            "關鍵詞": ["關鍵字", "搜索詞", "查詢詞"]
        }

        for word, syns in synonym_map.items():
            if word in query:
                for syn in syns:
                    new_query = query.replace(word, syn)
                    if new_query != query:
                        synonyms.append(new_query)

        return synonyms

    def merge_results(self, results: List[Tuple[str, float]], k: int) -> List[Tuple[str, float]]:
        """合併搜索結果"""
        # 按文檔ID聚合分數
        doc_scores = {}
        for doc_id, score in results:
            if doc_id in doc_scores:
                # 取最高分數
                doc_scores[doc_id] = max(doc_scores[doc_id], score)
            else:
                doc_scores[doc_id] = score

        # 排序並返回top-k
        sorted_results = sorted(doc_scores.items(),
                              key=lambda x: x[1],
                              reverse=True)
        return sorted_results[:k]

    async def save_index(self):
        """保存索引到磁盤"""
        try:
            # 確保目錄存在
            os.makedirs(os.path.dirname(self.index_path), exist_ok=True)

            # 保存FAISS索引
            faiss.write_index(self.index, self.index_path)

            # 保存ID映射
            with open(self.mapping_path, 'wb') as f:
                pickle.dump({
                    'id_mapping': self.id_mapping,
                    'documents': self.documents
                }, f)

            logger.info(f"索引已保存到: {self.index_path}")

        except Exception as e:
            logger.error(f"保存索引失敗: {e}")
            raise

    async def load_index(self):
        """從磁盤載入索引"""
        try:
            # 載入FAISS索引
            self.index = faiss.read_index(self.index_path)

            # 載入ID映射
            with open(self.mapping_path, 'rb') as f:
                data = pickle.load(f)
                self.id_mapping = data['id_mapping']
                self.documents = data.get('documents', [])

            logger.info(f"索引已載入: {len(self.id_mapping)} 個文檔")

        except Exception as e:
            logger.error(f"載入索引失敗: {e}")
            raise

    async def _load_documents_from_db(self) -> List[Dict]:
        """從數據庫載入文檔"""
        try:
            from sqlalchemy import select
            from ..core.database import get_async_session

            documents = []

            async with get_async_session() as session:
                # 查詢所有長尾查詢記錄
                result = await session.execute(
                    select(LongTailQuery).limit(10000)  # 限制數量避免內存問題
                )
                queries = result.scalars().all()

                for query in queries:
                    doc = {
                        'id': str(query.id),
                        'content': query.query_text,
                        'query_text': query.query_text,
                        'query_type': query.query_type,
                        'intent': query.intent,
                        'semantic_category': query.semantic_category,
                        'longtail_score': query.longtail_score,
                        'keywords': query.keywords or []
                    }
                    documents.append(doc)

            logger.info(f"從數據庫載入 {len(documents)} 個文檔")
            return documents

        except Exception as e:
            logger.error(f"從數據庫載入文檔失敗: {e}")
            return []

    async def add_document(self, document: Dict):
        """添加新文檔到索引"""
        if not self.initialized:
            await self.initialize()

        try:
            # 擴展文檔內容
            content = document.get('content', document.get('query_text', ''))
            expanded_text = self.concept_expander.expand(content)

            # 編碼
            embedding = self.encoder.encode(expanded_text)
            embedding = embedding.reshape(1, -1).astype('float32')

            # 歸一化
            faiss.normalize_L2(embedding)

            # 添加到索引
            if self.index is None:
                # 創建新索引
                dimension = embedding.shape[1]
                self.index = faiss.IndexFlatIP(dimension)

            # 獲取新的索引ID
            new_idx = len(self.id_mapping)
            self.id_mapping[new_idx] = document.get('id', new_idx)
            self.documents.append(document)

            # 添加到索引
            self.index.add(embedding)

            logger.info(f"文檔已添加到索引: {document.get('id', new_idx)}")

        except Exception as e:
            logger.error(f"添加文檔到索引失敗: {e}")
            raise

    async def get_similar_queries(self, query: str, k: int = 5) -> List[Dict]:
        """獲取相似查詢的詳細信息"""
        similar_ids = await self.search(query, k)

        similar_queries = []
        for doc_id, score in similar_ids:
            # 查找對應的文檔
            doc = None
            for d in self.documents:
                if str(d.get('id')) == str(doc_id):
                    doc = d
                    break

            if doc:
                similar_queries.append({
                    'id': doc_id,
                    'query': doc.get('query_text', ''),
                    'score': score,
                    'query_type': doc.get('query_type', ''),
                    'intent': doc.get('intent', ''),
                    'semantic_category': doc.get('semantic_category', ''),
                    'longtail_score': doc.get('longtail_score', 0.0)
                })

        return similar_queries
