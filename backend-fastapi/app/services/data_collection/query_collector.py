import json
import logging
import asyncio
import hashlib
from typing import List, Dict, Any, Optional
from pathlib import Path
from datetime import datetime
import random

# 設置日誌
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class QueryDataCollector:
    """
    查詢數據收集器，用於收集和管理真實查詢數據，以替代模擬數據提高模型準確性
    """
    def __init__(self, data_dir: str = "./data/collected_queries"):
        self.data_dir = Path(data_dir)
        self.data_dir.mkdir(exist_ok=True, parents=True)
        self.raw_queries_file = self.data_dir / "raw_queries.json"
        self.labeled_queries_file = self.data_dir / "labeled_queries.json"
        self.raw_queries = self._load_raw_queries()
        self.labeled_queries = self._load_labeled_queries()
        
    def _load_raw_queries(self) -> List[Dict[str, Any]]:
        """載入已收集的原始查詢數據"""
        if self.raw_queries_file.exists():
            try:
                with open(self.raw_queries_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                logger.error(f"載入原始查詢數據失敗: {e}")
                return []
        else:
            return []
    
    def _load_labeled_queries(self) -> List[Dict[str, Any]]:
        """載入已標註的查詢數據"""
        if self.labeled_queries_file.exists():
            try:
                with open(self.labeled_queries_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                logger.error(f"載入已標註查詢數據失敗: {e}")
                return []
        else:
            return []
    
    def _save_raw_queries(self) -> bool:
        """保存原始查詢數據"""
        try:
            with open(self.raw_queries_file, 'w', encoding='utf-8') as f:
                json.dump(self.raw_queries, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            logger.error(f"保存原始查詢數據失敗: {e}")
            return False
    
    def _save_labeled_queries(self) -> bool:
        """保存已標註查詢數據"""
        try:
            with open(self.labeled_queries_file, 'w', encoding='utf-8') as f:
                json.dump(self.labeled_queries, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            logger.error(f"保存已標註查詢數據失敗: {e}")
            return False
    
    def _generate_query_id(self, query: str) -> str:
        """為查詢生成唯一ID"""
        timestamp = datetime.now().strftime("%Y%m%d%H%M%S%f")
        hash_input = f"{query}_{timestamp}_{random.randint(1000, 9999)}"
        return hashlib.md5(hash_input.encode()).hexdigest()[:16]
    
    def collect_query(self, query: str, source: str = "user_input",
                    metadata: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        收集單個查詢
        
        Args:
            query: 查詢文本
            source: 查詢來源
            metadata: 相關的元數據
            
        Returns:
            包含查詢ID的字典
        """
        query_id = self._generate_query_id(query)
        
        # 創建查詢記錄
        query_record = {
            "id": query_id,
            "query": query,
            "timestamp": datetime.now().isoformat(),
            "source": source,
            "is_processed": False,
            "metadata": metadata or {}
        }
        
        # 添加到原始查詢列表
        self.raw_queries.append(query_record)
        
        # 保存原始查詢數據
        self._save_raw_queries()
        
        return {"query_id": query_id, "status": "collected"}
    
    def collect_batch_queries(self, queries: List[str], source: str = "batch_import",
                           metadata: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        批量收集查詢
        
        Args:
            queries: 查詢文本列表
            source: 查詢來源
            metadata: 相關的元數據
            
        Returns:
            包含收集結果的字典
        """
        collected_ids = []
        
        for query in queries:
            result = self.collect_query(query, source, metadata)
            collected_ids.append(result["query_id"])
        
        return {
            "status": "success",
            "collected_count": len(queries),
            "query_ids": collected_ids
        }
    
    def add_labeled_query(self, query: str, intent: str, 
                       commercial_value: float = 0.0,
                       is_commercial: bool = False,
                       confidence: float = 1.0,
                       source: str = "manual_label",
                       metadata: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        添加已標註的查詢
        
        Args:
            query: 查詢文本
            intent: 意圖標籤
            commercial_value: 商業價值評分
            is_commercial: 是否為商業意圖
            confidence: 標籤置信度
            source: 標籤來源
            metadata: 相關的元數據
            
        Returns:
            包含查詢ID的字典
        """
        query_id = self._generate_query_id(query)
        
        # 創建已標註的查詢記錄
        labeled_record = {
            "id": query_id,
            "query": query,
            "intent": intent,
            "commercial_value": commercial_value,
            "is_commercial": is_commercial,
            "confidence": confidence,
            "timestamp": datetime.now().isoformat(),
            "label_source": source,
            "metadata": metadata or {}
        }
        
        # 添加到已標註查詢列表
        self.labeled_queries.append(labeled_record)
        
        # 保存已標註查詢數據
        self._save_labeled_queries()
        
        return {"query_id": query_id, "status": "labeled"}
    
    def add_batch_labeled_queries(self, labeled_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        批量添加已標註的查詢
        
        Args:
            labeled_data: 已標註的查詢數據列表
            
        Returns:
            包含處理結果的字典
        """
        added_count = 0
        query_ids = []
        
        for item in labeled_data:
            try:
                result = self.add_labeled_query(
                    query=item["query"],
                    intent=item["intent"],
                    commercial_value=item.get("commercial_value", 0.0),
                    is_commercial=item.get("is_commercial", False),
                    confidence=item.get("confidence", 1.0),
                    source=item.get("source", "batch_import"),
                    metadata=item.get("metadata")
                )
                query_ids.append(result["query_id"])
                added_count += 1
            except Exception as e:
                logger.error(f"處理標註數據失敗: {e}")
        
        return {
            "status": "success",
            "added_count": added_count,
            "total_items": len(labeled_data),
            "query_ids": query_ids
        }
    
    def process_raw_query(self, query_id: str, intent: str,
                       commercial_value: float = 0.0,
                       is_commercial: bool = False,
                       confidence: float = 1.0) -> Dict[str, Any]:
        """
        處理原始查詢，添加標註信息
        
        Args:
            query_id: 查詢ID
            intent: 意圖標籤
            commercial_value: 商業價值評分
            is_commercial: 是否為商業意圖
            confidence: 標籤置信度
            
        Returns:
            處理結果
        """
        # 查找原始查詢
        query_record = None
        for record in self.raw_queries:
            if record["id"] == query_id:
                query_record = record
                break
        
        if not query_record:
            return {"status": "error", "message": f"找不到ID為 {query_id} 的查詢"}
        
        # 標記為已處理
        query_record["is_processed"] = True
        self._save_raw_queries()
        
        # 創建已標註的查詢記錄
        labeled_record = {
            "id": query_id,
            "query": query_record["query"],
            "intent": intent,
            "commercial_value": commercial_value,
            "is_commercial": is_commercial,
            "confidence": confidence,
            "timestamp": datetime.now().isoformat(),
            "label_source": "processed",
            "original_metadata": query_record["metadata"]
        }
        
        # 添加到已標註查詢列表
        self.labeled_queries.append(labeled_record)
        self._save_labeled_queries()
        
        return {"status": "success", "message": "查詢已處理並標註"}
    
    def get_raw_queries(self, limit: int = 100, processed: bool = False) -> List[Dict[str, Any]]:
        """
        獲取原始查詢數據
        
        Args:
            limit: 返回數量限制
            processed: 是否僅返回已處理的查詢
            
        Returns:
            查詢列表
        """
        filtered = [q for q in self.raw_queries if q["is_processed"] == processed]
        return sorted(filtered, key=lambda x: x["timestamp"], reverse=True)[:limit]
    
    def get_labeled_queries(self, intent: Optional[str] = None, 
                         is_commercial: Optional[bool] = None,
                         limit: int = 100) -> List[Dict[str, Any]]:
        """
        獲取已標註的查詢數據
        
        Args:
            intent: 篩選特定意圖
            is_commercial: 篩選商業意圖
            limit: 返回數量限制
            
        Returns:
            查詢列表
        """
        filtered = self.labeled_queries
        
        if intent is not None:
            filtered = [q for q in filtered if q["intent"] == intent]
        
        if is_commercial is not None:
            filtered = [q for q in filtered if q["is_commercial"] == is_commercial]
        
        return sorted(filtered, key=lambda x: x["timestamp"], reverse=True)[:limit]
    
    def get_intent_distribution(self) -> Dict[str, int]:
        """獲取已標註查詢的意圖分佈"""
        intent_counts = {}
        for query in self.labeled_queries:
            intent = query["intent"]
            if intent not in intent_counts:
                intent_counts[intent] = 0
            intent_counts[intent] += 1
        
        return intent_counts
    
    def get_commercial_value_stats(self) -> Dict[str, Any]:
        """獲取商業價值評分的統計數據"""
        if not self.labeled_queries:
            return {
                "avg_commercial_value": 0,
                "commercial_queries_percentage": 0,
                "count": 0
            }
        
        values = [q["commercial_value"] for q in self.labeled_queries]
        commercial_count = sum(1 for q in self.labeled_queries if q["is_commercial"])
        
        return {
            "avg_commercial_value": sum(values) / len(values),
            "commercial_queries_percentage": (commercial_count / len(self.labeled_queries)) * 100,
            "count": len(self.labeled_queries)
        }
    
    def export_training_data(self) -> Dict[str, Any]:
        """導出用於模型訓練的數據"""
        if not self.labeled_queries:
            return {"status": "error", "message": "沒有已標註的查詢數據可用於訓練"}
        
        training_data = {
            "texts": [],
            "intents": [],
            "commercial_values": []
        }
        
        for query in self.labeled_queries:
            training_data["texts"].append(query["query"])
            training_data["intents"].append(query["intent"])
            training_data["commercial_values"].append(query["commercial_value"])
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        export_file = self.data_dir / f"training_data_{timestamp}.json"
        
        try:
            with open(export_file, 'w', encoding='utf-8') as f:
                json.dump(training_data, f, ensure_ascii=False, indent=2)
            
            return {
                "status": "success",
                "message": f"已導出 {len(training_data['texts'])} 條訓練數據",
                "file_path": str(export_file)
            }
        except Exception as e:
            logger.error(f"導出訓練數據失敗: {e}")
            return {"status": "error", "message": f"導出訓練數據失敗: {str(e)}"}
    
    def import_from_csv(self, file_path: str, has_header: bool = True) -> Dict[str, Any]:
        """從CSV文件導入查詢數據"""
        import csv
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                reader = csv.reader(f)
                
                if has_header:
                    next(reader)  # 跳過標題行
                
                queries = []
                for row in reader:
                    if not row or not row[0].strip():
                        continue
                    
                    queries.append(row[0].strip())
            
            result = self.collect_batch_queries(
                queries, 
                source="csv_import",
                metadata={"file": file_path}
            )
            
            return {
                "status": "success",
                "message": f"從CSV文件導入了 {len(queries)} 條查詢",
                "result": result
            }
        except Exception as e:
            logger.error(f"從CSV導入查詢失敗: {e}")
            return {"status": "error", "message": f"導入失敗: {str(e)}"}
    
    def import_from_log(self, log_file: str, query_pattern: str = r'"query"\s*:\s*"([^"]+)"') -> Dict[str, Any]:
        """從日誌文件導入查詢數據，使用正則表達式提取查詢"""
        import re
        
        try:
            with open(log_file, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            
            # 使用正則表達式提取查詢
            matches = re.findall(query_pattern, content)
            
            if not matches:
                return {"status": "warning", "message": "未找到匹配的查詢"}
            
            # 收集提取的查詢
            result = self.collect_batch_queries(
                matches,
                source="log_import",
                metadata={"file": log_file}
            )
            
            return {
                "status": "success", 
                "message": f"從日誌文件導入了 {len(matches)} 條查詢",
                "result": result
            }
        except Exception as e:
            logger.error(f"從日誌導入查詢失敗: {e}")
            return {"status": "error", "message": f"導入失敗: {str(e)}"}
