"""
意圖分類處理器
使用規則和機器學習方法對查詢意圖進行分類
"""

import re
from typing import Dict, List, Tuple
import structlog

from app.services.data_collection_pipeline import ProcessedQueryData

logger = structlog.get_logger()


class IntentClassificationProcessor:
    """意圖分類處理器"""
    
    def __init__(self):
        self.name = "IntentClassification"
        
        # 意圖分類規則 - 基本意圖和擴展商業意圖類別
        self.intent_patterns = {
            # 基本搜尋意圖
            'search': {
                'patterns': [
                    r'搜[尋索]',
                    r'找.*[商產]品',
                    r'search',
                    r'find',
                    r'looking for'
                ],
                'keywords': ['搜尋', '搜索', '查找', '尋找', 'search', 'find'],
                'commercial_value': 0.3  # 商業價值評分 (0-1)
            },
            # 基本信息意圖
            'information': {
                'patterns': [
                    r'什麼是',
                    r'如何.*',
                    r'怎麼.*',
                    r'為什麼',
                    r'what is',
                    r'how to',
                    r'why'
                ],
                'keywords': ['什麼', '如何', '怎麼', '為什麼', 'what', 'how', 'why'],
                'commercial_value': 0.2
            },
            # === 商業意圖細分類別 ===
            # 比較購物意圖
            'commercial_comparison': {
                'patterns': [
                    r'比較.*[商品|產品|方案|服務]',
                    r'[產品|方案|服務].*比較',
                    r'對比.*[價格|功能|規格]',
                    r'vs',
                    r'哪個.*好',
                    r'哪個.*值得',
                    r'哪款.*推薦',
                    r'compare.*products',
                    r'difference between',
                    r'which is better'
                ],
                'keywords': ['比較', '對比', '差異', 'vs', '區別', 'compare', 'versus', 'comparison', '哪個好'],
                'commercial_value': 0.7
            },
            # 直接購買意圖
            'commercial_purchase': {
                'patterns': [
                    r'購買.*[方式|渠道]',
                    r'[如何|怎麼].*購買',
                    r'[哪裡|哪兒].*買',
                    r'訂購.*[方式|流程]',
                    r'直接.*購買',
                    r'立即.*購買',
                    r'buy now',
                    r'purchase.*online',
                    r'order.*now',
                    r'checkout'
                ],
                'keywords': ['購買', '買', '訂購', '結帳', '付款', 'buy', 'purchase', 'order', 'checkout'],
                'commercial_value': 0.9
            },
            # 價格查詢意圖
            'commercial_pricing': {
                'patterns': [
                    r'[價格|費用|收費].*多少',
                    r'多少錢',
                    r'[產品|服務].*價格',
                    r'價格.*[範圍|區間]',
                    r'[便宜|貴].*多少',
                    r'price.*range',
                    r'how much.*cost',
                    r'pricing.*plans',
                    r'subscription.*fee'
                ],
                'keywords': ['價格', '費用', '收費', '多少錢', '報價', 'price', 'cost', 'fee', 'pricing'],
                'commercial_value': 0.8
            },
            # 產品研究意圖
            'commercial_research': {
                'patterns': [
                    r'[產品|服務].*資訊',
                    r'了解.*[產品|服務]',
                    r'[產品|服務].*資料',
                    r'[產品|服務].*規格',
                    r'[產品|服務].*功能',
                    r'product.*information',
                    r'product.*specs',
                    r'service.*details'
                ],
                'keywords': ['資訊', '資料', '規格', '功能', '特點', 'information', 'specs', 'features', 'details'],
                'commercial_value': 0.5
            },
            # 尋求服務諮詢意圖
            'commercial_consultation': {
                'patterns': [
                    r'[諮詢|咨詢].*服務',
                    r'[專家|顧問].*[諮詢|咨詢]',
                    r'預約.*[諮詢|咨詢]',
                    r'[諮詢|咨詢].*[專線|電話]',
                    r'免費.*[諮詢|咨詢]',
                    r'consultation.*service',
                    r'expert.*advice',
                    r'free.*consultation'
                ],
                'keywords': ['諮詢', '咨詢', '顧問', '專家', '預約', 'consultation', 'advice', 'expert', 'appointment'],
                'commercial_value': 0.7
            },
            # 評價查詢意圖
            'review': {
                'patterns': [
                    r'評[價論]',
                    r'心得',
                    r'使用.*經驗',
                    r'好不好[用|買]',
                    r'值不值[得|錢]',
                    r'review',
                    r'rating',
                    r'testimonial',
                    r'feedback'
                ],
                'keywords': ['評價', '評論', '心得', '經驗', '好評', 'review', 'rating', 'testimonial'],
                'commercial_value': 0.6
            },
            # 問題解決意圖
            'troubleshooting': {
                'patterns': [
                    r'問題',
                    r'錯誤',
                    r'故障',
                    r'不.*工作',
                    r'如何.*修復',
                    r'解決.*問題',
                    r'problem',
                    r'error',
                    r'issue',
                    r'fix'
                ],
                'keywords': ['問題', '錯誤', '故障', '修復', '解決', 'problem', 'error', 'issue', 'fix'],
                'commercial_value': 0.4
            },
            # 位置導航意圖
            'navigation': {
                'patterns': [
                    r'哪裡.*[買找]',
                    r'地[址點]',
                    r'位置',
                    r'附近.*[店|門市]',
                    r'實體.*店',
                    r'導航.*到',
                    r'where',
                    r'location',
                    r'store.*near'
                ],
                'keywords': ['哪裡', '地址', '位置', '門市', '實體店', 'where', 'location', 'store', 'directions'],
                'commercial_value': 0.6
            },
            # 尋求推薦意圖
            'recommendation': {
                'patterns': [
                    r'推薦',
                    r'建議',
                    r'最好.*[的|產品|品牌]',
                    r'[值得|好].*[買|用]',
                    r'[有沒有|有什麼].*推薦',
                    r'recommend',
                    r'suggest',
                    r'best.*[product|brand]',
                    r'top.*[rated|selling]'
                ],
                'keywords': ['推薦', '建議', '最好', '值得', '好用', 'recommend', 'best', 'suggest', 'top'],
                'commercial_value': 0.8
            }
        }
        
        # SEO 相關意圖 - 擴展為更細緻的SEO商業意圖分類
        self.seo_patterns = {
            # 基本SEO分析意圖
            'seo_analysis': {
                'patterns': [
                    r'SEO.*分析',
                    r'搜尋.*優化',
                    r'關鍵[字詞].*分析',
                    r'seo.*analysis',
                    r'網站.*排名',
                    r'搜尋引擎.*優化'
                ],
                'keywords': ['SEO', '優化', '分析', '關鍵字', '排名', '搜尋引擎'],
                'commercial_value': 0.7
            },
            # 關鍵字研究意圖
            'keyword_research': {
                'patterns': [
                    r'關鍵[字詞].*研究',
                    r'關鍵[字詞].*工具',
                    r'keyword.*research',
                    r'長尾關鍵字',
                    r'熱門.*搜尋詞',
                    r'搜尋量.*關鍵詞'
                ],
                'keywords': ['關鍵字研究', '關鍵詞工具', '長尾詞', '搜尋量', '熱門關鍵字', 'keyword research'],
                'commercial_value': 0.8
            },
            # 競爭對手SEO分析意圖
            'competitor_analysis': {
                'patterns': [
                    r'競爭.*分析',
                    r'對手.*分析',
                    r'competitor.*analysis',
                    r'同業.*SEO',
                    r'競爭.*關鍵字',
                    r'競爭對手.*排名'
                ],
                'keywords': ['競爭分析', '對手分析', '同業', 'competitor', '競爭情報', '市場分析'],
                'commercial_value': 0.7
            },
            # 內容優化意圖
            'content_optimization': {
                'patterns': [
                    r'內容.*優化',
                    r'文章.*SEO',
                    r'content.*optimization',
                    r'網站.*內容.*優化',
                    r'部落格.*優化',
                    r'文章.*關鍵字'
                ],
                'keywords': ['內容優化', '文章SEO', '部落格', 'content optimization', '內容策略', '網站內容'],
                'commercial_value': 0.6
            },
            # 技術SEO意圖
            'technical_seo': {
                'patterns': [
                    r'技術性.*SEO',
                    r'網站速度.*優化',
                    r'結構化數據',
                    r'網站架構.*SEO',
                    r'爬蟲.*優化',
                    r'technical.*seo',
                    r'schema.*markup'
                ],
                'keywords': ['技術SEO', '網站速度', '結構化數據', 'schema', '爬蟲', 'technical SEO', 'crawl'],
                'commercial_value': 0.6
            },
            # 本地SEO意圖
            'local_seo': {
                'patterns': [
                    r'本地.*SEO',
                    r'在地.*優化',
                    r'Google商家',
                    r'地圖.*排名',
                    r'附近.*搜尋',
                    r'local.*seo',
                    r'google.*business'
                ],
                'keywords': ['本地SEO', '在地優化', '地區排名', 'Google商家', 'Google地圖', 'local SEO', 'GMB'],
                'commercial_value': 0.8
            },
            # SEO數據分析意圖
            'seo_analytics': {
                'patterns': [
                    r'SEO.*報表',
                    r'排名.*追蹤',
                    r'流量.*分析',
                    r'數據.*SEO',
                    r'轉換率.*優化',
                    r'seo.*analytics',
                    r'ranking.*reports'
                ],
                'keywords': ['SEO報表', '排名追蹤', '流量分析', '數據分析', '轉換率', 'analytics', 'reports'],
                'commercial_value': 0.7
            },
            # 反向連結建設意圖
            'backlink_building': {
                'patterns': [
                    r'反向連結',
                    r'外部連結',
                    r'連結.*建設',
                    r'連結.*策略',
                    r'高權重.*連結',
                    r'backlinks',
                    r'link.*building'
                ],
                'keywords': ['反向連結', '外部連結', '連結建設', '權重', '外鏈', 'backlinks', 'link building'],
                'commercial_value': 0.7
            }
        }
        
        # 商業價值指標 - 用於評估不同意圖類型的商業潛力
        self.commercial_indicators = {
            # 高轉化率關鍵詞
            'high_conversion_terms': [
                '購買', '訂購', '價格', '優惠', '折扣', '促銷',
                '最佳', '比較', '推薦', '評價', '方案', '費用',
                'buy', 'price', 'discount', 'deal', 'best', 'review', 'top'
            ],
            # 高商業價值詞彙
            'commercial_value_terms': [
                '專業', '服務', '方案', '品牌', '官方', '保固',
                '團購', '限時', '獨家', '首選', '認證', '保證',
                'professional', 'service', 'brand', 'official', 'warranty', 'exclusive'
            ],
            # 購買意向詞彙
            'purchase_intent_terms': [
                '立即', '現在', '今天', '線上', '訂購', '購物車',
                '結帳', '付款', '運費', '配送', '貨到付款', '信用卡',
                'now', 'today', 'online', 'cart', 'checkout', 'shipping'
            ],
            # 專業諮詢詞彙
            'consultation_terms': [
                '諮詢', '顧問', '專家', '建議', '解決方案', '客製化',
                '預約', '聯繫', '專線', '線上客服', '免費評估',
                'consult', 'expert', 'solution', 'custom', 'contact', 'free assessment'
            ]
        }
        
        # 意圖相關的SEO策略建議
        self.seo_strategy_recommendations = {
            # 商業意圖的SEO策略
            'commercial_purchase': [
                '強化產品頁面的轉換元素（價格、購買按鈕、付款選項）',
                '加入顧客評價和社會證明',
                '優化商品結構化資料標記',
                '確保結帳流程順暢且行動裝置友善'
            ],
            'commercial_comparison': [
                '建立產品比較表格和詳細規格說明',
                '強調產品優勢和獨特賣點',
                '提供專業評測和實際使用案例',
                '清晰顯示價格和方案差異'
            ],
            'commercial_research': [
                '提供豐富的產品資訊和教學內容',
                '加入影片和視覺化內容說明產品功能',
                '建立產品知識庫和常見問題解答',
                '提供產品規格PDF下載'
            ],
            'commercial_pricing': [
                '清晰列出價格資訊和方案比較',
                '強調性價比和價值主張',
                '設置價格相關的結構化資料',
                '提供多種付款選項和彈性方案'
            ],
            'commercial_consultation': [
                '優化諮詢表單和聯絡頁面',
                '突顯專業資格和專家認證',
                '提供免費諮詢或評估選項',
                '展示成功案例和客戶見證'
            ],
            
            # SEO專項策略
            'keyword_research': [
                '聚焦長尾關鍵詞和問答式內容',
                '建立主題內容叢集',
                '優化內部連結結構',
                '針對搜尋意圖設計內容'
            ],
            'technical_seo': [
                '優化網站速度和行動裝置體驗',
                '修復爬蟲錯誤和索引問題',
                '實施結構化資料標記',
                '確保符合核心網頁指標'
            ],
            'local_seo': [
                '優化Google商家資訊',
                '建立並維護本地引文',
                '取得正面評價和回應',
                '建立地區性內容和著陸頁'
            ],
            'content_optimization': [
                '優化標題標籤和描述中繼標籤',
                '建立優質原創內容',
                '強化內容層級和標題結構',
                '優化圖片和多媒體元素'
            ]
        }

    async def process(self, data: ProcessedQueryData) -> ProcessedQueryData:
        """處理意圖分類，並添加商業價值評估"""
        try:
            query = data.normalized_query
            
            if not query:
                data.intent = 'unknown'
                data.intent_confidence = 0.0
                return data
            
            # 執行意圖分類
            intent, confidence = self._classify_intent(query)
            
            # 基本屬性賦值
            data.intent = intent
            data.intent_confidence = confidence
            
            # 商業價值評估
            commercial_value = self._evaluate_commercial_value(query, intent)
            data.commercial_value = commercial_value
            
            # 添加SEO策略建議
            seo_strategies = self._get_seo_strategies(intent)
            data.seo_strategy_recommendations = seo_strategies
            
            # 添加意圖改進建議
            if confidence < 0.7:  # 只在信心度較低時提供改進建議
                improvement_suggestions = self._generate_improvement_suggestions(query, intent, confidence)
                data.intent_improvement_suggestions = improvement_suggestions
            
            # 增加細分意圖類別
            data.intent_category = self._categorize_intent(intent)
            
            logger.debug(
                "意圖分類完成",
                query=query,
                intent=intent,
                confidence=confidence,
                commercial_value=commercial_value,
                intent_category=data.intent_category
            )
            
            return data
            
        except Exception as e:
            logger.error("意圖分類處理失敗", error=str(e), exc_info=True)
            data.intent = 'unknown'
            data.intent_confidence = 0.0
            data.commercial_value = 0.0
            return data

    def _classify_intent(self, query: str) -> Tuple[str, float]:
        """執行意圖分類"""
        query_lower = query.lower()
        intent_scores = {}
        
        # 檢查一般意圖
        for intent, config in self.intent_patterns.items():
            score = self._calculate_intent_score(query_lower, config)
            if score > 0:
                intent_scores[intent] = score
        
        # 檢查 SEO 特定意圖
        for intent, config in self.seo_patterns.items():
            score = self._calculate_intent_score(query_lower, config)
            if score > 0:
                intent_scores[intent] = score
        
        # 如果沒有匹配到任何意圖
        if not intent_scores:
            return 'unknown', 0.0
        
        # 選擇得分最高的意圖
        best_intent = max(intent_scores.items(), key=lambda x: x[1])
        intent, raw_score = best_intent
        
        # 計算信心度（0-1之間）
        confidence = min(raw_score / 10.0, 1.0)
        
        return intent, confidence

    def _calculate_intent_score(self, query: str, config: Dict) -> float:
        """計算意圖得分，根據模式和關鍵詞匹配進行評分"""
        score = 0.0
        query_lower = query.lower()
        
        # 檢查模式匹配
        for pattern in config.get('patterns', []):
            if re.search(pattern, query_lower):
                score += 5.0  # 模式匹配得到高分
        
        # 檢查關鍵詞匹配
        for keyword in config.get('keywords', []):
            if keyword.lower() in query_lower:
                score += 2.0  # 關鍵詞匹配得到中等分數
                
        # 檢查商業價值指標（如果包含高商業價值詞彙則加分）
        for term in self.commercial_indicators['high_conversion_terms']:
            if term.lower() in query_lower:
                score += 1.5
                
        for term in self.commercial_indicators['commercial_value_terms']:
            if term.lower() in query_lower:
                score += 1.0
                
        for term in self.commercial_indicators['purchase_intent_terms']:
            if term.lower() in query_lower:
                score += 2.0
        
        # 內容複雜度加權（長尾查詢通常更具體且具有價值）
        words = query.split()
        if len(words) >= 6:  # 長查詢獲得額外分數
            score += min(len(words) / 3, 3.0)  # 上限為3分
        
        return score

    def _classify_by_context(self, query: str, metadata: Dict) -> Tuple[str, float]:
        """基於上下文的意圖分類"""
        # 檢查來源類型
        source = metadata.get('source', '')
        if source == 'api_request':
            endpoint = metadata.get('endpoint', '')
            if 'analyze' in endpoint:
                return 'seo_analysis', 0.8
            elif 'keyword' in endpoint:
                return 'keyword_research', 0.8
        
        # 檢查設備類型
        device_type = metadata.get('device_type', '')
        if device_type == 'mobile' and any(word in query for word in ['附近', '地址', 'near', 'location']):
            return 'navigation', 0.7
        
        # 檢查時間上下文
        time_info = metadata.get('time_info', {})
        if time_info.get('is_business_hours', False) and 'price' in query:
            return 'purchase', 0.6
        
        return 'unknown', 0.0

    def get_intent_explanation(self, intent: str) -> str:
        """獲取意圖解釋"""
        explanations = {
            'search': '用戶正在搜索特定的產品或信息',
            'information': '用戶想要了解某個概念或獲取知識',
            'comparison': '用戶想要比較不同的選項或產品',
            'purchase': '用戶有購買意向或詢問價格',
            'review': '用戶想要查看評價或分享經驗',
            'troubleshooting': '用戶遇到問題需要解決方案',
            'navigation': '用戶需要位置或導航信息',
            'recommendation': '用戶尋求推薦或建議',
            'seo_analysis': '用戶需要SEO分析服務',
            'keyword_research': '用戶進行關鍵字研究',
            'competitor_analysis': '用戶需要競爭對手分析',
            'content_optimization': '用戶需要內容優化建議',
            'unknown': '無法確定用戶意圖'
        }
        
        return explanations.get(intent, '未知意圖類型')

    def get_intent_statistics(self) -> Dict[str, int]:
        """獲取意圖分類統計，包括商業意圖分布"""
        # 計算商業意圖的數量
        commercial_intents = sum(1 for intent, config in self.intent_patterns.items() 
                                if intent.startswith('commercial_') or 
                                config.get('commercial_value', 0) >= 0.6)
        
        # 計算SEO相關商業意圖的數量
        commercial_seo_intents = sum(1 for _, config in self.seo_patterns.items() 
                                    if config.get('commercial_value', 0) >= 0.6)
        
        return {
            'total_patterns': len(self.intent_patterns) + len(self.seo_patterns),
            'general_intents': len(self.intent_patterns),
            'seo_intents': len(self.seo_patterns),
            'commercial_intents': commercial_intents,
            'commercial_seo_intents': commercial_seo_intents,
            'high_value_intents': commercial_intents + commercial_seo_intents
        }
    
    def _evaluate_commercial_value(self, query: str, intent: str) -> float:
        """評估查詢的商業價值（0-1之間的分數）"""
        # 基礎商業價值來自意圖本身
        base_value = 0.0
        
        # 從意圖配置中獲取預設商業價值
        if intent in self.intent_patterns:
            base_value = self.intent_patterns[intent].get('commercial_value', 0.0)
        elif intent in self.seo_patterns:
            base_value = self.seo_patterns[intent].get('commercial_value', 0.0)
        
        # 計算額外的商業信號
        commercial_signals = 0.0
        query_lower = query.lower()
        
        # 檢查高價值商業詞彙
        conversion_terms = sum(1 for term in self.commercial_indicators['high_conversion_terms'] 
                              if term.lower() in query_lower)
        commercial_terms = sum(1 for term in self.commercial_indicators['commercial_value_terms'] 
                              if term.lower() in query_lower)
        purchase_terms = sum(1 for term in self.commercial_indicators['purchase_intent_terms'] 
                            if term.lower() in query_lower)
        
        # 權重計算
        commercial_signals = (conversion_terms * 0.05 + 
                             commercial_terms * 0.03 + 
                             purchase_terms * 0.07)
        
        # 計算最終商業價值 (基礎值 + 調整值)，最高為1.0
        final_value = min(base_value + commercial_signals, 1.0)
        
        return round(final_value, 2)
    
    def _get_seo_strategies(self, intent: str) -> List[str]:
        """根據意圖獲取相應的SEO策略建議"""
        # 從策略字典中獲取建議
        strategies = []
        
        # 直接匹配意圖
        if intent in self.seo_strategy_recommendations:
            strategies.extend(self.seo_strategy_recommendations[intent])
        
        # 針對SEO意圖獲取一般性建議
        if intent in self.seo_patterns:
            if 'content_optimization' in self.seo_strategy_recommendations:
                strategies.extend(self.seo_strategy_recommendations['content_optimization'])
        
        # 針對商業意圖獲取商業相關建議
        if intent.startswith('commercial_') or intent in ['purchase', 'recommendation']:
            if 'commercial_purchase' in self.seo_strategy_recommendations:
                strategies.extend(self.seo_strategy_recommendations['commercial_purchase'])
        
        # 如果沒有特定策略，提供一般建議
        if not strategies:
            strategies = [
                '針對相關長尾關鍵詞優化內容',
                '提高內容質量和原創性',
                '優化頁面標題和描述中繼標籤',
                '改善用戶體驗和頁面載入速度'
            ]
        
        return strategies[:4]  # 最多返回4個建議
    
    def _generate_improvement_suggestions(self, query: str, intent: str, confidence: float) -> List[str]:
        """生成意圖改進建議，幫助提高意圖識別準確度"""
        suggestions = []
        
        # 信心度低於0.3認為是非常不確定
        if confidence < 0.3:
            suggestions.append("此查詢的意圖不明確，建議提供更多上下文或具體關鍵詞")
        
        # 查詢太短
        if len(query.split()) < 3:
            suggestions.append("查詢過短，建議擴展為更具體的長尾查詢以提高意圖識別準確度")
        
        # 針對商業意圖的改進建議
        if intent.startswith('commercial_') and confidence < 0.6:
            suggestions.append("添加具體的商業詞彙（如'購買'、'價格'、'比較'）以更清晰表達商業意圖")
        
        # 針對SEO意圖的改進建議
        if intent in self.seo_patterns and confidence < 0.6:
            suggestions.append("增加SEO術語或明確的SEO目標以提高意圖識別準確度")
        
        # 針對意圖不明確的情況
        if intent == 'unknown':
            suggestions.extend([
                "使用更具體的問句（如'如何'、'什麼是'）來表達信息需求",
                "添加行業或主題相關詞彙來幫助確定查詢上下文"
            ])
        
        # 如果沒有具體建議，提供一般性建議
        if not suggestions:
            suggestions = ["使用更具體的關鍵詞來提高意圖識別準確度"]
        
        return suggestions
    
    def _categorize_intent(self, intent: str) -> str:
        """將意圖分類到主要類別"""
        # 商業意圖類別
        if intent.startswith('commercial_') or intent in ['purchase', 'comparison', 'recommendation', 'review']:
            return 'commercial'
        
        # SEO意圖類別
        elif intent in self.seo_patterns:
            return 'seo'
        
        # 信息查詢意圖
        elif intent in ['information', 'troubleshooting']:
            return 'informational'
        
        # 導航意圖
        elif intent in ['navigation', 'search']:
            return 'navigational'
        
        # 默認類別
        else:
            return 'other'