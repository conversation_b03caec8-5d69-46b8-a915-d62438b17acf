"""
簡化索引管理系統
不依賴數據庫配置，用於演示和測試
"""

import os
import json
import pickle
import logging
import asyncio
from typing import List, Dict, Optional, Any
from datetime import datetime, timedelta
from pathlib import Path
import hashlib

# 基礎依賴
import numpy as np

logger = logging.getLogger(__name__)

class SimpleIndexManager:
    """簡化索引管理器 - 不依賴數據庫配置"""
    
    def __init__(self, 
                 index_dir: str = "data/indices",
                 max_index_age_hours: int = 24,
                 min_queries_for_rebuild: int = 100):
        
        self.index_dir = Path(index_dir)
        self.max_index_age_hours = max_index_age_hours
        self.min_queries_for_rebuild = min_queries_for_rebuild
        
        # 確保目錄存在
        self.index_dir.mkdir(parents=True, exist_ok=True)
        
        # 索引元數據
        self.metadata_file = self.index_dir / "index_metadata.json"
        self.metadata = self._load_metadata()
        
        # 狀態
        self.last_check_time = None
        self.is_building = False
        
        # 模擬數據
        self.sample_queries = self._generate_sample_queries()
        
    def _load_metadata(self) -> Dict[str, Any]:
        """載入索引元數據"""
        try:
            if self.metadata_file.exists():
                with open(self.metadata_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            logger.error(f"載入索引元數據失敗: {e}")
        
        return {
            "version": "1.0",
            "created_at": None,
            "updated_at": None,
            "total_documents": 0,
            "index_hash": None,
            "last_query_id": None,
            "rebuild_count": 0
        }
    
    def _save_metadata(self):
        """保存索引元數據"""
        try:
            with open(self.metadata_file, 'w', encoding='utf-8') as f:
                json.dump(self.metadata, f, ensure_ascii=False, indent=2, default=str)
        except Exception as e:
            logger.error(f"保存索引元數據失敗: {e}")
    
    def _generate_sample_queries(self) -> List[Dict[str, Any]]:
        """生成示例查詢數據"""
        sample_data = [
            {
                'id': '1',
                'content': 'AI人工智能在台灣中小企業的應用案例與實施策略分析',
                'query_text': 'AI人工智能在台灣中小企業的應用案例與實施策略分析',
                'query_type': 'long_tail',
                'intent': 'informational',
                'semantic_category': 'technology',
                'longtail_score': 0.85,
                'confidence': 0.92,
                'keywords': ['AI', '人工智能', '台灣', '中小企業', '應用案例', '實施策略'],
                'language': 'zh',
                'word_count': 20,
                'character_count': 25
            },
            {
                'id': '2',
                'content': '如何選擇最適合小型企業的AI客服系統並進行有效整合',
                'query_text': '如何選擇最適合小型企業的AI客服系統並進行有效整合',
                'query_type': 'long_tail',
                'intent': 'commercial',
                'semantic_category': 'business',
                'longtail_score': 0.78,
                'confidence': 0.88,
                'keywords': ['AI客服系統', '小型企業', '選擇', '整合'],
                'language': 'zh',
                'word_count': 18,
                'character_count': 23
            },
            {
                'id': '3',
                'content': '台北最好的AI寫作工具推薦與價格比較分析報告',
                'query_text': '台北最好的AI寫作工具推薦與價格比較分析報告',
                'query_type': 'long_tail',
                'intent': 'commercial',
                'semantic_category': 'technology',
                'longtail_score': 0.72,
                'confidence': 0.85,
                'keywords': ['台北', 'AI寫作工具', '推薦', '價格比較'],
                'language': 'zh',
                'word_count': 16,
                'character_count': 20
            },
            {
                'id': '4',
                'content': 'SEO優化',
                'query_text': 'SEO優化',
                'query_type': 'head',
                'intent': 'informational',
                'semantic_category': 'marketing',
                'longtail_score': 0.15,
                'confidence': 0.95,
                'keywords': ['SEO', '優化'],
                'language': 'zh',
                'word_count': 2,
                'character_count': 5
            },
            {
                'id': '5',
                'content': '機器學習演算法在電商推薦系統中的應用與性能優化策略',
                'query_text': '機器學習演算法在電商推薦系統中的應用與性能優化策略',
                'query_type': 'long_tail',
                'intent': 'informational',
                'semantic_category': 'technology',
                'longtail_score': 0.88,
                'confidence': 0.90,
                'keywords': ['機器學習', '演算法', '電商', '推薦系統', '性能優化'],
                'language': 'zh',
                'word_count': 19,
                'character_count': 24
            }
        ]
        return sample_data
    
    async def check_index_status(self) -> Dict[str, Any]:
        """檢查索引狀態"""
        try:
            # 檢查索引文件是否存在
            index_files = list(self.index_dir.glob("*.index"))
            mapping_files = list(self.index_dir.glob("*_mapping.pkl"))
            
            index_exists = len(index_files) > 0 and len(mapping_files) > 0
            
            # 檢查索引年齡
            index_age_hours = 0
            if self.metadata.get("updated_at"):
                updated_at = datetime.fromisoformat(self.metadata["updated_at"])
                index_age_hours = (datetime.now() - updated_at).total_seconds() / 3600
            
            # 模擬新查詢數量
            new_queries_count = len(self.sample_queries) - self.metadata.get("total_documents", 0)
            
            # 判斷是否需要重建
            needs_rebuild = (
                not index_exists or
                index_age_hours > self.max_index_age_hours or
                new_queries_count >= self.min_queries_for_rebuild
            )
            
            return {
                "index_exists": index_exists,
                "index_age_hours": round(index_age_hours, 2),
                "new_queries_count": max(0, new_queries_count),
                "needs_rebuild": needs_rebuild,
                "total_documents": self.metadata.get("total_documents", 0),
                "last_updated": self.metadata.get("updated_at"),
                "is_building": self.is_building
            }
            
        except Exception as e:
            logger.error(f"檢查索引狀態失敗: {e}")
            return {
                "index_exists": False,
                "needs_rebuild": True,
                "error": str(e)
            }
    
    async def get_queries_for_indexing(self, limit: int = 10000) -> List[Dict[str, Any]]:
        """獲取用於索引的查詢數據"""
        try:
            # 返回示例數據
            documents = self.sample_queries[:limit]
            logger.info(f"獲取到 {len(documents)} 個查詢用於索引")
            return documents
                
        except Exception as e:
            logger.error(f"獲取索引查詢數據失敗: {e}")
            return []
    
    async def build_index_async(self, force: bool = False) -> Dict[str, Any]:
        """異步構建索引"""
        if self.is_building and not force:
            return {"status": "already_building", "message": "索引正在構建中"}
        
        self.is_building = True
        start_time = datetime.now()
        
        try:
            # 檢查是否需要重建
            status = await self.check_index_status()
            if not status.get("needs_rebuild") and not force:
                self.is_building = False
                return {
                    "status": "no_rebuild_needed",
                    "message": "索引不需要重建",
                    "index_status": status
                }
            
            # 獲取查詢數據
            logger.info("開始獲取查詢數據...")
            documents = await self.get_queries_for_indexing()
            
            if not documents:
                self.is_building = False
                return {
                    "status": "no_data",
                    "message": "沒有可用的查詢數據"
                }
            
            # 計算數據哈希
            data_hash = self._calculate_data_hash(documents)
            
            # 檢查是否需要重建（基於數據變化）
            if (self.metadata.get("index_hash") == data_hash and 
                not force and 
                status.get("index_exists")):
                self.is_building = False
                return {
                    "status": "no_changes",
                    "message": "數據沒有變化，無需重建索引"
                }
            
            # 構建簡化索引
            logger.info(f"開始構建簡化索引，文檔數量: {len(documents)}")
            index_result = await self._build_simple_index(documents)
            
            # 更新元數據
            self.metadata.update({
                "updated_at": datetime.now().isoformat(),
                "total_documents": len(documents),
                "index_hash": data_hash,
                "rebuild_count": self.metadata.get("rebuild_count", 0) + 1
            })
            
            if not self.metadata.get("created_at"):
                self.metadata["created_at"] = self.metadata["updated_at"]
            
            self._save_metadata()
            
            end_time = datetime.now()
            build_time = (end_time - start_time).total_seconds()
            
            result = {
                "status": "success",
                "message": f"索引構建完成，處理了 {len(documents)} 個文檔",
                "documents_count": len(documents),
                "build_time_seconds": round(build_time, 2),
                "index_type": "simple",
                **index_result
            }
            
            logger.info(f"索引構建完成: {result}")
            return result
            
        except Exception as e:
            logger.error(f"索引構建失敗: {e}")
            return {
                "status": "error",
                "message": f"索引構建失敗: {str(e)}"
            }
        finally:
            self.is_building = False
    
    def _calculate_data_hash(self, documents: List[Dict]) -> str:
        """計算數據哈希值"""
        try:
            # 創建數據的簡化表示用於哈希
            data_for_hash = []
            for doc in documents:
                data_for_hash.append({
                    'id': doc['id'],
                    'content': doc['content'],
                    'query_type': doc['query_type'],
                    'longtail_score': doc['longtail_score']
                })
            
            # 排序確保一致性
            data_for_hash.sort(key=lambda x: x['id'])
            
            # 計算哈希
            data_str = json.dumps(data_for_hash, sort_keys=True, ensure_ascii=False)
            return hashlib.md5(data_str.encode('utf-8')).hexdigest()
            
        except Exception as e:
            logger.error(f"計算數據哈希失敗: {e}")
            return str(datetime.now().timestamp())
    
    async def _build_simple_index(self, documents: List[Dict]) -> Dict[str, Any]:
        """構建簡化索引（基於關鍵詞和統計特徵）"""
        try:
            # 創建簡化的索引結構
            index_data = {
                'documents': documents,
                'keyword_index': {},
                'category_index': {},
                'type_index': {},
                'score_ranges': {}
            }
            
            # 構建關鍵詞索引
            for i, doc in enumerate(documents):
                doc_id = doc['id']
                
                # 關鍵詞索引
                keywords = doc.get('keywords', [])
                for keyword in keywords:
                    if keyword not in index_data['keyword_index']:
                        index_data['keyword_index'][keyword] = []
                    index_data['keyword_index'][keyword].append(i)
                
                # 類別索引
                category = doc.get('semantic_category', 'unknown')
                if category not in index_data['category_index']:
                    index_data['category_index'][category] = []
                index_data['category_index'][category].append(i)
                
                # 類型索引
                query_type = doc.get('query_type', 'unknown')
                if query_type not in index_data['type_index']:
                    index_data['type_index'][query_type] = []
                index_data['type_index'][query_type].append(i)
            
            # 計算分數範圍
            scores = [doc.get('longtail_score', 0) for doc in documents]
            index_data['score_ranges'] = {
                'min': min(scores) if scores else 0,
                'max': max(scores) if scores else 1,
                'avg': sum(scores) / len(scores) if scores else 0.5
            }
            
            # 保存索引
            index_file = self.index_dir / "simple_index.pkl"
            with open(index_file, 'wb') as f:
                pickle.dump(index_data, f)
            
            logger.info(f"簡化索引構建完成: {len(documents)} 個文檔")
            
            return {
                "index_file": str(index_file),
                "keyword_count": len(index_data['keyword_index']),
                "category_count": len(index_data['category_index']),
                "type_count": len(index_data['type_index'])
            }
            
        except Exception as e:
            logger.error(f"構建簡化索引失敗: {e}")
            raise
    
    async def search_simple_index(self, 
                                query: str, 
                                k: int = 10,
                                filters: Optional[Dict] = None) -> List[Dict]:
        """在簡化索引中搜索"""
        try:
            index_file = self.index_dir / "simple_index.pkl"
            if not index_file.exists():
                return []
            
            # 載入索引
            with open(index_file, 'rb') as f:
                index_data = pickle.load(f)
            
            documents = index_data['documents']
            keyword_index = index_data['keyword_index']
            
            # 簡單的關鍵詞匹配
            query_words = query.lower().split()
            candidate_indices = set()
            
            # 基於關鍵詞匹配
            for word in query_words:
                for keyword, doc_indices in keyword_index.items():
                    if word in keyword.lower():
                        candidate_indices.update(doc_indices)
            
            # 如果沒有關鍵詞匹配，使用文本包含匹配
            if not candidate_indices:
                for i, doc in enumerate(documents):
                    content = doc.get('content', '').lower()
                    if any(word in content for word in query_words):
                        candidate_indices.add(i)
            
            # 應用過濾器
            if filters:
                filtered_indices = set()
                for idx in candidate_indices:
                    doc = documents[idx]
                    match = True
                    
                    if 'query_type' in filters and doc.get('query_type') != filters['query_type']:
                        match = False
                    if 'semantic_category' in filters and doc.get('semantic_category') != filters['semantic_category']:
                        match = False
                    if 'min_score' in filters and doc.get('longtail_score', 0) < filters['min_score']:
                        match = False
                    
                    if match:
                        filtered_indices.add(idx)
                
                candidate_indices = filtered_indices
            
            # 計算相似度分數並排序
            results = []
            for idx in candidate_indices:
                doc = documents[idx]
                
                # 簡單的相似度計算
                content = doc.get('content', '').lower()
                score = sum(1 for word in query_words if word in content) / len(query_words)
                
                # 加權長尾分數
                longtail_score = doc.get('longtail_score', 0)
                final_score = score * 0.7 + longtail_score * 0.3
                
                results.append({
                    'id': doc['id'],
                    'query': doc['content'],
                    'score': final_score,
                    'query_type': doc.get('query_type', ''),
                    'intent': doc.get('intent', ''),
                    'semantic_category': doc.get('semantic_category', ''),
                    'longtail_score': longtail_score
                })
            
            # 排序並返回top-k
            results.sort(key=lambda x: x['score'], reverse=True)
            return results[:k]
            
        except Exception as e:
            logger.error(f"簡化索引搜索失敗: {e}")
            return []
    
    async def get_index_stats(self) -> Dict[str, Any]:
        """獲取索引統計信息"""
        try:
            status = await self.check_index_status()
            
            # 載入索引統計
            index_stats = {}
            index_file = self.index_dir / "simple_index.pkl"
            if index_file.exists():
                with open(index_file, 'rb') as f:
                    index_data = pickle.load(f)
                    
                index_stats = {
                    "total_documents": len(index_data['documents']),
                    "unique_keywords": len(index_data['keyword_index']),
                    "categories": len(index_data['category_index']),
                    "query_types": len(index_data['type_index']),
                    "score_range": index_data['score_ranges']
                }
            
            return {
                **status,
                **index_stats,
                "metadata": self.metadata
            }
            
        except Exception as e:
            logger.error(f"獲取索引統計失敗: {e}")
            return {"error": str(e)}
