"""
長尾查詢識別系統 - 核心分析器
提供長尾查詢的識別、分類和分析功能
"""

import re
import json
import asyncio
import logging
from typing import List, Dict, Any, Optional, Tuple, Union
from dataclasses import dataclass, asdict
from enum import Enum
from datetime import datetime
import numpy as np

# 配置日誌
logger = logging.getLogger(__name__)

class QueryType(Enum):
    """查詢類型枚舉"""
    HEAD = "head"           # 頭部查詢 (高頻)
    MIDDLE = "middle"       # 中部查詢 (中頻)
    LONG_TAIL = "long_tail" # 長尾查詢 (低頻)

class QueryIntent(Enum):
    """查詢意圖枚舉"""
    INFORMATIONAL = "informational"     # 信息型
    NAVIGATIONAL = "navigational"       # 導航型
    TRANSACTIONAL = "transactional"     # 交易型
    COMMERCIAL = "commercial"           # 商業型
    LOCAL = "local"                     # 本地型

class QueryComplexity(Enum):
    """查詢複雜度枚舉"""
    SIMPLE = "simple"       # 簡單查詢 (1-2個詞)
    MODERATE = "moderate"   # 中等查詢 (3-5個詞)
    COMPLEX = "complex"     # 複雜查詢 (6+個詞)

@dataclass
class QueryFeatures:
    """查詢特徵"""
    length: int                    # 查詢長度
    word_count: int               # 詞數
    character_count: int          # 字符數
    has_question_words: bool      # 是否包含疑問詞
    has_brand_names: bool         # 是否包含品牌名
    has_location: bool            # 是否包含地理位置
    has_numbers: bool             # 是否包含數字
    has_special_chars: bool       # 是否包含特殊字符
    language: str                 # 語言
    specificity_score: float      # 特異性分數
    commercial_intent_score: float # 商業意圖分數

@dataclass
class LongTailAnalysisResult:
    """長尾查詢分析結果"""
    query: str
    query_type: QueryType
    intent: QueryIntent
    complexity: QueryComplexity
    features: QueryFeatures
    longtail_score: float         # 長尾分數 (0-1)
    confidence: float             # 置信度
    keywords: List[str]           # 提取的關鍵詞
    semantic_category: str        # 語義類別
    search_volume_estimate: int   # 搜索量估計
    competition_level: str        # 競爭程度
    optimization_suggestions: List[str]  # 優化建議
    timestamp: datetime

class LongTailQueryAnalyzer:
    """長尾查詢分析器"""
    
    def __init__(self):
        self.question_words_zh = {
            '什麼', '如何', '怎麼', '為什麼', '哪裡', '何時', '誰', '哪個', '多少'
        }
        self.question_words_en = {
            'what', 'how', 'why', 'where', 'when', 'who', 'which', 'how much', 'how many'
        }
        
        self.commercial_keywords_zh = {
            '買', '購買', '價格', '便宜', '優惠', '折扣', '比較', '評價', '推薦'
        }
        self.commercial_keywords_en = {
            'buy', 'purchase', 'price', 'cheap', 'discount', 'compare', 'review', 'best'
        }
        
        self.location_keywords_zh = {
            '附近', '台北', '台中', '高雄', '台南', '桃園', '新竹', '台灣'
        }
        self.location_keywords_en = {
            'near', 'nearby', 'location', 'address', 'taipei', 'taiwan'
        }

    async def analyze_query(self, query: str) -> LongTailAnalysisResult:
        """分析單個查詢"""
        try:
            # 提取基礎特徵
            features = self._extract_features(query)
            
            # 分類查詢類型
            query_type = self._classify_query_type(query, features)
            
            # 識別查詢意圖
            intent = self._detect_intent(query, features)
            
            # 評估複雜度
            complexity = self._assess_complexity(features)
            
            # 計算長尾分數
            longtail_score = self._calculate_longtail_score(query, features)
            
            # 提取關鍵詞
            keywords = self._extract_keywords(query)
            
            # 語義分類
            semantic_category = self._categorize_semantically(query, keywords)
            
            # 估計搜索量和競爭程度
            search_volume_estimate = self._estimate_search_volume(query, features)
            competition_level = self._assess_competition(query, features)
            
            # 生成優化建議
            optimization_suggestions = self._generate_suggestions(
                query, query_type, intent, features
            )
            
            # 計算置信度
            confidence = self._calculate_confidence(features, longtail_score)
            
            return LongTailAnalysisResult(
                query=query,
                query_type=query_type,
                intent=intent,
                complexity=complexity,
                features=features,
                longtail_score=longtail_score,
                confidence=confidence,
                keywords=keywords,
                semantic_category=semantic_category,
                search_volume_estimate=search_volume_estimate,
                competition_level=competition_level,
                optimization_suggestions=optimization_suggestions,
                timestamp=datetime.utcnow()
            )
            
        except Exception as e:
            logger.error(f"查詢分析失敗: {query}, 錯誤: {e}")
            raise

    def _extract_features(self, query: str) -> QueryFeatures:
        """提取查詢特徵"""
        # 基礎統計
        length = len(query)
        words = query.split()
        word_count = len(words)
        character_count = len(query.replace(' ', ''))
        
        # 語言檢測
        language = self._detect_language(query)
        
        # 特徵檢測
        has_question_words = self._has_question_words(query, language)
        has_brand_names = self._has_brand_names(query)
        has_location = self._has_location(query, language)
        has_numbers = bool(re.search(r'\d', query))
        has_special_chars = bool(re.search(r'[!@#$%^&*(),.?":{}|<>]', query))
        
        # 計算特異性分數
        specificity_score = self._calculate_specificity(query, words)
        
        # 計算商業意圖分數
        commercial_intent_score = self._calculate_commercial_intent(query, language)
        
        return QueryFeatures(
            length=length,
            word_count=word_count,
            character_count=character_count,
            has_question_words=has_question_words,
            has_brand_names=has_brand_names,
            has_location=has_location,
            has_numbers=has_numbers,
            has_special_chars=has_special_chars,
            language=language,
            specificity_score=specificity_score,
            commercial_intent_score=commercial_intent_score
        )

    def _classify_query_type(self, query: str, features: QueryFeatures) -> QueryType:
        """分類查詢類型"""
        # 基於長度和特異性的簡單分類
        if features.word_count <= 2 and features.specificity_score < 0.3:
            return QueryType.HEAD
        elif features.word_count <= 4 and features.specificity_score < 0.6:
            return QueryType.MIDDLE
        else:
            return QueryType.LONG_TAIL

    def _detect_intent(self, query: str, features: QueryFeatures) -> QueryIntent:
        """檢測查詢意圖"""
        query_lower = query.lower()
        
        # 問題意圖
        if features.has_question_words:
            return QueryIntent.INFORMATIONAL
        
        # 商業意圖
        if features.commercial_intent_score > 0.5:
            return QueryIntent.COMMERCIAL
        
        # 交易意圖
        transaction_patterns = ['買', '購買', '訂購', 'buy', 'purchase', 'order']
        if any(pattern in query_lower for pattern in transaction_patterns):
            return QueryIntent.TRANSACTIONAL
        
        # 本地意圖
        if features.has_location:
            return QueryIntent.LOCAL
        
        # 導航意圖
        navigation_patterns = ['登入', '註冊', '首頁', 'login', 'register', 'home']
        if any(pattern in query_lower for pattern in navigation_patterns):
            return QueryIntent.NAVIGATIONAL
        
        return QueryIntent.INFORMATIONAL

    def _assess_complexity(self, features: QueryFeatures) -> QueryComplexity:
        """評估查詢複雜度"""
        if features.word_count <= 2:
            return QueryComplexity.SIMPLE
        elif features.word_count <= 5:
            return QueryComplexity.MODERATE
        else:
            return QueryComplexity.COMPLEX

    def _calculate_longtail_score(self, query: str, features: QueryFeatures) -> float:
        """計算長尾分數"""
        score = 0.0
        
        # 長度因子 (更長的查詢更可能是長尾)
        length_factor = min(features.word_count / 10.0, 1.0)
        score += length_factor * 0.3
        
        # 特異性因子
        score += features.specificity_score * 0.4
        
        # 複雜性因子
        if features.has_question_words:
            score += 0.1
        if features.has_numbers:
            score += 0.1
        if features.has_location:
            score += 0.1
        
        return min(score, 1.0)

    def _detect_language(self, query: str) -> str:
        """檢測查詢語言"""
        # 簡單的語言檢測
        chinese_chars = len(re.findall(r'[\u4e00-\u9fff]', query))
        total_chars = len(query.replace(' ', ''))
        
        if total_chars == 0:
            return 'unknown'
        
        chinese_ratio = chinese_chars / total_chars
        return 'zh' if chinese_ratio > 0.5 else 'en'

    def _has_question_words(self, query: str, language: str) -> bool:
        """檢查是否包含疑問詞"""
        query_lower = query.lower()
        
        if language == 'zh':
            return any(word in query for word in self.question_words_zh)
        else:
            return any(word in query_lower for word in self.question_words_en)

    def _has_brand_names(self, query: str) -> bool:
        """檢查是否包含品牌名 (簡化版)"""
        # 這裡可以擴展為更完整的品牌名數據庫
        common_brands = {
            'apple', 'google', 'microsoft', 'amazon', 'facebook',
            '蘋果', '谷歌', '微軟', '亞馬遜', '臉書'
        }
        query_lower = query.lower()
        return any(brand in query_lower for brand in common_brands)

    def _has_location(self, query: str, language: str) -> bool:
        """檢查是否包含地理位置"""
        query_lower = query.lower()
        
        if language == 'zh':
            return any(loc in query for loc in self.location_keywords_zh)
        else:
            return any(loc in query_lower for loc in self.location_keywords_en)

    def _calculate_specificity(self, query: str, words: List[str]) -> float:
        """計算特異性分數"""
        # 基於詞彙多樣性和長度的簡單計算
        unique_words = len(set(words))
        total_words = len(words)
        
        if total_words == 0:
            return 0.0
        
        diversity = unique_words / total_words
        length_factor = min(total_words / 8.0, 1.0)
        
        return (diversity + length_factor) / 2.0

    def _calculate_commercial_intent(self, query: str, language: str) -> float:
        """計算商業意圖分數"""
        query_lower = query.lower()
        score = 0.0
        
        if language == 'zh':
            commercial_words = self.commercial_keywords_zh
        else:
            commercial_words = self.commercial_keywords_en
        
        for word in commercial_words:
            if word in query_lower:
                score += 0.2
        
        return min(score, 1.0)

    def _extract_keywords(self, query: str) -> List[str]:
        """提取關鍵詞"""
        # 簡單的關鍵詞提取
        words = re.findall(r'\b\w+\b', query.lower())
        
        # 過濾停用詞
        stop_words = {
            '的', '是', '在', '有', '和', '或', '但', '與', '或者',
            'the', 'is', 'in', 'and', 'or', 'but', 'with', 'for', 'a', 'an'
        }
        
        keywords = [word for word in words if word not in stop_words and len(word) > 1]
        return keywords[:10]  # 限制數量

    def _categorize_semantically(self, query: str, keywords: List[str]) -> str:
        """語義分類"""
        # 簡化的語義分類
        categories = {
            'technology': ['電腦', '手機', '軟體', 'computer', 'phone', 'software'],
            'shopping': ['買', '購買', '商品', 'buy', 'shop', 'product'],
            'health': ['健康', '醫療', '藥物', 'health', 'medical', 'medicine'],
            'education': ['學習', '教育', '課程', 'learn', 'education', 'course'],
            'travel': ['旅遊', '酒店', '機票', 'travel', 'hotel', 'flight']
        }
        
        query_lower = query.lower()
        for category, terms in categories.items():
            if any(term in query_lower for term in terms):
                return category
        
        return 'general'

    def _estimate_search_volume(self, query: str, features: QueryFeatures) -> int:
        """估計搜索量"""
        # 基於查詢特徵的簡單估計
        base_volume = 1000
        
        # 長尾查詢通常搜索量較低
        if features.word_count > 4:
            base_volume //= 2
        if features.word_count > 6:
            base_volume //= 2
        
        # 特異性越高，搜索量越低
        volume_reduction = int(base_volume * features.specificity_score)
        estimated_volume = max(base_volume - volume_reduction, 10)
        
        return estimated_volume

    def _assess_competition(self, query: str, features: QueryFeatures) -> str:
        """評估競爭程度"""
        if features.commercial_intent_score > 0.7:
            return 'high'
        elif features.commercial_intent_score > 0.3:
            return 'medium'
        else:
            return 'low'

    def _generate_suggestions(
        self, 
        query: str, 
        query_type: QueryType, 
        intent: QueryIntent, 
        features: QueryFeatures
    ) -> List[str]:
        """生成優化建議"""
        suggestions = []
        
        if query_type == QueryType.LONG_TAIL:
            suggestions.append("這是一個長尾查詢，競爭較低，適合精準定位")
            suggestions.append("建議創建專門針對此查詢的內容頁面")
        
        if intent == QueryIntent.COMMERCIAL:
            suggestions.append("具有商業意圖，建議優化產品頁面和購買流程")
        
        if features.has_location:
            suggestions.append("包含地理位置，建議優化本地SEO")
        
        if features.has_question_words:
            suggestions.append("問題型查詢，建議使用FAQ格式回答")
        
        return suggestions

    def _calculate_confidence(self, features: QueryFeatures, longtail_score: float) -> float:
        """計算置信度"""
        # 基於特徵完整性和分數一致性計算置信度
        feature_completeness = 0.8  # 簡化計算
        score_consistency = 0.9     # 簡化計算
        
        return (feature_completeness + score_consistency) / 2.0

class BatchLongTailAnalyzer:
    """批量長尾查詢分析器"""

    def __init__(self, analyzer: LongTailQueryAnalyzer):
        self.analyzer = analyzer
        self.batch_size = 100

    async def analyze_batch(
        self,
        queries: List[str],
        progress_callback: Optional[callable] = None
    ) -> List[LongTailAnalysisResult]:
        """批量分析查詢"""
        results = []
        total = len(queries)

        for i in range(0, total, self.batch_size):
            batch = queries[i:i + self.batch_size]
            batch_results = await asyncio.gather(
                *[self.analyzer.analyze_query(query) for query in batch],
                return_exceptions=True
            )

            # 處理結果和異常
            for j, result in enumerate(batch_results):
                if isinstance(result, Exception):
                    logger.error(f"查詢分析失敗: {batch[j]}, 錯誤: {result}")
                    continue
                results.append(result)

            # 進度回調
            if progress_callback:
                progress = min((i + len(batch)) / total, 1.0)
                await progress_callback(progress, len(results))

        return results

    def generate_batch_report(self, results: List[LongTailAnalysisResult]) -> Dict[str, Any]:
        """生成批量分析報告"""
        if not results:
            return {"error": "沒有分析結果"}

        # 統計信息
        total_queries = len(results)
        longtail_queries = [r for r in results if r.query_type == QueryType.LONG_TAIL]
        longtail_count = len(longtail_queries)
        longtail_percentage = (longtail_count / total_queries) * 100

        # 意圖分布
        intent_distribution = {}
        for result in results:
            intent = result.intent.value
            intent_distribution[intent] = intent_distribution.get(intent, 0) + 1

        # 複雜度分布
        complexity_distribution = {}
        for result in results:
            complexity = result.complexity.value
            complexity_distribution[complexity] = complexity_distribution.get(complexity, 0) + 1

        # 語義類別分布
        category_distribution = {}
        for result in results:
            category = result.semantic_category
            category_distribution[category] = category_distribution.get(category, 0) + 1

        # 平均分數
        avg_longtail_score = np.mean([r.longtail_score for r in results])
        avg_confidence = np.mean([r.confidence for r in results])

        # 高價值長尾查詢 (長尾分數高且商業意圖強)
        high_value_longtail = [
            r for r in longtail_queries
            if r.longtail_score > 0.7 and r.features.commercial_intent_score > 0.5
        ]

        return {
            "summary": {
                "total_queries": total_queries,
                "longtail_queries": longtail_count,
                "longtail_percentage": round(longtail_percentage, 2),
                "avg_longtail_score": round(avg_longtail_score, 3),
                "avg_confidence": round(avg_confidence, 3)
            },
            "distributions": {
                "intent": intent_distribution,
                "complexity": complexity_distribution,
                "semantic_category": category_distribution
            },
            "high_value_longtail": [
                {
                    "query": r.query,
                    "longtail_score": r.longtail_score,
                    "commercial_score": r.features.commercial_intent_score,
                    "search_volume_estimate": r.search_volume_estimate,
                    "competition_level": r.competition_level
                }
                for r in high_value_longtail[:20]  # 前20個
            ],
            "recommendations": self._generate_batch_recommendations(results)
        }

    def _generate_batch_recommendations(self, results: List[LongTailAnalysisResult]) -> List[str]:
        """生成批量建議"""
        recommendations = []

        longtail_queries = [r for r in results if r.query_type == QueryType.LONG_TAIL]
        longtail_percentage = (len(longtail_queries) / len(results)) * 100

        if longtail_percentage > 60:
            recommendations.append("長尾查詢比例較高，建議重點優化長尾關鍵詞策略")

        commercial_queries = [r for r in results if r.features.commercial_intent_score > 0.5]
        if len(commercial_queries) > len(results) * 0.3:
            recommendations.append("商業意圖查詢較多，建議優化產品頁面和轉換流程")

        question_queries = [r for r in results if r.features.has_question_words]
        if len(question_queries) > len(results) * 0.4:
            recommendations.append("問題型查詢較多，建議增加FAQ和問答內容")

        local_queries = [r for r in results if r.features.has_location]
        if len(local_queries) > len(results) * 0.2:
            recommendations.append("本地查詢較多，建議加強本地SEO優化")

        return recommendations
