"""
監控集成系統
實現Prometheus指標、性能監控和分析準確度追蹤
"""

import time
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
from collections import defaultdict, deque
import json
from pathlib import Path
import asyncio

logger = logging.getLogger(__name__)

class MonitoringSystem:
    """監控系統 - 收集和報告系統性能指標"""
    
    def __init__(self, metrics_dir: str = "data/metrics"):
        self.metrics_dir = Path(metrics_dir)
        self.metrics_dir.mkdir(parents=True, exist_ok=True)
        
        # 指標存儲
        self.metrics = defaultdict(list)
        self.counters = defaultdict(int)
        self.gauges = defaultdict(float)
        self.histograms = defaultdict(list)
        
        # 性能指標
        self.request_durations = deque(maxlen=10000)
        self.error_counts = defaultdict(int)
        self.success_counts = defaultdict(int)
        
        # 分析準確度指標
        self.accuracy_metrics = deque(maxlen=1000)
        self.prediction_confidence = deque(maxlen=1000)
        
        # 系統健康指標
        self.system_health = {
            'status': 'healthy',
            'last_check': datetime.now(),
            'uptime_start': datetime.now(),
            'total_requests': 0,
            'total_errors': 0
        }
        
        # 自動保存間隔（秒）
        self.save_interval = 300  # 5分鐘
        self.last_save = datetime.now()
        
    def record_request(self, 
                      endpoint: str, 
                      duration: float, 
                      status_code: int,
                      error: Optional[str] = None):
        """記錄請求指標"""
        timestamp = datetime.now()
        
        # 記錄請求持續時間
        self.request_durations.append({
            'endpoint': endpoint,
            'duration': duration,
            'timestamp': timestamp,
            'status_code': status_code
        })
        
        # 更新計數器
        self.counters[f'requests_total_{endpoint}'] += 1
        self.system_health['total_requests'] += 1
        
        if status_code >= 400:
            self.error_counts[endpoint] += 1
            self.system_health['total_errors'] += 1
            if error:
                self.counters[f'errors_{endpoint}_{error}'] += 1
        else:
            self.success_counts[endpoint] += 1
        
        # 更新響應時間直方圖
        self.histograms[f'response_time_{endpoint}'].append(duration)
        
        # 保持直方圖大小
        if len(self.histograms[f'response_time_{endpoint}']) > 1000:
            self.histograms[f'response_time_{endpoint}'] = self.histograms[f'response_time_{endpoint}'][-1000:]
    
    def record_analysis_accuracy(self, 
                                predicted_class: str,
                                actual_class: Optional[str] = None,
                                confidence: float = 0.0):
        """記錄分析準確度指標"""
        timestamp = datetime.now()
        
        # 記錄預測置信度
        self.prediction_confidence.append({
            'predicted_class': predicted_class,
            'confidence': confidence,
            'timestamp': timestamp
        })
        
        # 如果有實際標籤，計算準確度
        if actual_class is not None:
            is_correct = predicted_class == actual_class
            self.accuracy_metrics.append({
                'predicted': predicted_class,
                'actual': actual_class,
                'correct': is_correct,
                'confidence': confidence,
                'timestamp': timestamp
            })
            
            # 更新準確度計數器
            if is_correct:
                self.counters[f'correct_predictions_{predicted_class}'] += 1
            else:
                self.counters[f'incorrect_predictions_{predicted_class}'] += 1
    
    def record_system_metric(self, metric_name: str, value: float, metric_type: str = 'gauge'):
        """記錄系統指標"""
        timestamp = datetime.now()
        
        if metric_type == 'counter':
            self.counters[metric_name] += value
        elif metric_type == 'gauge':
            self.gauges[metric_name] = value
        elif metric_type == 'histogram':
            self.histograms[metric_name].append(value)
            # 保持直方圖大小
            if len(self.histograms[metric_name]) > 1000:
                self.histograms[metric_name] = self.histograms[metric_name][-1000:]
        
        # 記錄到時間序列
        self.metrics[metric_name].append({
            'value': value,
            'timestamp': timestamp,
            'type': metric_type
        })
        
        # 保持時間序列大小
        if len(self.metrics[metric_name]) > 10000:
            self.metrics[metric_name] = self.metrics[metric_name][-10000:]
    
    def get_prometheus_metrics(self) -> str:
        """生成Prometheus格式的指標"""
        lines = []
        
        # 請求計數器
        for endpoint, count in self.success_counts.items():
            lines.append(f'requests_total{{endpoint="{endpoint}",status="success"}} {count}')
        
        for endpoint, count in self.error_counts.items():
            lines.append(f'requests_total{{endpoint="{endpoint}",status="error"}} {count}')
        
        # 響應時間指標
        for endpoint_metric, durations in self.histograms.items():
            if endpoint_metric.startswith('response_time_') and durations:
                endpoint = endpoint_metric.replace('response_time_', '')
                avg_duration = sum(durations) / len(durations)
                max_duration = max(durations)
                min_duration = min(durations)
                
                lines.append(f'response_time_seconds{{endpoint="{endpoint}",quantile="avg"}} {avg_duration:.6f}')
                lines.append(f'response_time_seconds{{endpoint="{endpoint}",quantile="max"}} {max_duration:.6f}')
                lines.append(f'response_time_seconds{{endpoint="{endpoint}",quantile="min"}} {min_duration:.6f}')
        
        # 準確度指標
        if self.accuracy_metrics:
            recent_accuracy = [m for m in self.accuracy_metrics 
                             if (datetime.now() - m['timestamp']).total_seconds() < 3600]  # 最近1小時
            
            if recent_accuracy:
                total_predictions = len(recent_accuracy)
                correct_predictions = sum(1 for m in recent_accuracy if m['correct'])
                accuracy = correct_predictions / total_predictions
                
                lines.append(f'prediction_accuracy_ratio {accuracy:.6f}')
                lines.append(f'total_predictions {total_predictions}')
                lines.append(f'correct_predictions {correct_predictions}')
        
        # 系統健康指標
        uptime_seconds = (datetime.now() - self.system_health['uptime_start']).total_seconds()
        lines.append(f'system_uptime_seconds {uptime_seconds:.0f}')
        lines.append(f'total_requests {self.system_health["total_requests"]}')
        lines.append(f'total_errors {self.system_health["total_errors"]}')
        
        # 緩存和性能指標
        for metric_name, value in self.gauges.items():
            lines.append(f'{metric_name} {value}')
        
        for metric_name, count in self.counters.items():
            lines.append(f'{metric_name} {count}')
        
        return '\n'.join(lines)
    
    def get_health_status(self) -> Dict[str, Any]:
        """獲取系統健康狀態"""
        current_time = datetime.now()
        uptime = current_time - self.system_health['uptime_start']
        
        # 計算錯誤率
        total_requests = self.system_health['total_requests']
        total_errors = self.system_health['total_errors']
        error_rate = (total_errors / total_requests * 100) if total_requests > 0 else 0
        
        # 計算最近的響應時間統計
        recent_requests = [r for r in self.request_durations 
                          if (current_time - r['timestamp']).total_seconds() < 300]  # 最近5分鐘
        
        if recent_requests:
            avg_response_time = sum(r['duration'] for r in recent_requests) / len(recent_requests)
            max_response_time = max(r['duration'] for r in recent_requests)
        else:
            avg_response_time = max_response_time = 0
        
        # 計算最近的準確度
        recent_accuracy_metrics = [m for m in self.accuracy_metrics 
                                 if (current_time - m['timestamp']).total_seconds() < 3600]  # 最近1小時
        
        if recent_accuracy_metrics:
            accuracy = sum(1 for m in recent_accuracy_metrics if m['correct']) / len(recent_accuracy_metrics)
            avg_confidence = sum(m['confidence'] for m in recent_accuracy_metrics) / len(recent_accuracy_metrics)
        else:
            accuracy = avg_confidence = 0
        
        # 判斷健康狀態
        status = 'healthy'
        issues = []
        
        if error_rate > 10:  # 錯誤率超過10%
            status = 'degraded'
            issues.append(f'高錯誤率: {error_rate:.1f}%')
        
        if avg_response_time > 5:  # 平均響應時間超過5秒
            status = 'degraded'
            issues.append(f'響應時間過長: {avg_response_time:.2f}s')
        
        if accuracy < 0.8 and recent_accuracy_metrics:  # 準確度低於80%
            status = 'degraded'
            issues.append(f'預測準確度低: {accuracy:.1%}')
        
        return {
            'status': status,
            'timestamp': current_time,
            'uptime_seconds': uptime.total_seconds(),
            'uptime_human': str(uptime),
            'total_requests': total_requests,
            'total_errors': total_errors,
            'error_rate_percent': round(error_rate, 2),
            'avg_response_time_ms': round(avg_response_time * 1000, 2),
            'max_response_time_ms': round(max_response_time * 1000, 2),
            'recent_requests_count': len(recent_requests),
            'prediction_accuracy': round(accuracy, 4),
            'avg_prediction_confidence': round(avg_confidence, 4),
            'recent_predictions_count': len(recent_accuracy_metrics),
            'issues': issues
        }
    
    def get_detailed_metrics(self, time_range_hours: int = 24) -> Dict[str, Any]:
        """獲取詳細的指標數據"""
        cutoff_time = datetime.now() - timedelta(hours=time_range_hours)
        
        # 過濾時間範圍內的數據
        filtered_requests = [r for r in self.request_durations if r['timestamp'] > cutoff_time]
        filtered_accuracy = [a for a in self.accuracy_metrics if a['timestamp'] > cutoff_time]
        
        # 按端點統計請求
        endpoint_stats = defaultdict(lambda: {'count': 0, 'errors': 0, 'total_time': 0})
        
        for req in filtered_requests:
            endpoint = req['endpoint']
            endpoint_stats[endpoint]['count'] += 1
            endpoint_stats[endpoint]['total_time'] += req['duration']
            if req['status_code'] >= 400:
                endpoint_stats[endpoint]['errors'] += 1
        
        # 計算端點統計
        for endpoint, stats in endpoint_stats.items():
            if stats['count'] > 0:
                stats['avg_response_time'] = stats['total_time'] / stats['count']
                stats['error_rate'] = stats['errors'] / stats['count']
            else:
                stats['avg_response_time'] = stats['error_rate'] = 0
        
        # 按類別統計準確度
        class_accuracy = defaultdict(lambda: {'total': 0, 'correct': 0})
        
        for acc in filtered_accuracy:
            predicted = acc['predicted']
            class_accuracy[predicted]['total'] += 1
            if acc['correct']:
                class_accuracy[predicted]['correct'] += 1
        
        # 計算類別準確度
        for class_name, stats in class_accuracy.items():
            if stats['total'] > 0:
                stats['accuracy'] = stats['correct'] / stats['total']
            else:
                stats['accuracy'] = 0
        
        return {
            'time_range_hours': time_range_hours,
            'total_requests': len(filtered_requests),
            'total_predictions': len(filtered_accuracy),
            'endpoint_statistics': dict(endpoint_stats),
            'class_accuracy': dict(class_accuracy),
            'overall_accuracy': (sum(1 for a in filtered_accuracy if a['correct']) / len(filtered_accuracy)) if filtered_accuracy else 0,
            'counters': dict(self.counters),
            'gauges': dict(self.gauges)
        }
    
    async def save_metrics_to_file(self):
        """保存指標到文件"""
        try:
            timestamp = datetime.now()
            filename = self.metrics_dir / f"metrics_{timestamp.strftime('%Y%m%d_%H%M%S')}.json"
            
            data = {
                'timestamp': timestamp.isoformat(),
                'health_status': self.get_health_status(),
                'detailed_metrics': self.get_detailed_metrics(),
                'prometheus_metrics': self.get_prometheus_metrics()
            }
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2, default=str)
            
            self.last_save = timestamp
            logger.info(f"指標已保存到: {filename}")
            
        except Exception as e:
            logger.error(f"保存指標失敗: {e}")
    
    async def auto_save_metrics(self):
        """自動保存指標"""
        if (datetime.now() - self.last_save).total_seconds() > self.save_interval:
            await self.save_metrics_to_file()
    
    def reset_metrics(self):
        """重置所有指標"""
        self.metrics.clear()
        self.counters.clear()
        self.gauges.clear()
        self.histograms.clear()
        self.request_durations.clear()
        self.error_counts.clear()
        self.success_counts.clear()
        self.accuracy_metrics.clear()
        self.prediction_confidence.clear()
        
        self.system_health = {
            'status': 'healthy',
            'last_check': datetime.now(),
            'uptime_start': datetime.now(),
            'total_requests': 0,
            'total_errors': 0
        }
        
        logger.info("所有監控指標已重置")

# 全局監控實例
monitoring = MonitoringSystem()

def record_request_metric(endpoint: str, duration: float, status_code: int, error: Optional[str] = None):
    """記錄請求指標的便捷函數"""
    monitoring.record_request(endpoint, duration, status_code, error)

def record_prediction_metric(predicted_class: str, actual_class: Optional[str] = None, confidence: float = 0.0):
    """記錄預測指標的便捷函數"""
    monitoring.record_analysis_accuracy(predicted_class, actual_class, confidence)

def record_system_metric(metric_name: str, value: float, metric_type: str = 'gauge'):
    """記錄系統指標的便捷函數"""
    monitoring.record_system_metric(metric_name, value, metric_type)
