"""
長尾查詢識別系統 - 監控服務
實現Prometheus指標收集、性能監控和模型準確度追蹤
"""

import time
import logging
import asyncio
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from dataclasses import dataclass
from prometheus_client import Counter, Histogram, Gauge, CollectorRegistry, generate_latest
import psutil
import numpy as np

# 配置日誌
logger = logging.getLogger(__name__)

# 創建自定義註冊表
longtail_registry = CollectorRegistry()

# Prometheus 指標定義
longtail_analysis_requests = Counter(
    'longtail_analysis_requests_total',
    'Total number of longtail analysis requests',
    ['method', 'status'],
    registry=longtail_registry
)

longtail_analysis_duration = Histogram(
    'longtail_analysis_duration_seconds',
    'Time spent on longtail analysis',
    ['method'],
    registry=longtail_registry
)

longtail_batch_size = Histogram(
    'longtail_batch_size',
    'Size of batch analysis requests',
    registry=longtail_registry
)

longtail_score_distribution = Histogram(
    'longtail_score_distribution',
    'Distribution of longtail scores',
    buckets=[0.0, 0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1.0],
    registry=longtail_registry
)

longtail_confidence_distribution = Histogram(
    'longtail_confidence_distribution',
    'Distribution of confidence scores',
    buckets=[0.0, 0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1.0],
    registry=longtail_registry
)

longtail_query_types = Counter(
    'longtail_query_types_total',
    'Count of different query types',
    ['query_type'],
    registry=longtail_registry
)

longtail_intents = Counter(
    'longtail_intents_total',
    'Count of different query intents',
    ['intent'],
    registry=longtail_registry
)

longtail_languages = Counter(
    'longtail_languages_total',
    'Count of different languages',
    ['language'],
    registry=longtail_registry
)

longtail_cache_hits = Counter(
    'longtail_cache_hits_total',
    'Number of cache hits',
    ['cache_type'],
    registry=longtail_registry
)

longtail_cache_misses = Counter(
    'longtail_cache_misses_total',
    'Number of cache misses',
    ['cache_type'],
    registry=longtail_registry
)

longtail_model_load_time = Gauge(
    'longtail_model_load_time_seconds',
    'Time taken to load ML models',
    registry=longtail_registry
)

longtail_memory_usage = Gauge(
    'longtail_memory_usage_bytes',
    'Memory usage of longtail service',
    registry=longtail_registry
)

longtail_cpu_usage = Gauge(
    'longtail_cpu_usage_percent',
    'CPU usage of longtail service',
    registry=longtail_registry
)

@dataclass
class PerformanceMetrics:
    """性能指標"""
    timestamp: datetime
    analysis_count: int
    avg_analysis_time: float
    avg_longtail_score: float
    avg_confidence: float
    memory_usage_mb: float
    cpu_usage_percent: float
    cache_hit_rate: float

@dataclass
class ModelAccuracyMetrics:
    """模型準確度指標"""
    timestamp: datetime
    total_predictions: int
    accuracy_score: float
    precision_score: float
    recall_score: float
    f1_score: float
    confusion_matrix: List[List[int]]

class LongTailMonitoringService:
    """長尾查詢監控服務"""
    
    def __init__(self):
        self.start_time = time.time()
        self.performance_history: List[PerformanceMetrics] = []
        self.accuracy_history: List[ModelAccuracyMetrics] = []
        self.analysis_times: List[float] = []
        self.longtail_scores: List[float] = []
        self.confidence_scores: List[float] = []
        self.cache_stats = {
            'hits': 0,
            'misses': 0
        }
        
    def record_analysis_request(self, method: str, status: str):
        """記錄分析請求"""
        longtail_analysis_requests.labels(method=method, status=status).inc()
    
    def record_analysis_duration(self, method: str, duration: float):
        """記錄分析耗時"""
        longtail_analysis_duration.labels(method=method).observe(duration)
        self.analysis_times.append(duration)
        
        # 保持最近1000個記錄
        if len(self.analysis_times) > 1000:
            self.analysis_times = self.analysis_times[-1000:]
    
    def record_batch_size(self, size: int):
        """記錄批量大小"""
        longtail_batch_size.observe(size)
    
    def record_analysis_result(self, result):
        """記錄分析結果"""
        # 記錄長尾分數分布
        longtail_score_distribution.observe(result.longtail_score)
        self.longtail_scores.append(result.longtail_score)
        
        # 記錄置信度分布
        longtail_confidence_distribution.observe(result.confidence)
        self.confidence_scores.append(result.confidence)
        
        # 記錄查詢類型
        longtail_query_types.labels(query_type=result.query_type.value).inc()
        
        # 記錄查詢意圖
        longtail_intents.labels(intent=result.intent.value).inc()
        
        # 記錄語言
        longtail_languages.labels(language=result.features.language).inc()
        
        # 保持最近1000個記錄
        if len(self.longtail_scores) > 1000:
            self.longtail_scores = self.longtail_scores[-1000:]
        if len(self.confidence_scores) > 1000:
            self.confidence_scores = self.confidence_scores[-1000:]
    
    def record_cache_hit(self, cache_type: str):
        """記錄緩存命中"""
        longtail_cache_hits.labels(cache_type=cache_type).inc()
        self.cache_stats['hits'] += 1
    
    def record_cache_miss(self, cache_type: str):
        """記錄緩存未命中"""
        longtail_cache_misses.labels(cache_type=cache_type).inc()
        self.cache_stats['misses'] += 1
    
    def record_model_load_time(self, load_time: float):
        """記錄模型載入時間"""
        longtail_model_load_time.set(load_time)
    
    def update_system_metrics(self):
        """更新系統指標"""
        try:
            # 記錄內存使用
            memory_info = psutil.virtual_memory()
            memory_usage = memory_info.used
            longtail_memory_usage.set(memory_usage)
            
            # 記錄CPU使用率
            cpu_percent = psutil.cpu_percent(interval=1)
            longtail_cpu_usage.set(cpu_percent)
            
        except Exception as e:
            logger.error(f"更新系統指標失敗: {e}")
    
    def get_performance_metrics(self) -> PerformanceMetrics:
        """獲取性能指標"""
        try:
            current_time = datetime.utcnow()
            
            # 計算分析統計
            analysis_count = len(self.analysis_times)
            avg_analysis_time = np.mean(self.analysis_times) if self.analysis_times else 0.0
            avg_longtail_score = np.mean(self.longtail_scores) if self.longtail_scores else 0.0
            avg_confidence = np.mean(self.confidence_scores) if self.confidence_scores else 0.0
            
            # 計算系統資源使用
            memory_info = psutil.virtual_memory()
            memory_usage_mb = memory_info.used / (1024 * 1024)
            cpu_usage_percent = psutil.cpu_percent()
            
            # 計算緩存命中率
            total_cache_requests = self.cache_stats['hits'] + self.cache_stats['misses']
            cache_hit_rate = (self.cache_stats['hits'] / total_cache_requests * 100) if total_cache_requests > 0 else 0.0
            
            metrics = PerformanceMetrics(
                timestamp=current_time,
                analysis_count=analysis_count,
                avg_analysis_time=avg_analysis_time,
                avg_longtail_score=avg_longtail_score,
                avg_confidence=avg_confidence,
                memory_usage_mb=memory_usage_mb,
                cpu_usage_percent=cpu_usage_percent,
                cache_hit_rate=cache_hit_rate
            )
            
            # 保存到歷史記錄
            self.performance_history.append(metrics)
            
            # 保持最近100個記錄
            if len(self.performance_history) > 100:
                self.performance_history = self.performance_history[-100:]
            
            return metrics
            
        except Exception as e:
            logger.error(f"獲取性能指標失敗: {e}")
            return PerformanceMetrics(
                timestamp=datetime.utcnow(),
                analysis_count=0,
                avg_analysis_time=0.0,
                avg_longtail_score=0.0,
                avg_confidence=0.0,
                memory_usage_mb=0.0,
                cpu_usage_percent=0.0,
                cache_hit_rate=0.0
            )
    
    def get_model_accuracy_metrics(self, predictions: List, ground_truth: List) -> ModelAccuracyMetrics:
        """計算模型準確度指標"""
        try:
            from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, confusion_matrix
            
            # 計算各種指標
            accuracy = accuracy_score(ground_truth, predictions)
            precision = precision_score(ground_truth, predictions, average='weighted', zero_division=0)
            recall = recall_score(ground_truth, predictions, average='weighted', zero_division=0)
            f1 = f1_score(ground_truth, predictions, average='weighted', zero_division=0)
            cm = confusion_matrix(ground_truth, predictions).tolist()
            
            metrics = ModelAccuracyMetrics(
                timestamp=datetime.utcnow(),
                total_predictions=len(predictions),
                accuracy_score=accuracy,
                precision_score=precision,
                recall_score=recall,
                f1_score=f1,
                confusion_matrix=cm
            )
            
            # 保存到歷史記錄
            self.accuracy_history.append(metrics)
            
            # 保持最近50個記錄
            if len(self.accuracy_history) > 50:
                self.accuracy_history = self.accuracy_history[-50:]
            
            return metrics
            
        except Exception as e:
            logger.error(f"計算模型準確度失敗: {e}")
            return ModelAccuracyMetrics(
                timestamp=datetime.utcnow(),
                total_predictions=0,
                accuracy_score=0.0,
                precision_score=0.0,
                recall_score=0.0,
                f1_score=0.0,
                confusion_matrix=[]
            )
    
    def get_prometheus_metrics(self) -> str:
        """獲取Prometheus格式的指標"""
        try:
            # 更新系統指標
            self.update_system_metrics()
            
            # 生成Prometheus格式的指標
            return generate_latest(longtail_registry).decode('utf-8')
            
        except Exception as e:
            logger.error(f"生成Prometheus指標失敗: {e}")
            return ""
    
    def get_health_status(self) -> Dict[str, Any]:
        """獲取健康狀態"""
        try:
            current_metrics = self.get_performance_metrics()
            
            # 定義健康閾值
            health_thresholds = {
                'max_avg_analysis_time': 5.0,  # 5秒
                'max_memory_usage_mb': 2048,   # 2GB
                'max_cpu_usage_percent': 80,   # 80%
                'min_cache_hit_rate': 50       # 50%
            }
            
            # 檢查各項指標
            health_checks = {
                'analysis_time': current_metrics.avg_analysis_time <= health_thresholds['max_avg_analysis_time'],
                'memory_usage': current_metrics.memory_usage_mb <= health_thresholds['max_memory_usage_mb'],
                'cpu_usage': current_metrics.cpu_usage_percent <= health_thresholds['max_cpu_usage_percent'],
                'cache_hit_rate': current_metrics.cache_hit_rate >= health_thresholds['min_cache_hit_rate']
            }
            
            # 計算整體健康狀態
            overall_healthy = all(health_checks.values())
            
            return {
                'status': 'healthy' if overall_healthy else 'unhealthy',
                'timestamp': datetime.utcnow().isoformat(),
                'uptime_seconds': time.time() - self.start_time,
                'checks': health_checks,
                'metrics': {
                    'analysis_count': current_metrics.analysis_count,
                    'avg_analysis_time': current_metrics.avg_analysis_time,
                    'avg_longtail_score': current_metrics.avg_longtail_score,
                    'avg_confidence': current_metrics.avg_confidence,
                    'memory_usage_mb': current_metrics.memory_usage_mb,
                    'cpu_usage_percent': current_metrics.cpu_usage_percent,
                    'cache_hit_rate': current_metrics.cache_hit_rate
                },
                'thresholds': health_thresholds
            }
            
        except Exception as e:
            logger.error(f"獲取健康狀態失敗: {e}")
            return {
                'status': 'error',
                'timestamp': datetime.utcnow().isoformat(),
                'error': str(e)
            }
    
    def generate_performance_report(self, hours: int = 24) -> Dict[str, Any]:
        """生成性能報告"""
        try:
            cutoff_time = datetime.utcnow() - timedelta(hours=hours)
            recent_metrics = [m for m in self.performance_history if m.timestamp >= cutoff_time]
            
            if not recent_metrics:
                return {'error': '沒有足夠的歷史數據'}
            
            # 計算統計信息
            analysis_counts = [m.analysis_count for m in recent_metrics]
            analysis_times = [m.avg_analysis_time for m in recent_metrics]
            longtail_scores = [m.avg_longtail_score for m in recent_metrics]
            confidence_scores = [m.avg_confidence for m in recent_metrics]
            memory_usages = [m.memory_usage_mb for m in recent_metrics]
            cpu_usages = [m.cpu_usage_percent for m in recent_metrics]
            cache_hit_rates = [m.cache_hit_rate for m in recent_metrics]
            
            return {
                'period_hours': hours,
                'total_metrics': len(recent_metrics),
                'analysis_stats': {
                    'total_analyses': sum(analysis_counts),
                    'avg_analysis_time': np.mean(analysis_times),
                    'max_analysis_time': np.max(analysis_times),
                    'min_analysis_time': np.min(analysis_times)
                },
                'quality_stats': {
                    'avg_longtail_score': np.mean(longtail_scores),
                    'avg_confidence': np.mean(confidence_scores)
                },
                'resource_stats': {
                    'avg_memory_usage_mb': np.mean(memory_usages),
                    'max_memory_usage_mb': np.max(memory_usages),
                    'avg_cpu_usage_percent': np.mean(cpu_usages),
                    'max_cpu_usage_percent': np.max(cpu_usages)
                },
                'cache_stats': {
                    'avg_hit_rate': np.mean(cache_hit_rates),
                    'total_hits': self.cache_stats['hits'],
                    'total_misses': self.cache_stats['misses']
                }
            }
            
        except Exception as e:
            logger.error(f"生成性能報告失敗: {e}")
            return {'error': str(e)}

# 全局監控服務實例
monitoring_service = LongTailMonitoringService()
