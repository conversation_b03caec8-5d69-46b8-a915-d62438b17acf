"""
長尾查詢識別系統 - 數據收集管道
實現實時查詢數據處理、特徵提取和存儲
"""

import json
import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import hashlib
import re
from dataclasses import dataclass, asdict

# 配置日誌
logger = logging.getLogger(__name__)

try:
    import redis
    import psycopg2
    from psycopg2.extras import RealDictCursor
    REDIS_AVAILABLE = True
    POSTGRES_AVAILABLE = True
except ImportError as e:
    logger.warning(f"數據庫庫未安裝: {e}")
    REDIS_AVAILABLE = False
    POSTGRES_AVAILABLE = False

try:
    from kafka import KafkaConsumer, KafkaProducer
    KAFKA_AVAILABLE = True
except ImportError:
    logger.warning("Kafka庫未安裝，使用模擬模式")
    KAFKA_AVAILABLE = False

@dataclass
class QueryEvent:
    """查詢事件數據結構"""
    query: str
    user_id: str
    session_id: str
    timestamp: datetime
    source: str  # web, api, mobile
    user_agent: str
    ip_address: str
    referer: Optional[str] = None
    click_through: bool = False
    result_count: int = 0
    response_time: float = 0.0

@dataclass
class QueryFeatures:
    """查詢特徵數據結構"""
    query_hash: str
    word_count: int
    char_count: int
    entity_count: int
    complexity_score: float
    has_question_words: bool
    has_brand_names: bool
    has_location: bool
    has_numbers: bool
    language: str
    intent_score: Dict[str, float]
    technical_terms: List[str]
    frequency_score: float

class QueryDataPipeline:
    """查詢數據處理管道"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.redis_client = None
        self.pg_conn = None
        self.kafka_consumer = None
        self.kafka_producer = None
        self.running = False
        
        # 初始化連接
        self._init_connections()
        
        # 長尾查詢閾值
        self.longtail_threshold = config.get('longtail_threshold', 0.1)
        self.complexity_threshold = config.get('complexity_threshold', 0.6)
        
        # 技術術語詞典
        self.technical_terms = {
            'ai', 'machine learning', 'deep learning', 'neural network',
            'api', 'database', 'algorithm', 'optimization', 'analytics',
            '人工智能', '機器學習', '深度學習', '神經網絡', '算法', '優化', '分析'
        }
        
        # 疑問詞
        self.question_words = {
            'zh': {'什麼', '如何', '怎麼', '為什麼', '哪裡', '何時', '誰', '哪個'},
            'en': {'what', 'how', 'why', 'where', 'when', 'who', 'which'}
        }
        
        # 品牌名詞典
        self.brand_names = {
            'google', 'microsoft', 'apple', 'amazon', 'facebook', 'openai',
            '谷歌', '微軟', '蘋果', '亞馬遜', '臉書'
        }

    def _init_connections(self):
        """初始化數據庫連接"""
        try:
            # Redis連接
            if REDIS_AVAILABLE and 'redis' in self.config:
                self.redis_client = redis.Redis(**self.config['redis'])
                logger.info("Redis連接初始化成功")
            
            # PostgreSQL連接
            if POSTGRES_AVAILABLE and 'postgres' in self.config:
                self.pg_conn = psycopg2.connect(**self.config['postgres'])
                logger.info("PostgreSQL連接初始化成功")
            
            # Kafka連接
            if KAFKA_AVAILABLE and 'kafka' in self.config:
                self.kafka_consumer = KafkaConsumer(
                    'search_queries',
                    bootstrap_servers=self.config['kafka']['servers'],
                    value_deserializer=lambda m: json.loads(m.decode('utf-8')),
                    auto_offset_reset='latest',
                    enable_auto_commit=True
                )
                
                self.kafka_producer = KafkaProducer(
                    bootstrap_servers=self.config['kafka']['servers'],
                    value_serializer=lambda v: json.dumps(v, default=str).encode('utf-8')
                )
                logger.info("Kafka連接初始化成功")
                
        except Exception as e:
            logger.error(f"數據庫連接初始化失敗: {e}")

    async def start_processing(self):
        """開始處理查詢數據"""
        self.running = True
        logger.info("開始查詢數據處理管道")
        
        if KAFKA_AVAILABLE and self.kafka_consumer:
            await self._process_kafka_messages()
        else:
            # 模擬模式
            await self._simulate_query_processing()

    async def _process_kafka_messages(self):
        """處理Kafka消息"""
        try:
            for message in self.kafka_consumer:
                if not self.running:
                    break
                
                try:
                    query_data = message.value
                    query_event = QueryEvent(**query_data)
                    
                    # 處理查詢事件
                    await self.process_query_event(query_event)
                    
                except Exception as e:
                    logger.error(f"處理Kafka消息失敗: {e}")
                    continue
                    
        except Exception as e:
            logger.error(f"Kafka消息處理失敗: {e}")

    async def _simulate_query_processing(self):
        """模擬查詢處理（用於測試）"""
        sample_queries = [
            "AI工具推薦",
            "如何選擇最適合小型企業的AI客服系統",
            "人工智能在醫療領域的應用前景分析",
            "best machine learning algorithms for beginners",
            "how to implement deep learning models in production"
        ]
        
        for i, query in enumerate(sample_queries):
            if not self.running:
                break
                
            query_event = QueryEvent(
                query=query,
                user_id=f"user_{i}",
                session_id=f"session_{i}",
                timestamp=datetime.utcnow(),
                source="web",
                user_agent="Mozilla/5.0",
                ip_address="127.0.0.1"
            )
            
            await self.process_query_event(query_event)
            await asyncio.sleep(1)  # 模擬間隔

    async def process_query_event(self, query_event: QueryEvent):
        """處理單個查詢事件"""
        try:
            # 提取查詢特徵
            features = self.extract_features(query_event.query)
            
            # 判斷是否為長尾查詢
            is_longtail = self.is_longtail_query(query_event.query, features)
            
            # 存儲查詢數據
            await self.store_query_data(query_event, features, is_longtail)
            
            # 如果是長尾查詢，觸發優化流程
            if is_longtail:
                await self.trigger_longtail_optimization(query_event, features)
                
            logger.info(f"處理查詢: {query_event.query}, 長尾: {is_longtail}")
            
        except Exception as e:
            logger.error(f"處理查詢事件失敗: {e}")

    def extract_features(self, query: str) -> QueryFeatures:
        """提取查詢特徵"""
        # 基礎統計
        words = query.split()
        word_count = len(words)
        char_count = len(query)
        
        # 生成查詢哈希
        query_hash = hashlib.md5(query.encode()).hexdigest()
        
        # 語言檢測
        language = self._detect_language(query)
        
        # 實體檢測
        entities = self._extract_entities(query)
        entity_count = len(entities)
        
        # 複雜度計算
        complexity_score = self._calculate_complexity(query, words)
        
        # 特徵檢測
        has_question_words = self._has_question_words(query, language)
        has_brand_names = self._has_brand_names(query)
        has_location = self._has_location(query)
        has_numbers = bool(re.search(r'\d', query))
        
        # 技術術語檢測
        technical_terms = self._detect_technical_terms(query)
        
        # 意圖分數
        intent_score = self._calculate_intent_scores(query, language)
        
        # 頻率分數
        frequency_score = self._get_frequency_score(query)
        
        return QueryFeatures(
            query_hash=query_hash,
            word_count=word_count,
            char_count=char_count,
            entity_count=entity_count,
            complexity_score=complexity_score,
            has_question_words=has_question_words,
            has_brand_names=has_brand_names,
            has_location=has_location,
            has_numbers=has_numbers,
            language=language,
            intent_score=intent_score,
            technical_terms=technical_terms,
            frequency_score=frequency_score
        )

    def _detect_language(self, query: str) -> str:
        """檢測查詢語言"""
        chinese_chars = len(re.findall(r'[\u4e00-\u9fff]', query))
        total_chars = len(query.replace(' ', ''))
        
        if total_chars == 0:
            return 'unknown'
        
        chinese_ratio = chinese_chars / total_chars
        return 'zh' if chinese_ratio > 0.5 else 'en'

    def _extract_entities(self, query: str) -> List[str]:
        """提取實體（簡化版本）"""
        # 這裡可以集成NER模型
        entities = []
        
        # 簡單的實體檢測
        words = query.split()
        for word in words:
            if word.lower() in self.brand_names:
                entities.append(word)
            elif word.lower() in self.technical_terms:
                entities.append(word)
        
        return entities

    def _calculate_complexity(self, query: str, words: List[str]) -> float:
        """計算查詢複雜度"""
        score = 0.0
        
        # 詞彙複雜度
        score += len(words) * 0.1
        
        # 句法複雜度
        if self._has_question_words(query, self._detect_language(query)):
            score += 0.2
        
        # 專業術語
        technical_terms = self._detect_technical_terms(query)
        score += len(technical_terms) * 0.3
        
        # 實體複雜度
        entities = self._extract_entities(query)
        score += len(entities) * 0.2
        
        return min(score, 1.0)

    def _has_question_words(self, query: str, language: str) -> bool:
        """檢查是否包含疑問詞"""
        query_lower = query.lower()
        
        if language == 'zh':
            return any(word in query for word in self.question_words['zh'])
        else:
            return any(word in query_lower for word in self.question_words['en'])

    def _has_brand_names(self, query: str) -> bool:
        """檢查是否包含品牌名"""
        query_lower = query.lower()
        return any(brand in query_lower for brand in self.brand_names)

    def _has_location(self, query: str) -> bool:
        """檢查是否包含地理位置"""
        location_keywords = {
            '附近', '台北', '台中', '高雄', '台南', '桃園', '新竹', '台灣',
            'near', 'nearby', 'location', 'address', 'taipei', 'taiwan'
        }
        query_lower = query.lower()
        return any(loc in query_lower for loc in location_keywords)

    def _detect_technical_terms(self, query: str) -> List[str]:
        """檢測技術術語"""
        query_lower = query.lower()
        found_terms = []
        
        for term in self.technical_terms:
            if term in query_lower:
                found_terms.append(term)
        
        return found_terms

    def _calculate_intent_scores(self, query: str, language: str) -> Dict[str, float]:
        """計算意圖分數"""
        scores = {
            'informational': 0.0,
            'commercial': 0.0,
            'transactional': 0.0,
            'navigational': 0.0,
            'local': 0.0
        }
        
        query_lower = query.lower()
        
        # 信息型意圖
        if self._has_question_words(query, language):
            scores['informational'] += 0.5
        
        # 商業意圖
        commercial_words = ['價格', '比較', '評價', 'price', 'compare', 'review']
        for word in commercial_words:
            if word in query_lower:
                scores['commercial'] += 0.3
        
        # 交易意圖
        transaction_words = ['買', '購買', '訂購', 'buy', 'purchase', 'order']
        for word in transaction_words:
            if word in query_lower:
                scores['transactional'] += 0.4
        
        # 本地意圖
        if self._has_location(query):
            scores['local'] += 0.6
        
        # 導航意圖
        navigation_words = ['登入', '註冊', '首頁', 'login', 'register', 'home']
        for word in navigation_words:
            if word in query_lower:
                scores['navigational'] += 0.4
        
        # 歸一化分數
        total = sum(scores.values())
        if total > 0:
            scores = {k: v/total for k, v in scores.items()}
        
        return scores

    def _get_frequency_score(self, query: str) -> float:
        """獲取查詢頻率分數"""
        if self.redis_client:
            try:
                # 從Redis獲取歷史頻率
                frequency = self.redis_client.get(f"query_freq:{query}")
                if frequency:
                    return float(frequency) / 1000.0  # 歸一化
            except Exception as e:
                logger.warning(f"獲取頻率分數失敗: {e}")
        
        return 0.1  # 默認低頻率

    def is_longtail_query(self, query: str, features: QueryFeatures) -> bool:
        """判斷是否為長尾查詢"""
        # 多維度判斷
        conditions = []
        
        # 頻率條件
        conditions.append(features.frequency_score < self.longtail_threshold)
        
        # 複雜度條件
        conditions.append(features.complexity_score > self.complexity_threshold)
        
        # 實體數量條件
        conditions.append(features.entity_count > 2)
        
        # 詞數條件
        conditions.append(features.word_count > 5)
        
        # 至少滿足兩個條件
        return sum(conditions) >= 2

    async def store_query_data(self, query_event: QueryEvent, features: QueryFeatures, is_longtail: bool):
        """存儲查詢數據"""
        try:
            # 存儲到Redis（緩存）
            if self.redis_client:
                cache_data = {
                    'query': query_event.query,
                    'features': asdict(features),
                    'is_longtail': is_longtail,
                    'timestamp': query_event.timestamp.isoformat()
                }
                
                cache_key = f"query_cache:{features.query_hash}"
                self.redis_client.setex(cache_key, 3600, json.dumps(cache_data, default=str))
                
                # 更新頻率計數
                freq_key = f"query_freq:{query_event.query}"
                self.redis_client.incr(freq_key)
                self.redis_client.expire(freq_key, 86400 * 30)  # 30天過期
            
            # 存儲到PostgreSQL（持久化）
            if self.pg_conn:
                await self._store_to_postgres(query_event, features, is_longtail)
                
        except Exception as e:
            logger.error(f"存儲查詢數據失敗: {e}")

    async def _store_to_postgres(self, query_event: QueryEvent, features: QueryFeatures, is_longtail: bool):
        """存儲到PostgreSQL"""
        try:
            with self.pg_conn.cursor() as cursor:
                # 插入查詢事件
                cursor.execute("""
                    INSERT INTO query_events (
                        query, user_id, session_id, timestamp, source,
                        user_agent, ip_address, referer, click_through,
                        result_count, response_time
                    ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                    RETURNING id
                """, (
                    query_event.query, query_event.user_id, query_event.session_id,
                    query_event.timestamp, query_event.source, query_event.user_agent,
                    query_event.ip_address, query_event.referer, query_event.click_through,
                    query_event.result_count, query_event.response_time
                ))
                
                event_id = cursor.fetchone()[0]
                
                # 插入查詢特徵
                cursor.execute("""
                    INSERT INTO query_features (
                        event_id, query_hash, word_count, char_count, entity_count,
                        complexity_score, has_question_words, has_brand_names,
                        has_location, has_numbers, language, intent_scores,
                        technical_terms, frequency_score, is_longtail
                    ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """, (
                    event_id, features.query_hash, features.word_count,
                    features.char_count, features.entity_count, features.complexity_score,
                    features.has_question_words, features.has_brand_names,
                    features.has_location, features.has_numbers, features.language,
                    json.dumps(features.intent_score), features.technical_terms,
                    features.frequency_score, is_longtail
                ))
                
                self.pg_conn.commit()
                
        except Exception as e:
            logger.error(f"PostgreSQL存儲失敗: {e}")
            self.pg_conn.rollback()

    async def trigger_longtail_optimization(self, query_event: QueryEvent, features: QueryFeatures):
        """觸發長尾查詢優化"""
        try:
            optimization_data = {
                'query': query_event.query,
                'features': asdict(features),
                'timestamp': query_event.timestamp.isoformat(),
                'priority': self._calculate_optimization_priority(features)
            }
            
            # 發送到Kafka優化隊列
            if self.kafka_producer:
                self.kafka_producer.send('longtail_optimization', optimization_data)
            
            logger.info(f"觸發長尾查詢優化: {query_event.query}")
            
        except Exception as e:
            logger.error(f"觸發優化失敗: {e}")

    def _calculate_optimization_priority(self, features: QueryFeatures) -> int:
        """計算優化優先級"""
        priority = 0
        
        # 複雜度越高，優先級越高
        priority += int(features.complexity_score * 50)
        
        # 商業意圖越強，優先級越高
        commercial_score = features.intent_score.get('commercial', 0)
        priority += int(commercial_score * 30)
        
        # 技術術語越多，優先級越高
        priority += len(features.technical_terms) * 10
        
        return min(priority, 100)

    def stop_processing(self):
        """停止處理"""
        self.running = False
        logger.info("停止查詢數據處理管道")

    def __del__(self):
        """清理資源"""
        if self.pg_conn:
            self.pg_conn.close()
        if self.kafka_consumer:
            self.kafka_consumer.close()
        if self.kafka_producer:
            self.kafka_producer.close()
