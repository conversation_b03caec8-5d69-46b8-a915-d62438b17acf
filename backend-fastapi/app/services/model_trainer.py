"""
模型訓練系統
實現少樣本學習模型的訓練、持久化和在線學習
"""

import os
import json
import pickle
import logging
import asyncio
from typing import List, Dict, Optional, Any, Tuple
from datetime import datetime
from pathlib import Path
import numpy as np
from sklearn.model_selection import train_test_split
from sklearn.ensemble import RandomForestClassifier
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics import accuracy_score, classification_report
import joblib

logger = logging.getLogger(__name__)

class SimpleModelTrainer:
    """簡化的模型訓練器 - 使用傳統機器學習方法"""
    
    def __init__(self, model_dir: str = "data/models"):
        self.model_dir = Path(model_dir)
        self.model_dir.mkdir(parents=True, exist_ok=True)
        
        # 模型文件路徑
        self.classifier_path = self.model_dir / "query_classifier.pkl"
        self.vectorizer_path = self.model_dir / "text_vectorizer.pkl"
        self.metadata_path = self.model_dir / "model_metadata.json"
        
        # 模型組件
        self.classifier = None
        self.vectorizer = None
        self.metadata = self._load_metadata()
        
        # 訓練狀態
        self.is_training = False
        
    def _load_metadata(self) -> Dict[str, Any]:
        """載入模型元數據"""
        try:
            if self.metadata_path.exists():
                with open(self.metadata_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            logger.error(f"載入模型元數據失敗: {e}")
        
        return {
            "version": "1.0",
            "created_at": None,
            "updated_at": None,
            "training_samples": 0,
            "accuracy": 0.0,
            "model_type": "random_forest",
            "features": [],
            "classes": [],
            "training_history": []
        }
    
    def _save_metadata(self):
        """保存模型元數據"""
        try:
            with open(self.metadata_path, 'w', encoding='utf-8') as f:
                json.dump(self.metadata, f, ensure_ascii=False, indent=2, default=str)
        except Exception as e:
            logger.error(f"保存模型元數據失敗: {e}")
    
    def _generate_training_data(self) -> Tuple[List[str], List[str]]:
        """生成訓練數據"""
        # 示例訓練數據
        texts = [
            # 長尾查詢
            "AI人工智能在台灣中小企業的應用案例與實施策略分析",
            "如何選擇最適合小型企業的AI客服系統並進行有效整合",
            "台北最好的AI寫作工具推薦與價格比較分析報告",
            "機器學習演算法在電商推薦系統中的應用與性能優化策略",
            "深度學習模型在自然語言處理任務中的最新發展趨勢",
            "區塊鏈技術在供應鏈管理系統中的實際應用案例研究",
            "雲端運算平台選擇指南：AWS vs Azure vs Google Cloud比較",
            "數據科學家職業發展路徑與必備技能要求分析",
            "Python網頁爬蟲開發完整教學：從基礎到進階實戰",
            "React前端框架學習指南：組件設計與狀態管理最佳實踐",
            
            # 中等查詢
            "AI客服系統推薦",
            "機器學習教學",
            "Python爬蟲教程",
            "React開發指南",
            "數據分析工具",
            "雲端服務比較",
            "區塊鏈應用",
            "深度學習框架",
            "自然語言處理",
            "前端開發技術",
            
            # 短尾查詢
            "AI",
            "機器學習",
            "Python",
            "React",
            "數據分析",
            "雲端",
            "區塊鏈",
            "深度學習",
            "NLP",
            "前端"
        ]
        
        labels = (
            ["long_tail"] * 10 +  # 長尾查詢
            ["middle"] * 10 +     # 中等查詢
            ["head"] * 10         # 短尾查詢
        )
        
        return texts, labels
    
    async def train_model(self, force_retrain: bool = False) -> Dict[str, Any]:
        """訓練模型"""
        if self.is_training and not force_retrain:
            return {"status": "already_training", "message": "模型正在訓練中"}
        
        self.is_training = True
        start_time = datetime.now()
        
        try:
            logger.info("開始模型訓練...")
            
            # 生成訓練數據
            texts, labels = self._generate_training_data()
            
            # 文本向量化
            logger.info("進行文本向量化...")
            self.vectorizer = TfidfVectorizer(
                max_features=1000,
                ngram_range=(1, 2),
                stop_words=None  # 保留中文停用詞
            )
            
            X = self.vectorizer.fit_transform(texts)
            y = np.array(labels)
            
            # 分割訓練和測試數據
            X_train, X_test, y_train, y_test = train_test_split(
                X, y, test_size=0.2, random_state=42, stratify=y
            )
            
            # 訓練分類器
            logger.info("訓練隨機森林分類器...")
            self.classifier = RandomForestClassifier(
                n_estimators=100,
                random_state=42,
                max_depth=10
            )
            
            self.classifier.fit(X_train, y_train)
            
            # 評估模型
            y_pred = self.classifier.predict(X_test)
            accuracy = accuracy_score(y_test, y_pred)
            
            # 生成分類報告
            report = classification_report(y_test, y_pred, output_dict=True)
            
            # 保存模型
            logger.info("保存模型...")
            joblib.dump(self.classifier, self.classifier_path)
            joblib.dump(self.vectorizer, self.vectorizer_path)
            
            # 更新元數據
            end_time = datetime.now()
            training_time = (end_time - start_time).total_seconds()
            
            self.metadata.update({
                "updated_at": end_time.isoformat(),
                "training_samples": len(texts),
                "accuracy": accuracy,
                "classes": list(np.unique(labels)),
                "features": self.vectorizer.get_feature_names_out()[:50].tolist(),  # 前50個特徵
                "training_history": self.metadata.get("training_history", []) + [{
                    "timestamp": end_time.isoformat(),
                    "accuracy": accuracy,
                    "training_samples": len(texts),
                    "training_time_seconds": training_time
                }]
            })
            
            if not self.metadata.get("created_at"):
                self.metadata["created_at"] = end_time.isoformat()
            
            self._save_metadata()
            
            result = {
                "status": "success",
                "message": f"模型訓練完成，準確率: {accuracy:.3f}",
                "accuracy": accuracy,
                "training_samples": len(texts),
                "training_time_seconds": round(training_time, 2),
                "classification_report": report
            }
            
            logger.info(f"模型訓練完成: {result}")
            return result
            
        except Exception as e:
            logger.error(f"模型訓練失敗: {e}")
            return {
                "status": "error",
                "message": f"模型訓練失敗: {str(e)}"
            }
        finally:
            self.is_training = False
    
    async def load_model(self) -> Dict[str, Any]:
        """載入已訓練的模型"""
        try:
            if not self.classifier_path.exists() or not self.vectorizer_path.exists():
                return {
                    "status": "no_model",
                    "message": "沒有找到已訓練的模型"
                }
            
            # 載入模型組件
            self.classifier = joblib.load(self.classifier_path)
            self.vectorizer = joblib.load(self.vectorizer_path)
            
            return {
                "status": "success",
                "message": "模型載入成功",
                "metadata": self.metadata
            }
            
        except Exception as e:
            logger.error(f"模型載入失敗: {e}")
            return {
                "status": "error",
                "message": f"模型載入失敗: {str(e)}"
            }
    
    async def predict(self, texts: List[str]) -> Dict[str, Any]:
        """使用模型進行預測"""
        try:
            # 確保模型已載入
            if self.classifier is None or self.vectorizer is None:
                load_result = await self.load_model()
                if load_result["status"] != "success":
                    return load_result
            
            # 向量化輸入文本
            X = self.vectorizer.transform(texts)
            
            # 預測
            predictions = self.classifier.predict(X)
            probabilities = self.classifier.predict_proba(X)
            
            # 格式化結果
            results = []
            for i, text in enumerate(texts):
                pred_class = predictions[i]
                prob_dict = dict(zip(self.classifier.classes_, probabilities[i]))
                
                results.append({
                    "text": text,
                    "predicted_class": pred_class,
                    "confidence": max(probabilities[i]),
                    "probabilities": prob_dict
                })
            
            return {
                "status": "success",
                "predictions": results,
                "model_metadata": {
                    "accuracy": self.metadata.get("accuracy", 0),
                    "training_samples": self.metadata.get("training_samples", 0),
                    "last_updated": self.metadata.get("updated_at")
                }
            }
            
        except Exception as e:
            logger.error(f"模型預測失敗: {e}")
            return {
                "status": "error",
                "message": f"模型預測失敗: {str(e)}"
            }
    
    async def online_learning_update(self, new_texts: List[str], new_labels: List[str]) -> Dict[str, Any]:
        """在線學習更新（簡化版）"""
        try:
            if not new_texts or not new_labels:
                return {
                    "status": "no_data",
                    "message": "沒有新的訓練數據"
                }
            
            # 載入現有模型
            if self.classifier is None or self.vectorizer is None:
                load_result = await self.load_model()
                if load_result["status"] != "success":
                    return load_result
            
            # 獲取現有訓練數據
            existing_texts, existing_labels = self._generate_training_data()
            
            # 合併新舊數據
            all_texts = existing_texts + new_texts
            all_labels = existing_labels + new_labels
            
            # 重新訓練（簡化的在線學習）
            logger.info("執行在線學習更新...")
            X = self.vectorizer.fit_transform(all_texts)
            y = np.array(all_labels)
            
            # 重新訓練分類器
            self.classifier.fit(X, y)
            
            # 評估更新後的模型
            y_pred = self.classifier.predict(X)
            accuracy = accuracy_score(y, y_pred)
            
            # 保存更新後的模型
            joblib.dump(self.classifier, self.classifier_path)
            joblib.dump(self.vectorizer, self.vectorizer_path)
            
            # 更新元數據
            self.metadata.update({
                "updated_at": datetime.now().isoformat(),
                "training_samples": len(all_texts),
                "accuracy": accuracy
            })
            self._save_metadata()
            
            return {
                "status": "success",
                "message": f"在線學習更新完成，新準確率: {accuracy:.3f}",
                "new_accuracy": accuracy,
                "total_samples": len(all_texts),
                "new_samples": len(new_texts)
            }
            
        except Exception as e:
            logger.error(f"在線學習更新失敗: {e}")
            return {
                "status": "error",
                "message": f"在線學習更新失敗: {str(e)}"
            }
    
    async def get_model_stats(self) -> Dict[str, Any]:
        """獲取模型統計信息"""
        try:
            model_exists = (
                self.classifier_path.exists() and 
                self.vectorizer_path.exists()
            )
            
            stats = {
                "model_exists": model_exists,
                "is_training": self.is_training,
                "metadata": self.metadata
            }
            
            if model_exists and self.classifier is not None:
                stats.update({
                    "model_loaded": True,
                    "n_features": len(self.vectorizer.get_feature_names_out()) if self.vectorizer else 0,
                    "n_classes": len(self.classifier.classes_) if self.classifier else 0
                })
            else:
                stats.update({
                    "model_loaded": False,
                    "n_features": 0,
                    "n_classes": 0
                })
            
            return stats
            
        except Exception as e:
            logger.error(f"獲取模型統計失敗: {e}")
            return {"error": str(e)}
    
    async def clear_model(self) -> Dict[str, Any]:
        """清除模型文件"""
        try:
            # 刪除模型文件
            if self.classifier_path.exists():
                self.classifier_path.unlink()
            if self.vectorizer_path.exists():
                self.vectorizer_path.unlink()
            if self.metadata_path.exists():
                self.metadata_path.unlink()
            
            # 重置內存中的模型
            self.classifier = None
            self.vectorizer = None
            self.metadata = self._load_metadata()
            
            return {
                "status": "success",
                "message": "模型已清除"
            }
            
        except Exception as e:
            logger.error(f"清除模型失敗: {e}")
            return {
                "status": "error",
                "message": f"清除模型失敗: {str(e)}"
            }
