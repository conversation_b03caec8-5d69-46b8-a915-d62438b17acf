"""
性能優化系統
實現批量處理、異步處理、緩存和性能監控
"""

import asyncio
import time
import logging
from typing import List, Dict, Optional, Any, Callable, Tuple
from datetime import datetime, timedelta
from collections import defaultdict, deque
import json
from pathlib import Path
import hashlib

logger = logging.getLogger(__name__)

class PerformanceOptimizer:
    """性能優化器 - 提供批量處理、緩存和性能監控"""
    
    def __init__(self, 
                 cache_dir: str = "data/cache",
                 max_cache_size: int = 1000,
                 cache_ttl_seconds: int = 3600):
        
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        
        # 內存緩存
        self.memory_cache = {}
        self.cache_timestamps = {}
        self.max_cache_size = max_cache_size
        self.cache_ttl_seconds = cache_ttl_seconds
        
        # 性能統計
        self.performance_stats = defaultdict(list)
        self.request_times = deque(maxlen=1000)  # 保留最近1000次請求時間
        
        # 批量處理配置
        self.batch_size = 50
        self.batch_timeout = 5.0  # 秒
        
        # 異步任務隊列
        self.task_queue = asyncio.Queue()
        self.worker_count = 3
        self.workers_started = False
        
    def _generate_cache_key(self, data: Any) -> str:
        """生成緩存鍵"""
        try:
            if isinstance(data, dict):
                data_str = json.dumps(data, sort_keys=True, ensure_ascii=False)
            elif isinstance(data, list):
                data_str = json.dumps(data, ensure_ascii=False)
            else:
                data_str = str(data)
            
            return hashlib.md5(data_str.encode('utf-8')).hexdigest()
        except Exception as e:
            logger.error(f"生成緩存鍵失敗: {e}")
            return str(hash(str(data)))
    
    def _is_cache_valid(self, key: str) -> bool:
        """檢查緩存是否有效"""
        if key not in self.cache_timestamps:
            return False
        
        timestamp = self.cache_timestamps[key]
        return (datetime.now() - timestamp).total_seconds() < self.cache_ttl_seconds
    
    def _cleanup_cache(self):
        """清理過期緩存"""
        current_time = datetime.now()
        expired_keys = []
        
        for key, timestamp in self.cache_timestamps.items():
            if (current_time - timestamp).total_seconds() > self.cache_ttl_seconds:
                expired_keys.append(key)
        
        for key in expired_keys:
            self.memory_cache.pop(key, None)
            self.cache_timestamps.pop(key, None)
        
        # 如果緩存太大，移除最舊的項目
        if len(self.memory_cache) > self.max_cache_size:
            # 按時間戳排序，移除最舊的
            sorted_items = sorted(self.cache_timestamps.items(), key=lambda x: x[1])
            items_to_remove = len(self.memory_cache) - self.max_cache_size
            
            for i in range(items_to_remove):
                key = sorted_items[i][0]
                self.memory_cache.pop(key, None)
                self.cache_timestamps.pop(key, None)
    
    async def get_cached_result(self, key: str, compute_func: Callable, *args, **kwargs) -> Any:
        """獲取緩存結果，如果不存在則計算並緩存"""
        cache_key = self._generate_cache_key(f"{key}:{args}:{kwargs}")
        
        # 檢查內存緩存
        if cache_key in self.memory_cache and self._is_cache_valid(cache_key):
            logger.debug(f"緩存命中: {cache_key}")
            return self.memory_cache[cache_key]
        
        # 計算結果
        start_time = time.time()
        try:
            if asyncio.iscoroutinefunction(compute_func):
                result = await compute_func(*args, **kwargs)
            else:
                result = compute_func(*args, **kwargs)
            
            # 緩存結果
            self.memory_cache[cache_key] = result
            self.cache_timestamps[cache_key] = datetime.now()
            
            # 清理過期緩存
            self._cleanup_cache()
            
            # 記錄性能統計
            compute_time = time.time() - start_time
            self.performance_stats[key].append({
                'timestamp': datetime.now(),
                'compute_time': compute_time,
                'cache_hit': False
            })
            
            logger.debug(f"計算並緩存結果: {cache_key}, 耗時: {compute_time:.3f}s")
            return result
            
        except Exception as e:
            logger.error(f"計算結果失敗: {e}")
            raise
    
    async def batch_process(self, 
                          items: List[Any], 
                          process_func: Callable,
                          batch_size: Optional[int] = None,
                          progress_callback: Optional[Callable] = None) -> List[Any]:
        """批量處理項目"""
        if not items:
            return []
        
        batch_size = batch_size or self.batch_size
        results = []
        total_items = len(items)
        
        start_time = time.time()
        
        # 分批處理
        for i in range(0, len(items), batch_size):
            batch = items[i:i + batch_size]
            batch_start_time = time.time()
            
            try:
                # 並行處理批次中的項目
                if asyncio.iscoroutinefunction(process_func):
                    batch_results = await asyncio.gather(
                        *[process_func(item) for item in batch],
                        return_exceptions=True
                    )
                else:
                    batch_results = [process_func(item) for item in batch]
                
                # 處理異常
                for j, result in enumerate(batch_results):
                    if isinstance(result, Exception):
                        logger.error(f"批量處理項目 {i+j} 失敗: {result}")
                        results.append(None)
                    else:
                        results.append(result)
                
                batch_time = time.time() - batch_start_time
                processed_count = len(results)
                
                # 進度回調
                if progress_callback:
                    progress = processed_count / total_items
                    await progress_callback(progress, processed_count, total_items)
                
                logger.debug(f"批次 {i//batch_size + 1} 完成: {len(batch)} 項目, 耗時: {batch_time:.3f}s")
                
            except Exception as e:
                logger.error(f"批量處理批次失敗: {e}")
                # 添加None結果以保持索引一致性
                results.extend([None] * len(batch))
        
        total_time = time.time() - start_time
        
        # 記錄性能統計
        self.performance_stats['batch_process'].append({
            'timestamp': datetime.now(),
            'total_items': total_items,
            'batch_size': batch_size,
            'total_time': total_time,
            'items_per_second': total_items / total_time if total_time > 0 else 0
        })
        
        logger.info(f"批量處理完成: {total_items} 項目, 耗時: {total_time:.3f}s, 速度: {total_items/total_time:.1f} 項目/秒")
        
        return results
    
    async def start_async_workers(self):
        """啟動異步工作者"""
        if self.workers_started:
            return
        
        async def worker(worker_id: int):
            """異步工作者"""
            logger.info(f"異步工作者 {worker_id} 啟動")
            
            while True:
                try:
                    # 從隊列獲取任務
                    task_data = await self.task_queue.get()
                    
                    if task_data is None:  # 停止信號
                        break
                    
                    task_func, args, kwargs, result_callback = task_data
                    start_time = time.time()
                    
                    try:
                        # 執行任務
                        if asyncio.iscoroutinefunction(task_func):
                            result = await task_func(*args, **kwargs)
                        else:
                            result = task_func(*args, **kwargs)
                        
                        # 回調結果
                        if result_callback:
                            if asyncio.iscoroutinefunction(result_callback):
                                await result_callback(result, None)
                            else:
                                result_callback(result, None)
                        
                        task_time = time.time() - start_time
                        logger.debug(f"工作者 {worker_id} 完成任務, 耗時: {task_time:.3f}s")
                        
                    except Exception as e:
                        logger.error(f"工作者 {worker_id} 任務執行失敗: {e}")
                        if result_callback:
                            if asyncio.iscoroutinefunction(result_callback):
                                await result_callback(None, e)
                            else:
                                result_callback(None, e)
                    
                    finally:
                        self.task_queue.task_done()
                        
                except Exception as e:
                    logger.error(f"工作者 {worker_id} 錯誤: {e}")
        
        # 啟動工作者
        for i in range(self.worker_count):
            asyncio.create_task(worker(i))
        
        self.workers_started = True
        logger.info(f"已啟動 {self.worker_count} 個異步工作者")
    
    async def submit_async_task(self, 
                              task_func: Callable, 
                              *args, 
                              result_callback: Optional[Callable] = None,
                              **kwargs):
        """提交異步任務"""
        if not self.workers_started:
            await self.start_async_workers()
        
        await self.task_queue.put((task_func, args, kwargs, result_callback))
        logger.debug(f"已提交異步任務: {task_func.__name__}")
    
    def record_request_time(self, endpoint: str, duration: float):
        """記錄請求時間"""
        self.request_times.append({
            'endpoint': endpoint,
            'duration': duration,
            'timestamp': datetime.now()
        })
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """獲取性能統計"""
        try:
            # 計算請求統計
            if self.request_times:
                recent_requests = [r for r in self.request_times 
                                 if (datetime.now() - r['timestamp']).total_seconds() < 300]  # 最近5分鐘
                
                if recent_requests:
                    avg_response_time = sum(r['duration'] for r in recent_requests) / len(recent_requests)
                    max_response_time = max(r['duration'] for r in recent_requests)
                    min_response_time = min(r['duration'] for r in recent_requests)
                    requests_per_minute = len(recent_requests) / 5.0
                else:
                    avg_response_time = max_response_time = min_response_time = requests_per_minute = 0
            else:
                avg_response_time = max_response_time = min_response_time = requests_per_minute = 0
            
            # 緩存統計
            cache_stats = {
                'cache_size': len(self.memory_cache),
                'max_cache_size': self.max_cache_size,
                'cache_hit_ratio': self._calculate_cache_hit_ratio()
            }
            
            # 批量處理統計
            batch_stats = {}
            if 'batch_process' in self.performance_stats:
                recent_batches = [b for b in self.performance_stats['batch_process']
                                if (datetime.now() - b['timestamp']).total_seconds() < 3600]  # 最近1小時
                
                if recent_batches:
                    batch_stats = {
                        'total_batches': len(recent_batches),
                        'avg_batch_time': sum(b['total_time'] for b in recent_batches) / len(recent_batches),
                        'avg_items_per_second': sum(b['items_per_second'] for b in recent_batches) / len(recent_batches),
                        'total_items_processed': sum(b['total_items'] for b in recent_batches)
                    }
            
            return {
                'timestamp': datetime.now(),
                'request_stats': {
                    'avg_response_time_ms': round(avg_response_time * 1000, 2),
                    'max_response_time_ms': round(max_response_time * 1000, 2),
                    'min_response_time_ms': round(min_response_time * 1000, 2),
                    'requests_per_minute': round(requests_per_minute, 2),
                    'total_requests_tracked': len(self.request_times)
                },
                'cache_stats': cache_stats,
                'batch_stats': batch_stats,
                'async_stats': {
                    'workers_started': self.workers_started,
                    'worker_count': self.worker_count,
                    'queue_size': self.task_queue.qsize() if self.workers_started else 0
                }
            }
            
        except Exception as e:
            logger.error(f"獲取性能統計失敗: {e}")
            return {'error': str(e)}
    
    def _calculate_cache_hit_ratio(self) -> float:
        """計算緩存命中率"""
        try:
            total_requests = 0
            cache_hits = 0
            
            for stats_list in self.performance_stats.values():
                for stat in stats_list:
                    if 'cache_hit' in stat:
                        total_requests += 1
                        if stat['cache_hit']:
                            cache_hits += 1
            
            return cache_hits / total_requests if total_requests > 0 else 0.0
            
        except Exception as e:
            logger.error(f"計算緩存命中率失敗: {e}")
            return 0.0
    
    async def optimize_query_analysis(self, 
                                    queries: List[str],
                                    analysis_func: Callable,
                                    use_cache: bool = True,
                                    batch_size: Optional[int] = None) -> List[Any]:
        """優化查詢分析性能"""
        if not queries:
            return []
        
        start_time = time.time()
        results = []
        
        try:
            if use_cache:
                # 使用緩存的批量處理
                async def cached_analysis(query: str):
                    return await self.get_cached_result(
                        f"query_analysis_{query}",
                        analysis_func,
                        query
                    )
                
                results = await self.batch_process(
                    queries,
                    cached_analysis,
                    batch_size=batch_size
                )
            else:
                # 直接批量處理
                results = await self.batch_process(
                    queries,
                    analysis_func,
                    batch_size=batch_size
                )
            
            total_time = time.time() - start_time
            
            # 記錄優化統計
            self.performance_stats['optimized_analysis'].append({
                'timestamp': datetime.now(),
                'query_count': len(queries),
                'total_time': total_time,
                'queries_per_second': len(queries) / total_time if total_time > 0 else 0,
                'used_cache': use_cache
            })
            
            logger.info(f"優化查詢分析完成: {len(queries)} 查詢, 耗時: {total_time:.3f}s")
            
            return results
            
        except Exception as e:
            logger.error(f"優化查詢分析失敗: {e}")
            raise
    
    async def clear_cache(self):
        """清除所有緩存"""
        self.memory_cache.clear()
        self.cache_timestamps.clear()
        logger.info("緩存已清除")
    
    async def stop_workers(self):
        """停止異步工作者"""
        if not self.workers_started:
            return
        
        # 發送停止信號
        for _ in range(self.worker_count):
            await self.task_queue.put(None)
        
        # 等待隊列清空
        await self.task_queue.join()
        
        self.workers_started = False
        logger.info("異步工作者已停止")
