import json
import time
import logging
from pathlib import Path
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import asyncio
import matplotlib.pyplot as plt
import numpy as np
from collections import Counter, defaultdict

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class CommercialIntentDashboard:
    """
    商業意圖分類監控儀表板，用於分析和視覺化商業意圖分類結果
    """
    def __init__(self, data_dir: str = "./data/dashboard"):
        self.data_dir = Path(data_dir)
        self.data_dir.mkdir(exist_ok=True, parents=True)
        self.metrics_file = self.data_dir / "commercial_metrics.json"
        self.recent_queries_file = self.data_dir / "recent_queries.json"
        self.daily_stats_dir = self.data_dir / "daily_stats"
        self.daily_stats_dir.mkdir(exist_ok=True, parents=True)
        self.metrics = self._load_metrics()
        self.recent_queries = self._load_recent_queries()
        
    def _load_metrics(self) -> Dict[str, Any]:
        """載入已保存的指標數據"""
        if self.metrics_file.exists():
            try:
                with open(self.metrics_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                logger.error(f"載入指標數據失敗: {e}")
                return self._initialize_metrics()
        else:
            return self._initialize_metrics()
    
    def _initialize_metrics(self) -> Dict[str, Any]:
        """初始化指標數據結構"""
        return {
            "total_queries": 0,
            "commercial_queries": 0,
            "intent_distribution": {},
            "commercial_value_distribution": {
                "very_low": 0,   # 0-0.2
                "low": 0,        # 0.2-0.4
                "medium": 0,     # 0.4-0.6
                "high": 0,       # 0.6-0.8
                "very_high": 0   # 0.8-1.0
            },
            "daily_stats": {},
            "confidence_distribution": {
                "very_low": 0,   # 0-0.2
                "low": 0,        # 0.2-0.4
                "medium": 0,     # 0.4-0.6
                "high": 0,       # 0.6-0.8
                "very_high": 0   # 0.8-1.0
            },
            "last_updated": datetime.now().isoformat()
        }
    
    def _load_recent_queries(self, max_queries: int = 100) -> List[Dict[str, Any]]:
        """載入最近的查詢數據"""
        if self.recent_queries_file.exists():
            try:
                with open(self.recent_queries_file, 'r', encoding='utf-8') as f:
                    queries = json.load(f)
                    # 只保留最近的查詢
                    return queries[-max_queries:] if len(queries) > max_queries else queries
            except Exception as e:
                logger.error(f"載入最近查詢數據失敗: {e}")
                return []
        else:
            return []
    
    def _save_metrics(self) -> None:
        """保存指標數據"""
        try:
            with open(self.metrics_file, 'w', encoding='utf-8') as f:
                json.dump(self.metrics, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"保存指標數據失敗: {e}")
    
    def _save_recent_queries(self) -> None:
        """保存最近的查詢數據"""
        try:
            with open(self.recent_queries_file, 'w', encoding='utf-8') as f:
                json.dump(self.recent_queries, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"保存最近查詢數據失敗: {e}")
    
    def _update_daily_stats(self, query_data: Dict[str, Any]) -> None:
        """更新每日統計數據"""
        today = datetime.now().strftime("%Y-%m-%d")
        
        # 確保有今日的數據
        if today not in self.metrics["daily_stats"]:
            self.metrics["daily_stats"][today] = {
                "total_queries": 0,
                "commercial_queries": 0,
                "intent_counts": {},
                "commercial_value_avg": 0,
                "confidence_avg": 0
            }
        
        # 更新今日統計
        daily_stats = self.metrics["daily_stats"][today]
        daily_stats["total_queries"] += 1
        
        intent = query_data.get("intent", "unknown")
        if intent not in daily_stats["intent_counts"]:
            daily_stats["intent_counts"][intent] = 0
        daily_stats["intent_counts"][intent] += 1
        
        if query_data.get("is_commercial_intent", False):
            daily_stats["commercial_queries"] += 1
        
        # 更新平均值
        commercial_value = query_data.get("commercial_value", 0)
        confidence = query_data.get("confidence", 0)
        
        # 使用加權平均更新
        n = daily_stats["total_queries"]
        daily_stats["commercial_value_avg"] = ((n-1) * daily_stats["commercial_value_avg"] + commercial_value) / n
        daily_stats["confidence_avg"] = ((n-1) * daily_stats["confidence_avg"] + confidence) / n
        
        # 清理舊數據 (保留最近30天)
        cutoff_date = (datetime.now() - timedelta(days=30)).strftime("%Y-%m-%d")
        self.metrics["daily_stats"] = {k: v for k, v in self.metrics["daily_stats"].items() 
                                      if k >= cutoff_date}
    
    def add_query_result(self, query_result: Dict[str, Any]) -> None:
        """
        添加查詢結果到儀表板數據
        
        Args:
            query_result: 查詢結果數據，包含查詢文本、意圖分類、商業價值等信息
        """
        # 更新總計數
        self.metrics["total_queries"] += 1
        
        # 更新意圖分佈
        intent = query_result.get("intent", "unknown")
        if intent not in self.metrics["intent_distribution"]:
            self.metrics["intent_distribution"][intent] = 0
        self.metrics["intent_distribution"][intent] += 1
        
        # 更新商業查詢計數
        if query_result.get("is_commercial_intent", False):
            self.metrics["commercial_queries"] += 1
        
        # 更新商業價值分佈
        commercial_value = query_result.get("commercial_value", 0)
        if 0 <= commercial_value < 0.2:
            self.metrics["commercial_value_distribution"]["very_low"] += 1
        elif 0.2 <= commercial_value < 0.4:
            self.metrics["commercial_value_distribution"]["low"] += 1
        elif 0.4 <= commercial_value < 0.6:
            self.metrics["commercial_value_distribution"]["medium"] += 1
        elif 0.6 <= commercial_value < 0.8:
            self.metrics["commercial_value_distribution"]["high"] += 1
        else:
            self.metrics["commercial_value_distribution"]["very_high"] += 1
        
        # 更新置信度分佈
        confidence = query_result.get("confidence", 0)
        if 0 <= confidence < 0.2:
            self.metrics["confidence_distribution"]["very_low"] += 1
        elif 0.2 <= confidence < 0.4:
            self.metrics["confidence_distribution"]["low"] += 1
        elif 0.4 <= confidence < 0.6:
            self.metrics["confidence_distribution"]["medium"] += 1
        elif 0.6 <= confidence < 0.8:
            self.metrics["confidence_distribution"]["high"] += 1
        else:
            self.metrics["confidence_distribution"]["very_high"] += 1
        
        # 更新每日統計
        self._update_daily_stats(query_result)
        
        # 更新最近查詢
        self.recent_queries.append({
            "timestamp": datetime.now().isoformat(),
            "query": query_result.get("query", ""),
            "intent": intent,
            "commercial_value": commercial_value,
            "confidence": confidence,
            "is_commercial": query_result.get("is_commercial_intent", False)
        })
        
        # 只保留最近500個查詢
        if len(self.recent_queries) > 500:
            self.recent_queries = self.recent_queries[-500:]
        
        # 更新最後更新時間
        self.metrics["last_updated"] = datetime.now().isoformat()
        
        # 保存數據
        self._save_metrics()
        self._save_recent_queries()
    
    def get_summary_metrics(self) -> Dict[str, Any]:
        """獲取摘要指標"""
        return {
            "total_queries": self.metrics["total_queries"],
            "commercial_queries": self.metrics["commercial_queries"],
            "commercial_percentage": (self.metrics["commercial_queries"] / self.metrics["total_queries"]) * 100 
                                    if self.metrics["total_queries"] > 0 else 0,
            "top_intents": dict(sorted(self.metrics["intent_distribution"].items(), 
                               key=lambda x: x[1], reverse=True)[:5]),
            "commercial_value_distribution": self.metrics["commercial_value_distribution"],
            "confidence_distribution": self.metrics["confidence_distribution"],
            "last_updated": self.metrics["last_updated"]
        }
    
    def get_daily_trends(self, days: int = 7) -> Dict[str, Any]:
        """獲取每日趨勢數據"""
        # 獲取最近N天的日期
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days-1)
        date_range = [(start_date + timedelta(days=i)).strftime("%Y-%m-%d") 
                      for i in range(days)]
        
        # 準備數據
        daily_data = {
            "dates": date_range,
            "total_queries": [],
            "commercial_queries": [],
            "commercial_percentage": [],
            "commercial_value_avg": [],
            "confidence_avg": []
        }
        
        # 填充數據
        for date in date_range:
            if date in self.metrics["daily_stats"]:
                stats = self.metrics["daily_stats"][date]
                daily_data["total_queries"].append(stats["total_queries"])
                daily_data["commercial_queries"].append(stats["commercial_queries"])
                
                percentage = (stats["commercial_queries"] / stats["total_queries"]) * 100 \
                            if stats["total_queries"] > 0 else 0
                daily_data["commercial_percentage"].append(percentage)
                
                daily_data["commercial_value_avg"].append(stats["commercial_value_avg"])
                daily_data["confidence_avg"].append(stats["confidence_avg"])
            else:
                # 沒有該日期的數據，填充0
                daily_data["total_queries"].append(0)
                daily_data["commercial_queries"].append(0)
                daily_data["commercial_percentage"].append(0)
                daily_data["commercial_value_avg"].append(0)
                daily_data["confidence_avg"].append(0)
        
        return daily_data
    
    def get_recent_queries(self, limit: int = 20, 
                         filter_commercial: bool = False) -> List[Dict[str, Any]]:
        """獲取最近的查詢結果"""
        if filter_commercial:
            return [q for q in self.recent_queries if q.get("is_commercial", False)][-limit:]
        else:
            return self.recent_queries[-limit:]
    
    def generate_dashboard_charts(self) -> Dict[str, str]:
        """生成儀表板圖表，返回圖表文件路徑"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        chart_paths = {}
        
        # 1. 意圖分佈餅圖
        try:
            plt.figure(figsize=(10, 6))
            
            # 只顯示前10個最常見的意圖，其他歸為"其他"
            intent_counts = self.metrics["intent_distribution"]
            if len(intent_counts) > 10:
                top_intents = dict(sorted(intent_counts.items(), key=lambda x: x[1], reverse=True)[:9])
                top_intents["其他"] = sum([count for intent, count in intent_counts.items() 
                                       if intent not in top_intents])
            else:
                top_intents = intent_counts
            
            labels = list(top_intents.keys())
            sizes = list(top_intents.values())
            
            plt.pie(sizes, labels=labels, autopct='%1.1f%%', startangle=140)
            plt.axis('equal')
            plt.title('查詢意圖分佈')
            
            chart_file = self.data_dir / f"intent_distribution_{timestamp}.png"
            plt.savefig(chart_file)
            plt.close()
            chart_paths["intent_distribution"] = str(chart_file)
        except Exception as e:
            logger.error(f"生成意圖分佈圖表失敗: {e}")
        
        # 2. 商業價值分佈柱狀圖
        try:
            plt.figure(figsize=(10, 6))
            
            labels = ["很低 (0-0.2)", "低 (0.2-0.4)", "中等 (0.4-0.6)", "高 (0.6-0.8)", "很高 (0.8-1.0)"]
            values = list(self.metrics["commercial_value_distribution"].values())
            
            plt.bar(labels, values)
            plt.xlabel('商業價值範圍')
            plt.ylabel('查詢數量')
            plt.title('商業價值分佈')
            
            chart_file = self.data_dir / f"commercial_value_distribution_{timestamp}.png"
            plt.savefig(chart_file)
            plt.close()
            chart_paths["commercial_value_distribution"] = str(chart_file)
        except Exception as e:
            logger.error(f"生成商業價值分佈圖表失敗: {e}")
        
        # 3. 每日趨勢線圖
        try:
            daily_trends = self.get_daily_trends(days=14)
            plt.figure(figsize=(12, 6))
            
            plt.plot(daily_trends["dates"], daily_trends["commercial_percentage"], marker='o', label='商業查詢百分比')
            plt.plot(daily_trends["dates"], [cv*100 for cv in daily_trends["commercial_value_avg"]], marker='s', label='平均商業價值')
            plt.plot(daily_trends["dates"], [conf*100 for conf in daily_trends["confidence_avg"]], marker='^', label='平均置信度')
            
            plt.xlabel('日期')
            plt.ylabel('百分比')
            plt.title('查詢指標每日趨勢 (最近14天)')
            plt.xticks(rotation=45)
            plt.legend()
            plt.grid(True, alpha=0.3)
            plt.tight_layout()
            
            chart_file = self.data_dir / f"daily_trends_{timestamp}.png"
            plt.savefig(chart_file)
            plt.close()
            chart_paths["daily_trends"] = str(chart_file)
        except Exception as e:
            logger.error(f"生成每日趨勢圖表失敗: {e}")
        
        return chart_paths
    
    def export_data(self, output_file: Optional[str] = None) -> str:
        """導出儀表板數據"""
        if output_file is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = str(self.data_dir / f"dashboard_export_{timestamp}.json")
        
        try:
            export_data = {
                "metrics": self.metrics,
                "recent_queries": self.recent_queries,
                "export_timestamp": datetime.now().isoformat()
            }
            
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, ensure_ascii=False, indent=2)
            
            return output_file
        except Exception as e:
            logger.error(f"導出儀表板數據失敗: {e}")
            return ""
    
    def reset_metrics(self, confirm: bool = False) -> bool:
        """重置所有指標數據"""
        if not confirm:
            logger.warning("需要確認才能重置指標數據")
            return False
        
        # 備份當前數據
        self.export_data()
        
        # 重置數據
        self.metrics = self._initialize_metrics()
        self.recent_queries = []
        
        # 保存空數據
        self._save_metrics()
        self._save_recent_queries()
        
        logger.info("已重置所有指標數據")
        return True
