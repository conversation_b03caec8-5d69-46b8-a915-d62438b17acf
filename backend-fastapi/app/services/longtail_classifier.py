"""
長尾查詢識別系統 - 智能分類器
基於BERT和機器學習的多維度查詢分類器
"""

import os
import json
import pickle
import logging
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
import numpy as np
from datetime import datetime

# 配置日誌
logger = logging.getLogger(__name__)

try:
    import torch
    import torch.nn as nn
    from transformers import AutoTokenizer, AutoModel, AutoConfig
    from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
    from sklearn.model_selection import train_test_split
    from sklearn.metrics import classification_report, confusion_matrix
    from sklearn.preprocessing import StandardScaler
    import joblib
    ML_AVAILABLE = True
except ImportError as e:
    logger.warning(f"機器學習庫未安裝: {e}")
    ML_AVAILABLE = False

@dataclass
class ClassificationResult:
    """分類結果"""
    category: str
    confidence: float
    probabilities: Dict[str, float]
    features: Dict[str, Any]
    processing_time: float

@dataclass
class TrainingData:
    """訓練數據結構"""
    query: str
    category: str
    word_count: int
    char_count: int
    entity_count: int
    historical_frequency: float
    click_through_rate: float
    complexity_score: float
    intent_scores: Dict[str, float]
    is_longtail: bool

class LongTailClassifier:
    """長尾查詢分類器"""
    
    def __init__(self, model_path: Optional[str] = None, config: Optional[Dict] = None):
        self.config = config or {}
        self.model_path = model_path or "models/longtail_classifier"
        
        # 模型組件
        self.tokenizer = None
        self.bert_model = None
        self.rf_classifier = None
        self.gb_classifier = None
        self.scaler = None
        
        # 分類標籤
        self.categories = [
            'head_query',      # 頭部查詢
            'middle_query',    # 中部查詢
            'longtail_query',  # 長尾查詢
            'ultra_longtail'   # 超長尾查詢
        ]
        
        # 特徵維度
        self.bert_dim = 768
        self.stat_features_count = 10
        
        # 初始化模型
        if ML_AVAILABLE:
            self._initialize_models()
            if model_path and os.path.exists(model_path):
                self.load_model(model_path)

    def _initialize_models(self):
        """初始化模型組件"""
        try:
            # 初始化BERT模型
            model_name = self.config.get('bert_model', 'bert-base-multilingual-cased')
            self.tokenizer = AutoTokenizer.from_pretrained(model_name)
            self.bert_model = AutoModel.from_pretrained(model_name)
            self.bert_model.eval()
            
            # 初始化分類器
            self.rf_classifier = RandomForestClassifier(
                n_estimators=100,
                max_depth=10,
                random_state=42,
                n_jobs=-1
            )
            
            self.gb_classifier = GradientBoostingClassifier(
                n_estimators=100,
                max_depth=6,
                random_state=42
            )
            
            # 初始化標準化器
            self.scaler = StandardScaler()
            
            logger.info("模型組件初始化完成")
            
        except Exception as e:
            logger.error(f"模型初始化失敗: {e}")
            raise

    def extract_bert_features(self, queries: List[str]) -> np.ndarray:
        """使用BERT提取查詢特徵"""
        if not ML_AVAILABLE or not self.bert_model:
            # 返回隨機特徵作為fallback
            return np.random.rand(len(queries), self.bert_dim)
        
        features = []
        
        try:
            for query in queries:
                # 分詞和編碼
                inputs = self.tokenizer(
                    query,
                    return_tensors="pt",
                    max_length=128,
                    truncation=True,
                    padding=True
                )
                
                # 前向傳播
                with torch.no_grad():
                    outputs = self.bert_model(**inputs)
                    # 使用[CLS] token的表示
                    query_embedding = outputs.last_hidden_state[:, 0, :].numpy()
                    features.append(query_embedding.flatten())
            
            return np.array(features)
            
        except Exception as e:
            logger.error(f"BERT特徵提取失敗: {e}")
            # 返回零向量作為fallback
            return np.zeros((len(queries), self.bert_dim))

    def extract_statistical_features(self, training_data: List[TrainingData]) -> np.ndarray:
        """提取統計特徵"""
        features = []
        
        for item in training_data:
            # 基礎統計特徵
            stats = [
                item.word_count,
                item.char_count,
                item.entity_count,
                item.historical_frequency,
                item.click_through_rate,
                item.complexity_score,
                # 意圖分數
                item.intent_scores.get('informational', 0),
                item.intent_scores.get('commercial', 0),
                item.intent_scores.get('transactional', 0),
                item.intent_scores.get('local', 0)
            ]
            features.append(stats)
        
        return np.array(features)

    def train(self, training_data: List[TrainingData], validation_split: float = 0.2):
        """訓練分類器"""
        if not ML_AVAILABLE:
            logger.warning("機器學習庫未安裝，無法訓練模型")
            return
        
        try:
            logger.info(f"開始訓練，數據量: {len(training_data)}")
            
            # 準備數據
            queries = [item.query for item in training_data]
            labels = [item.category for item in training_data]
            
            # 提取BERT特徵
            logger.info("提取BERT特徵...")
            bert_features = self.extract_bert_features(queries)
            
            # 提取統計特徵
            logger.info("提取統計特徵...")
            stat_features = self.extract_statistical_features(training_data)
            
            # 合併特徵
            all_features = np.hstack([bert_features, stat_features])
            
            # 標準化特徵
            all_features = self.scaler.fit_transform(all_features)
            
            # 分割訓練和驗證集
            X_train, X_val, y_train, y_val = train_test_split(
                all_features, labels, test_size=validation_split, random_state=42
            )
            
            # 訓練隨機森林
            logger.info("訓練隨機森林分類器...")
            self.rf_classifier.fit(X_train, y_train)
            
            # 訓練梯度提升
            logger.info("訓練梯度提升分類器...")
            self.gb_classifier.fit(X_train, y_train)
            
            # 評估模型
            self._evaluate_models(X_val, y_val)
            
            logger.info("模型訓練完成")
            
        except Exception as e:
            logger.error(f"模型訓練失敗: {e}")
            raise

    def _evaluate_models(self, X_val: np.ndarray, y_val: List[str]):
        """評估模型性能"""
        try:
            # 隨機森林評估
            rf_pred = self.rf_classifier.predict(X_val)
            rf_report = classification_report(y_val, rf_pred, output_dict=True)
            
            # 梯度提升評估
            gb_pred = self.gb_classifier.predict(X_val)
            gb_report = classification_report(y_val, gb_pred, output_dict=True)
            
            logger.info(f"隨機森林準確率: {rf_report['accuracy']:.3f}")
            logger.info(f"梯度提升準確率: {gb_report['accuracy']:.3f}")
            
            # 保存評估結果
            evaluation_results = {
                'random_forest': rf_report,
                'gradient_boosting': gb_report,
                'timestamp': datetime.utcnow().isoformat()
            }
            
            os.makedirs(self.model_path, exist_ok=True)
            with open(f"{self.model_path}/evaluation.json", 'w') as f:
                json.dump(evaluation_results, f, indent=2)
                
        except Exception as e:
            logger.error(f"模型評估失敗: {e}")

    def classify(self, query: str, metadata: Optional[Dict] = None) -> ClassificationResult:
        """分類單個查詢"""
        start_time = datetime.utcnow()
        
        try:
            # 提取BERT特徵
            bert_features = self.extract_bert_features([query])[0]
            
            # 提取統計特徵
            stat_features = self._extract_single_query_stats(query, metadata or {})
            
            # 合併特徵
            features = np.hstack([bert_features, stat_features]).reshape(1, -1)
            
            # 標準化特徵
            if self.scaler:
                features = self.scaler.transform(features)
            
            # 集成預測
            predictions = self._ensemble_predict(features)
            
            # 計算處理時間
            processing_time = (datetime.utcnow() - start_time).total_seconds()
            
            return ClassificationResult(
                category=predictions['category'],
                confidence=predictions['confidence'],
                probabilities=predictions['probabilities'],
                features={
                    'bert_features_shape': bert_features.shape,
                    'stat_features': stat_features.tolist(),
                    'query_length': len(query),
                    'word_count': len(query.split())
                },
                processing_time=processing_time
            )
            
        except Exception as e:
            logger.error(f"查詢分類失敗: {e}")
            # 返回默認結果
            processing_time = (datetime.utcnow() - start_time).total_seconds()
            return ClassificationResult(
                category='middle_query',
                confidence=0.5,
                probabilities={cat: 0.25 for cat in self.categories},
                features={},
                processing_time=processing_time
            )

    def _extract_single_query_stats(self, query: str, metadata: Dict) -> np.ndarray:
        """提取單個查詢的統計特徵"""
        stats = [
            len(query.split()),  # word_count
            len(query),          # char_count
            metadata.get('entity_count', 0),
            metadata.get('frequency', 0.1),
            metadata.get('ctr', 0.0),
            metadata.get('complexity_score', 0.5),
            metadata.get('informational_score', 0.0),
            metadata.get('commercial_score', 0.0),
            metadata.get('transactional_score', 0.0),
            metadata.get('local_score', 0.0)
        ]
        
        return np.array(stats)

    def _ensemble_predict(self, features: np.ndarray) -> Dict[str, Any]:
        """集成預測"""
        if not ML_AVAILABLE or not self.rf_classifier or not self.gb_classifier:
            # 簡化預測
            return {
                'category': 'middle_query',
                'confidence': 0.5,
                'probabilities': {cat: 0.25 for cat in self.categories}
            }
        
        try:
            # 隨機森林預測
            rf_proba = self.rf_classifier.predict_proba(features)[0]
            rf_pred = self.rf_classifier.predict(features)[0]
            
            # 梯度提升預測
            gb_proba = self.gb_classifier.predict_proba(features)[0]
            gb_pred = self.gb_classifier.predict(features)[0]
            
            # 集成概率（加權平均）
            ensemble_proba = (rf_proba * 0.6 + gb_proba * 0.4)
            
            # 最終預測
            final_pred_idx = np.argmax(ensemble_proba)
            final_category = self.rf_classifier.classes_[final_pred_idx]
            final_confidence = ensemble_proba[final_pred_idx]
            
            # 構建概率字典
            probabilities = {}
            for i, category in enumerate(self.rf_classifier.classes_):
                probabilities[category] = float(ensemble_proba[i])
            
            return {
                'category': final_category,
                'confidence': float(final_confidence),
                'probabilities': probabilities
            }
            
        except Exception as e:
            logger.error(f"集成預測失敗: {e}")
            return {
                'category': 'middle_query',
                'confidence': 0.5,
                'probabilities': {cat: 0.25 for cat in self.categories}
            }

    def batch_classify(self, queries: List[str], metadata_list: Optional[List[Dict]] = None) -> List[ClassificationResult]:
        """批量分類查詢"""
        results = []
        metadata_list = metadata_list or [{}] * len(queries)
        
        for i, query in enumerate(queries):
            result = self.classify(query, metadata_list[i])
            results.append(result)
        
        return results

    def save_model(self, path: Optional[str] = None):
        """保存模型"""
        if not ML_AVAILABLE:
            logger.warning("機器學習庫未安裝，無法保存模型")
            return
        
        save_path = path or self.model_path
        os.makedirs(save_path, exist_ok=True)
        
        try:
            # 保存分類器
            if self.rf_classifier:
                joblib.dump(self.rf_classifier, f"{save_path}/rf_classifier.pkl")
            
            if self.gb_classifier:
                joblib.dump(self.gb_classifier, f"{save_path}/gb_classifier.pkl")
            
            if self.scaler:
                joblib.dump(self.scaler, f"{save_path}/scaler.pkl")
            
            # 保存配置
            config = {
                'categories': self.categories,
                'bert_dim': self.bert_dim,
                'stat_features_count': self.stat_features_count,
                'model_version': '1.0',
                'saved_at': datetime.utcnow().isoformat()
            }
            
            with open(f"{save_path}/config.json", 'w') as f:
                json.dump(config, f, indent=2)
            
            logger.info(f"模型保存完成: {save_path}")
            
        except Exception as e:
            logger.error(f"模型保存失敗: {e}")

    def load_model(self, path: str):
        """加載模型"""
        if not ML_AVAILABLE:
            logger.warning("機器學習庫未安裝，無法加載模型")
            return
        
        try:
            # 加載配置
            with open(f"{path}/config.json", 'r') as f:
                config = json.load(f)
            
            self.categories = config['categories']
            
            # 加載分類器
            rf_path = f"{path}/rf_classifier.pkl"
            if os.path.exists(rf_path):
                self.rf_classifier = joblib.load(rf_path)
            
            gb_path = f"{path}/gb_classifier.pkl"
            if os.path.exists(gb_path):
                self.gb_classifier = joblib.load(gb_path)
            
            scaler_path = f"{path}/scaler.pkl"
            if os.path.exists(scaler_path):
                self.scaler = joblib.load(scaler_path)
            
            logger.info(f"模型加載完成: {path}")
            
        except Exception as e:
            logger.error(f"模型加載失敗: {e}")

    def get_feature_importance(self) -> Dict[str, float]:
        """獲取特徵重要性"""
        if not ML_AVAILABLE or not self.rf_classifier:
            return {}
        
        try:
            # 獲取隨機森林的特徵重要性
            importance = self.rf_classifier.feature_importances_
            
            # 構建特徵名稱
            feature_names = []
            
            # BERT特徵
            for i in range(self.bert_dim):
                feature_names.append(f"bert_dim_{i}")
            
            # 統計特徵
            stat_names = [
                'word_count', 'char_count', 'entity_count', 'frequency',
                'ctr', 'complexity', 'informational', 'commercial',
                'transactional', 'local'
            ]
            feature_names.extend(stat_names)
            
            # 構建重要性字典
            importance_dict = {}
            for i, name in enumerate(feature_names):
                if i < len(importance):
                    importance_dict[name] = float(importance[i])
            
            return importance_dict
            
        except Exception as e:
            logger.error(f"獲取特徵重要性失敗: {e}")
            return {}

    def generate_training_data_from_queries(self, queries_with_labels: List[Tuple[str, str]]) -> List[TrainingData]:
        """從查詢列表生成訓練數據"""
        training_data = []
        
        for query, label in queries_with_labels:
            # 計算基礎特徵
            words = query.split()
            word_count = len(words)
            char_count = len(query)
            
            # 簡化的特徵計算
            entity_count = len([w for w in words if w.istitle()])
            complexity_score = min(word_count * 0.1 + len(query) * 0.01, 1.0)
            
            # 模擬其他特徵
            historical_frequency = np.random.uniform(0.01, 0.5)
            click_through_rate = np.random.uniform(0.1, 0.8)
            
            intent_scores = {
                'informational': np.random.uniform(0.0, 1.0),
                'commercial': np.random.uniform(0.0, 1.0),
                'transactional': np.random.uniform(0.0, 1.0),
                'local': np.random.uniform(0.0, 1.0)
            }
            
            # 歸一化意圖分數
            total_intent = sum(intent_scores.values())
            if total_intent > 0:
                intent_scores = {k: v/total_intent for k, v in intent_scores.items()}
            
            training_data.append(TrainingData(
                query=query,
                category=label,
                word_count=word_count,
                char_count=char_count,
                entity_count=entity_count,
                historical_frequency=historical_frequency,
                click_through_rate=click_through_rate,
                complexity_score=complexity_score,
                intent_scores=intent_scores,
                is_longtail=(label in ['longtail_query', 'ultra_longtail'])
            ))
        
        return training_data
