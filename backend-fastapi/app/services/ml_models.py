"""
長尾查詢識別系統 - 機器學習模型
實現BERT多語言模型、句子嵌入和語義相似度計算
"""

import os
import json
import logging
import asyncio
from typing import List, Dict, Any, Optional, Tuple, Union
from pathlib import Path
import numpy as np
from datetime import datetime

# 配置日誌
logger = logging.getLogger(__name__)

try:
    import torch
    import torch.nn as nn
    from transformers import (
        AutoModel, AutoTokenizer, AutoConfig,
        BertModel, BertTokenizer,
        XLMRobertaModel, XLMRobertaTokenizer
    )
    from sentence_transformers import SentenceTransformer
    import faiss
    MODELS_AVAILABLE = True
except ImportError as e:
    logger.warning(f"機器學習庫未安裝: {e}")
    MODELS_AVAILABLE = False

class ModelConfig:
    """模型配置類"""
    
    def __init__(self, config_path: Optional[str] = None):
        self.config_path = config_path or "models/models.config"
        self.config = self._load_config()
        
    def _load_config(self) -> Dict[str, Any]:
        """加載模型配置"""
        try:
            if os.path.exists(self.config_path):
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            else:
                return self._get_default_config()
        except Exception as e:
            logger.error(f"加載模型配置失敗: {e}")
            return self._get_default_config()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """獲取默認配置"""
        return {
            "language_models": {
                "bert_multilingual": "bert-base-multilingual-cased",
                "xlm_roberta": "xlm-roberta-base",
                "distilbert_multilingual": "distilbert-base-multilingual-cased"
            },
            "sentence_encoders": {
                "mpnet_base": "all-mpnet-base-v2",
                "paraphrase_multilingual": "paraphrase-multilingual-mpnet-base-v2",
                "distiluse_multilingual": "distiluse-base-multilingual-cased"
            },
            "default_models": {
                "query_classification": "bert-base-multilingual-cased",
                "sentence_embedding": "paraphrase-multilingual-mpnet-base-v2",
                "intent_detection": "xlm-roberta-base"
            }
        }
    
    def get_model_name(self, model_type: str, model_key: str) -> str:
        """獲取模型名稱"""
        return self.config.get(model_type, {}).get(model_key, "")
    
    def get_default_model(self, task: str) -> str:
        """獲取默認模型"""
        return self.config.get("default_models", {}).get(task, "")

class QueryEmbeddingModel:
    """查詢嵌入模型"""
    
    def __init__(self, model_config: ModelConfig):
        self.config = model_config
        self.models = {}
        self.tokenizers = {}
        self.sentence_transformers = {}
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        
    async def initialize_models(self):
        """初始化模型"""
        if not MODELS_AVAILABLE:
            raise RuntimeError("機器學習庫未安裝，無法初始化模型")
        
        try:
            # 初始化BERT多語言模型
            await self._load_bert_models()
            
            # 初始化句子編碼模型
            await self._load_sentence_transformers()
            
            logger.info("所有模型初始化完成")
            
        except Exception as e:
            logger.error(f"模型初始化失敗: {e}")
            raise
    
    async def _load_bert_models(self):
        """加載BERT模型"""
        bert_model_name = self.config.get_default_model("query_classification")
        
        try:
            # 加載分詞器
            tokenizer = AutoTokenizer.from_pretrained(bert_model_name)
            self.tokenizers["bert"] = tokenizer
            
            # 加載模型
            model = AutoModel.from_pretrained(bert_model_name)
            model.to(self.device)
            model.eval()
            self.models["bert"] = model
            
            logger.info(f"BERT模型加載完成: {bert_model_name}")
            
        except Exception as e:
            logger.error(f"BERT模型加載失敗: {e}")
            raise
    
    async def _load_sentence_transformers(self):
        """加載句子編碼模型"""
        sentence_model_name = self.config.get_default_model("sentence_embedding")
        
        try:
            model = SentenceTransformer(sentence_model_name)
            self.sentence_transformers["default"] = model
            
            logger.info(f"句子編碼模型加載完成: {sentence_model_name}")
            
        except Exception as e:
            logger.error(f"句子編碼模型加載失敗: {e}")
            raise
    
    async def encode_query_bert(self, query: str) -> np.ndarray:
        """使用BERT編碼查詢"""
        try:
            tokenizer = self.tokenizers["bert"]
            model = self.models["bert"]
            
            # 分詞
            inputs = tokenizer(
                query,
                return_tensors="pt",
                padding=True,
                truncation=True,
                max_length=512
            )
            
            # 移動到設備
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # 前向傳播
            with torch.no_grad():
                outputs = model(**inputs)
                # 使用[CLS]標記的嵌入
                embeddings = outputs.last_hidden_state[:, 0, :].cpu().numpy()
            
            return embeddings[0]  # 返回第一個樣本的嵌入
            
        except Exception as e:
            logger.error(f"BERT編碼失敗: {e}")
            raise
    
    async def encode_query_sentence_transformer(self, query: str) -> np.ndarray:
        """使用Sentence Transformer編碼查詢"""
        try:
            model = self.sentence_transformers["default"]
            embeddings = model.encode([query])
            return embeddings[0]
            
        except Exception as e:
            logger.error(f"Sentence Transformer編碼失敗: {e}")
            raise
    
    async def encode_queries_batch(
        self, 
        queries: List[str], 
        model_type: str = "sentence_transformer"
    ) -> np.ndarray:
        """批量編碼查詢"""
        try:
            if model_type == "sentence_transformer":
                model = self.sentence_transformers["default"]
                embeddings = model.encode(queries, show_progress_bar=True)
                return embeddings
            elif model_type == "bert":
                embeddings = []
                for query in queries:
                    embedding = await self.encode_query_bert(query)
                    embeddings.append(embedding)
                return np.array(embeddings)
            else:
                raise ValueError(f"不支持的模型類型: {model_type}")
                
        except Exception as e:
            logger.error(f"批量編碼失敗: {e}")
            raise

class SemanticSimilarityCalculator:
    """語義相似度計算器"""
    
    def __init__(self, embedding_model: QueryEmbeddingModel):
        self.embedding_model = embedding_model
        self.faiss_index = None
        self.query_embeddings = None
        self.query_texts = None
        
    def calculate_cosine_similarity(
        self, 
        embedding1: np.ndarray, 
        embedding2: np.ndarray
    ) -> float:
        """計算餘弦相似度"""
        try:
            # 歸一化向量
            norm1 = np.linalg.norm(embedding1)
            norm2 = np.linalg.norm(embedding2)
            
            if norm1 == 0 or norm2 == 0:
                return 0.0
            
            # 計算餘弦相似度
            similarity = np.dot(embedding1, embedding2) / (norm1 * norm2)
            return float(similarity)
            
        except Exception as e:
            logger.error(f"餘弦相似度計算失敗: {e}")
            return 0.0
    
    async def find_similar_queries(
        self,
        query: str,
        candidate_queries: List[str],
        threshold: float = 0.7,
        top_k: int = 10
    ) -> List[Tuple[str, float]]:
        """查找相似查詢"""
        try:
            # 編碼目標查詢
            query_embedding = await self.embedding_model.encode_query_sentence_transformer(query)
            
            # 編碼候選查詢
            candidate_embeddings = await self.embedding_model.encode_queries_batch(
                candidate_queries, "sentence_transformer"
            )
            
            # 計算相似度
            similarities = []
            for i, candidate_embedding in enumerate(candidate_embeddings):
                similarity = self.calculate_cosine_similarity(query_embedding, candidate_embedding)
                if similarity >= threshold:
                    similarities.append((candidate_queries[i], similarity))
            
            # 排序並返回前k個
            similarities.sort(key=lambda x: x[1], reverse=True)
            return similarities[:top_k]
            
        except Exception as e:
            logger.error(f"相似查詢查找失敗: {e}")
            return []
    
    def build_faiss_index(self, embeddings: np.ndarray):
        """構建FAISS索引"""
        try:
            dimension = embeddings.shape[1]
            
            # 創建FAISS索引
            self.faiss_index = faiss.IndexFlatIP(dimension)  # 內積索引
            
            # 歸一化嵌入向量
            faiss.normalize_L2(embeddings)
            
            # 添加向量到索引
            self.faiss_index.add(embeddings.astype('float32'))
            
            logger.info(f"FAISS索引構建完成，包含 {self.faiss_index.ntotal} 個向量")
            
        except Exception as e:
            logger.error(f"FAISS索引構建失敗: {e}")
            raise
    
    async def search_similar_with_faiss(
        self,
        query: str,
        top_k: int = 10,
        threshold: float = 0.7
    ) -> List[Tuple[str, float]]:
        """使用FAISS搜索相似查詢"""
        try:
            if self.faiss_index is None:
                raise ValueError("FAISS索引未構建")
            
            # 編碼查詢
            query_embedding = await self.embedding_model.encode_query_sentence_transformer(query)
            query_embedding = query_embedding.reshape(1, -1).astype('float32')
            
            # 歸一化
            faiss.normalize_L2(query_embedding)
            
            # 搜索
            scores, indices = self.faiss_index.search(query_embedding, top_k)
            
            # 過濾結果
            results = []
            for i, (score, idx) in enumerate(zip(scores[0], indices[0])):
                if score >= threshold and self.query_texts is not None:
                    results.append((self.query_texts[idx], float(score)))
            
            return results
            
        except Exception as e:
            logger.error(f"FAISS搜索失敗: {e}")
            return []

class QueryIntentClassifier:
    """查詢意圖分類器"""
    
    def __init__(self, embedding_model: QueryEmbeddingModel):
        self.embedding_model = embedding_model
        self.intent_patterns = self._load_intent_patterns()
        
    def _load_intent_patterns(self) -> Dict[str, List[str]]:
        """加載意圖模式"""
        return {
            "informational": [
                "什麼是", "如何", "怎麼", "為什麼", "哪裡", "何時", "誰",
                "what is", "how to", "why", "where", "when", "who"
            ],
            "transactional": [
                "買", "購買", "訂購", "下單", "付款",
                "buy", "purchase", "order", "checkout", "payment"
            ],
            "navigational": [
                "登入", "註冊", "首頁", "官網", "網站",
                "login", "register", "homepage", "website", "official"
            ],
            "commercial": [
                "價格", "比較", "評價", "推薦", "最好的", "便宜",
                "price", "compare", "review", "best", "cheap", "discount"
            ],
            "local": [
                "附近", "地址", "位置", "導航", "地圖",
                "near", "address", "location", "directions", "map"
            ]
        }
    
    async def classify_intent(self, query: str) -> Tuple[str, float]:
        """分類查詢意圖"""
        try:
            query_lower = query.lower()
            intent_scores = {}
            
            # 基於關鍵詞的簡單分類
            for intent, patterns in self.intent_patterns.items():
                score = 0.0
                for pattern in patterns:
                    if pattern.lower() in query_lower:
                        score += 1.0
                
                # 歸一化分數
                if patterns:
                    intent_scores[intent] = score / len(patterns)
            
            # 找到最高分數的意圖
            if intent_scores:
                best_intent = max(intent_scores, key=intent_scores.get)
                confidence = intent_scores[best_intent]
                return best_intent, confidence
            else:
                return "informational", 0.5  # 默認為信息型
                
        except Exception as e:
            logger.error(f"意圖分類失敗: {e}")
            return "informational", 0.0

class LongTailMLService:
    """長尾查詢機器學習服務"""
    
    def __init__(self):
        self.config = ModelConfig()
        self.embedding_model = None
        self.similarity_calculator = None
        self.intent_classifier = None
        self.initialized = False
        
    async def initialize(self):
        """初始化服務"""
        try:
            if not MODELS_AVAILABLE:
                logger.warning("機器學習庫未安裝，使用簡化版本")
                self.initialized = True
                return
            
            # 初始化嵌入模型
            self.embedding_model = QueryEmbeddingModel(self.config)
            await self.embedding_model.initialize_models()
            
            # 初始化相似度計算器
            self.similarity_calculator = SemanticSimilarityCalculator(self.embedding_model)
            
            # 初始化意圖分類器
            self.intent_classifier = QueryIntentClassifier(self.embedding_model)
            
            self.initialized = True
            logger.info("長尾查詢ML服務初始化完成")
            
        except Exception as e:
            logger.error(f"ML服務初始化失敗: {e}")
            raise
    
    async def get_query_embeddings(self, query: str) -> Dict[str, np.ndarray]:
        """獲取查詢嵌入向量"""
        if not self.initialized or not MODELS_AVAILABLE:
            return {}
        
        try:
            embeddings = {}
            
            # BERT嵌入
            bert_embedding = await self.embedding_model.encode_query_bert(query)
            embeddings["bert"] = bert_embedding
            
            # Sentence Transformer嵌入
            st_embedding = await self.embedding_model.encode_query_sentence_transformer(query)
            embeddings["sentence_transformer"] = st_embedding
            
            return embeddings
            
        except Exception as e:
            logger.error(f"獲取查詢嵌入失敗: {e}")
            return {}
    
    async def find_similar_queries(
        self,
        query: str,
        candidate_queries: List[str],
        threshold: float = 0.7,
        top_k: int = 10
    ) -> List[Tuple[str, float]]:
        """查找相似查詢"""
        if not self.initialized or not MODELS_AVAILABLE:
            return []
        
        return await self.similarity_calculator.find_similar_queries(
            query, candidate_queries, threshold, top_k
        )
    
    async def classify_query_intent(self, query: str) -> Tuple[str, float]:
        """分類查詢意圖"""
        if not self.initialized:
            return "informational", 0.5
        
        if MODELS_AVAILABLE and self.intent_classifier:
            return await self.intent_classifier.classify_intent(query)
        else:
            # 簡化版本的意圖分類
            return self._simple_intent_classification(query)
    
    def _simple_intent_classification(self, query: str) -> Tuple[str, float]:
        """簡化版本的意圖分類"""
        query_lower = query.lower()
        
        # 問題意圖
        question_words = ['什麼', '如何', '怎麼', '為什麼', '哪裡', '何時', '誰', 
                         'what', 'how', 'why', 'where', 'when', 'who', '?', '？']
        if any(word in query_lower for word in question_words):
            return "informational", 0.8
        
        # 交易意圖
        transaction_words = ['買', '購買', '訂購', 'buy', 'purchase', 'order']
        if any(word in query_lower for word in transaction_words):
            return "transactional", 0.8
        
        # 商業意圖
        commercial_words = ['價格', '比較', '評價', 'price', 'compare', 'review']
        if any(word in query_lower for word in commercial_words):
            return "commercial", 0.7
        
        # 本地意圖
        local_words = ['附近', '地址', '位置', 'near', 'address', 'location']
        if any(word in query_lower for word in local_words):
            return "local", 0.7
        
        return "informational", 0.5
