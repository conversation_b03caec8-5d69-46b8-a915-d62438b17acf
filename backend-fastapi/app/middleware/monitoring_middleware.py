"""
監控中間件
自動記錄請求指標和性能數據
"""

import time
import logging
from typing import Callable
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware

logger = logging.getLogger(__name__)

class MonitoringMiddleware(BaseHTTPMiddleware):
    """監控中間件 - 自動記錄請求指標"""
    
    def __init__(self, app, enable_monitoring: bool = True):
        super().__init__(app)
        self.enable_monitoring = enable_monitoring
        
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """處理請求並記錄指標"""
        if not self.enable_monitoring:
            return await call_next(request)
        
        # 記錄請求開始時間
        start_time = time.time()
        
        # 獲取端點信息
        endpoint = f"{request.method} {request.url.path}"
        
        try:
            # 處理請求
            response = await call_next(request)
            
            # 計算處理時間
            duration = time.time() - start_time
            
            # 記錄成功請求指標
            self._record_request_metric(
                endpoint=endpoint,
                duration=duration,
                status_code=response.status_code,
                error=None
            )
            
            # 添加性能頭部
            response.headers["X-Process-Time"] = str(duration)
            
            return response
            
        except Exception as e:
            # 計算處理時間
            duration = time.time() - start_time
            
            # 記錄錯誤請求指標
            self._record_request_metric(
                endpoint=endpoint,
                duration=duration,
                status_code=500,
                error=str(type(e).__name__)
            )
            
            logger.error(f"請求處理失敗: {endpoint}, 錯誤: {e}")
            raise
    
    def _record_request_metric(self, 
                              endpoint: str, 
                              duration: float, 
                              status_code: int,
                              error: str = None):
        """記錄請求指標"""
        try:
            from app.services.monitoring_system import record_request_metric
            record_request_metric(endpoint, duration, status_code, error)
        except Exception as e:
            # 避免監控系統錯誤影響主要功能
            logger.warning(f"記錄請求指標失敗: {e}")

def create_monitoring_middleware(enable_monitoring: bool = True):
    """創建監控中間件的工廠函數"""
    def middleware_factory(app):
        return MonitoringMiddleware(app, enable_monitoring)
    return middleware_factory
