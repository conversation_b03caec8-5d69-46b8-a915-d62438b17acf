"""
索引管理API端點
提供索引構建、更新和監控功能
"""

import logging
import time
from typing import List, Dict, Optional, Any
from fastapi import APIRouter, HTTPException, BackgroundTasks, Depends
from pydantic import BaseModel, Field
from datetime import datetime

# 項目依賴
from app.services.simple_index_manager import SimpleIndexManager

logger = logging.getLogger(__name__)

# 創建路由器
router = APIRouter(prefix="/index-management", tags=["索引管理"])

# 全局索引管理器實例
index_manager = SimpleIndexManager()

# Pydantic 模型
class IndexStatusResponse(BaseModel):
    """索引狀態響應"""
    index_exists: bool
    index_age_hours: float
    new_queries_count: int
    needs_rebuild: bool
    total_documents: int
    last_updated: Optional[str]
    is_building: bool

class IndexBuildRequest(BaseModel):
    """索引構建請求"""
    force: bool = Field(False, description="是否強制重建索引")
    async_build: bool = Field(True, description="是否異步構建")

class IndexBuildResponse(BaseModel):
    """索引構建響應"""
    status: str
    message: str
    documents_count: Optional[int] = None
    build_time_seconds: Optional[float] = None
    index_type: Optional[str] = None

class IndexSearchRequest(BaseModel):
    """索引搜索請求"""
    query: str = Field(..., min_length=1, max_length=500, description="搜索查詢")
    k: int = Field(10, ge=1, le=100, description="返回結果數量")
    filters: Optional[Dict[str, Any]] = Field(None, description="搜索過濾器")

class IndexSearchResult(BaseModel):
    """索引搜索結果"""
    id: str
    query: str
    score: float
    query_type: str
    intent: str
    semantic_category: str
    longtail_score: float

class IndexSearchResponse(BaseModel):
    """索引搜索響應"""
    query: str
    total_results: int
    results: List[IndexSearchResult]
    search_time_ms: float

class IndexStatsResponse(BaseModel):
    """索引統計響應"""
    index_exists: bool
    total_documents: int
    unique_keywords: int
    categories: int
    query_types: int
    score_range: Dict[str, float]
    metadata: Dict[str, Any]

# API 端點
@router.get("/status", response_model=IndexStatusResponse)
async def get_index_status():
    """
    獲取索引狀態
    """
    try:
        status = await index_manager.check_index_status()
        return IndexStatusResponse(**status)
        
    except Exception as e:
        logger.error(f"獲取索引狀態失敗: {e}")
        raise HTTPException(status_code=500, detail=f"獲取索引狀態失敗: {str(e)}")

@router.post("/build", response_model=IndexBuildResponse)
async def build_index(request: IndexBuildRequest, background_tasks: BackgroundTasks):
    """
    構建或重建索引
    """
    try:
        if request.async_build:
            # 異步構建
            background_tasks.add_task(index_manager.build_index_async, request.force)
            return IndexBuildResponse(
                status="started",
                message="索引構建已開始，將在背景執行"
            )
        else:
            # 同步構建
            result = await index_manager.build_index_async(request.force)
            return IndexBuildResponse(**result)
            
    except Exception as e:
        logger.error(f"索引構建失敗: {e}")
        raise HTTPException(status_code=500, detail=f"索引構建失敗: {str(e)}")

@router.post("/search", response_model=IndexSearchResponse)
async def search_index(request: IndexSearchRequest):
    """
    在索引中搜索相似查詢
    """
    try:
        start_time = datetime.now()
        
        # 執行搜索
        results = await index_manager.search_simple_index(
            query=request.query,
            k=request.k,
            filters=request.filters
        )
        
        end_time = datetime.now()
        search_time_ms = (end_time - start_time).total_seconds() * 1000
        
        # 轉換結果格式
        search_results = [
            IndexSearchResult(**result) for result in results
        ]
        
        return IndexSearchResponse(
            query=request.query,
            total_results=len(search_results),
            results=search_results,
            search_time_ms=round(search_time_ms, 2)
        )
        
    except Exception as e:
        logger.error(f"索引搜索失敗: {e}")
        raise HTTPException(status_code=500, detail=f"索引搜索失敗: {str(e)}")

@router.get("/stats", response_model=IndexStatsResponse)
async def get_index_stats():
    """
    獲取索引統計信息
    """
    try:
        stats = await index_manager.get_index_stats()
        
        # 處理缺失的字段
        stats.setdefault('index_exists', False)
        stats.setdefault('total_documents', 0)
        stats.setdefault('unique_keywords', 0)
        stats.setdefault('categories', 0)
        stats.setdefault('query_types', 0)
        stats.setdefault('score_range', {'min': 0, 'max': 1, 'avg': 0.5})
        stats.setdefault('metadata', {})
        
        return IndexStatsResponse(**stats)
        
    except Exception as e:
        logger.error(f"獲取索引統計失敗: {e}")
        raise HTTPException(status_code=500, detail=f"獲取索引統計失敗: {str(e)}")

@router.delete("/clear")
async def clear_index():
    """
    清除索引文件
    """
    try:
        import shutil
        
        # 清除索引文件
        if index_manager.index_dir.exists():
            for file in index_manager.index_dir.glob("*.pkl"):
                file.unlink()
            for file in index_manager.index_dir.glob("*.index"):
                file.unlink()
        
        # 重置元數據
        index_manager.metadata = {
            "version": "1.0",
            "created_at": None,
            "updated_at": None,
            "total_documents": 0,
            "index_hash": None,
            "last_query_id": None,
            "rebuild_count": 0
        }
        index_manager._save_metadata()
        
        return {
            "status": "success",
            "message": "索引已清除"
        }
        
    except Exception as e:
        logger.error(f"清除索引失敗: {e}")
        raise HTTPException(status_code=500, detail=f"清除索引失敗: {str(e)}")

@router.get("/health")
async def health_check():
    """
    索引管理健康檢查
    """
    try:
        status = await index_manager.check_index_status()
        
        return {
            "status": "healthy",
            "timestamp": datetime.now(),
            "index_manager": {
                "index_exists": status.get("index_exists", False),
                "is_building": status.get("is_building", False),
                "total_documents": status.get("total_documents", 0)
            }
        }
        
    except Exception as e:
        logger.error(f"索引管理健康檢查失敗: {e}")
        return {
            "status": "unhealthy",
            "timestamp": datetime.now(),
            "error": str(e)
        }

@router.post("/auto-rebuild")
async def auto_rebuild_if_needed(background_tasks: BackgroundTasks):
    """
    自動重建索引（如果需要）
    """
    try:
        status = await index_manager.check_index_status()
        
        if status.get("needs_rebuild", False):
            background_tasks.add_task(index_manager.build_index_async, False)
            return {
                "status": "rebuild_started",
                "message": "檢測到需要重建索引，已開始重建",
                "reason": {
                    "index_exists": status.get("index_exists", False),
                    "index_age_hours": status.get("index_age_hours", 0),
                    "new_queries_count": status.get("new_queries_count", 0)
                }
            }
        else:
            return {
                "status": "no_rebuild_needed",
                "message": "索引不需要重建",
                "index_status": status
            }
            
    except Exception as e:
        logger.error(f"自動重建檢查失敗: {e}")
        raise HTTPException(status_code=500, detail=f"自動重建檢查失敗: {str(e)}")

# 定期任務端點
@router.post("/schedule-maintenance")
async def schedule_maintenance(background_tasks: BackgroundTasks):
    """
    安排索引維護任務
    """
    try:
        async def maintenance_task():
            """維護任務"""
            try:
                # 檢查並重建索引
                status = await index_manager.check_index_status()
                if status.get("needs_rebuild", False):
                    await index_manager.build_index_async(False)
                    logger.info("定期維護：索引重建完成")
                else:
                    logger.info("定期維護：索引無需重建")
                    
            except Exception as e:
                logger.error(f"定期維護任務失敗: {e}")
        
        background_tasks.add_task(maintenance_task)
        
        return {
            "status": "scheduled",
            "message": "索引維護任務已安排"
        }
        
    except Exception as e:
        logger.error(f"安排維護任務失敗: {e}")
        raise HTTPException(status_code=500, detail=f"安排維護任務失敗: {str(e)}")

@router.get("/query-samples")
async def get_query_samples(limit: int = 10):
    """
    獲取查詢樣本（用於測試）
    """
    try:
        documents = await index_manager.get_queries_for_indexing(limit=limit)
        
        samples = []
        for doc in documents[:limit]:
            samples.append({
                "id": doc["id"],
                "query": doc["content"],
                "query_type": doc["query_type"],
                "longtail_score": doc["longtail_score"],
                "semantic_category": doc["semantic_category"]
            })
        
        return {
            "total_available": len(documents),
            "samples": samples
        }
        
    except Exception as e:
        logger.error(f"獲取查詢樣本失敗: {e}")
        raise HTTPException(status_code=500, detail=f"獲取查詢樣本失敗: {str(e)}")

# 模型訓練相關端點
@router.post("/train-model")
async def train_model(background_tasks: BackgroundTasks, force_retrain: bool = False):
    """
    訓練查詢分類模型
    """
    try:
        from app.services.simple_model_trainer import BasicModelTrainer

        trainer = BasicModelTrainer()

        if force_retrain:
            # 同步訓練
            result = await trainer.train_model(force_retrain=True)
            return result
        else:
            # 異步訓練
            background_tasks.add_task(trainer.train_model)
            return {
                "status": "started",
                "message": "模型訓練已開始，將在背景執行"
            }

    except Exception as e:
        logger.error(f"模型訓練失敗: {e}")
        raise HTTPException(status_code=500, detail=f"模型訓練失敗: {str(e)}")

@router.post("/predict")
async def predict_queries(texts: List[str]):
    """
    使用訓練好的模型預測查詢類型
    """
    try:
        from app.services.simple_model_trainer import BasicModelTrainer

        trainer = BasicModelTrainer()
        result = await trainer.predict(texts)
        return result

    except Exception as e:
        logger.error(f"模型預測失敗: {e}")
        raise HTTPException(status_code=500, detail=f"模型預測失敗: {str(e)}")

@router.get("/model-stats")
async def get_model_stats():
    """
    獲取模型統計信息
    """
    try:
        from app.services.simple_model_trainer import BasicModelTrainer

        trainer = BasicModelTrainer()
        stats = await trainer.get_model_stats()
        return stats

    except Exception as e:
        logger.error(f"獲取模型統計失敗: {e}")
        raise HTTPException(status_code=500, detail=f"獲取模型統計失敗: {str(e)}")

# 性能優化相關端點
@router.get("/performance-stats")
async def get_performance_stats():
    """
    獲取性能統計信息
    """
    try:
        from app.services.performance_optimizer import PerformanceOptimizer

        optimizer = PerformanceOptimizer()
        stats = optimizer.get_performance_stats()
        return stats

    except Exception as e:
        logger.error(f"獲取性能統計失敗: {e}")
        raise HTTPException(status_code=500, detail=f"獲取性能統計失敗: {str(e)}")

@router.post("/batch-analyze")
async def batch_analyze_queries(queries: List[str], use_cache: bool = True, batch_size: Optional[int] = None):
    """
    批量分析查詢（性能優化版）
    """
    try:
        from app.services.performance_optimizer import PerformanceOptimizer
        from app.services.simple_model_trainer import BasicModelTrainer

        if not queries:
            return {"status": "no_data", "message": "沒有提供查詢數據"}

        optimizer = PerformanceOptimizer()
        trainer = BasicModelTrainer()

        # 確保模型已載入
        await trainer.load_model()

        # 定義分析函數
        async def analyze_query(query: str):
            result = await trainer.predict([query])
            if result["status"] == "success" and result["predictions"]:
                return result["predictions"][0]
            return None

        # 優化批量分析
        start_time = time.time()
        results = await optimizer.optimize_query_analysis(
            queries,
            analyze_query,
            use_cache=use_cache,
            batch_size=batch_size
        )
        total_time = time.time() - start_time

        # 過濾None結果
        valid_results = [r for r in results if r is not None]

        return {
            "status": "success",
            "total_queries": len(queries),
            "successful_analyses": len(valid_results),
            "failed_analyses": len(queries) - len(valid_results),
            "results": valid_results,
            "processing_time_seconds": round(total_time, 3),
            "queries_per_second": round(len(queries) / total_time, 2) if total_time > 0 else 0,
            "used_cache": use_cache,
            "batch_size": batch_size or optimizer.batch_size
        }

    except Exception as e:
        logger.error(f"批量分析失敗: {e}")
        raise HTTPException(status_code=500, detail=f"批量分析失敗: {str(e)}")

@router.post("/clear-cache")
async def clear_performance_cache():
    """
    清除性能緩存
    """
    try:
        from app.services.performance_optimizer import PerformanceOptimizer

        optimizer = PerformanceOptimizer()
        await optimizer.clear_cache()

        return {
            "status": "success",
            "message": "性能緩存已清除"
        }

    except Exception as e:
        logger.error(f"清除緩存失敗: {e}")
        raise HTTPException(status_code=500, detail=f"清除緩存失敗: {str(e)}")

@router.post("/benchmark")
async def run_performance_benchmark(query_count: int = 100, use_cache: bool = True):
    """
    運行性能基準測試
    """
    try:
        from app.services.performance_optimizer import PerformanceOptimizer
        from app.services.simple_model_trainer import BasicModelTrainer
        import time

        if query_count > 1000:
            raise HTTPException(status_code=400, detail="查詢數量不能超過1000")

        optimizer = PerformanceOptimizer()
        trainer = BasicModelTrainer()

        # 生成測試查詢
        base_queries = [
            "AI人工智能應用案例分析",
            "機器學習演算法優化策略",
            "SEO優化",
            "Python開發",
            "如何選擇最適合的深度學習框架進行自然語言處理任務"
        ]

        test_queries = []
        for i in range(query_count):
            base_query = base_queries[i % len(base_queries)]
            test_queries.append(f"{base_query} {i}")

        test_queries = test_queries[:query_count]

        # 確保模型已載入
        await trainer.load_model()

        # 定義分析函數
        async def analyze_query(query: str):
            result = await trainer.predict([query])
            if result["status"] == "success" and result["predictions"]:
                return result["predictions"][0]
            return None

        # 運行基準測試
        start_time = time.time()
        results = await optimizer.optimize_query_analysis(
            test_queries,
            analyze_query,
            use_cache=use_cache,
            batch_size=50
        )
        total_time = time.time() - start_time

        # 統計結果
        valid_results = [r for r in results if r is not None]

        return {
            "status": "success",
            "benchmark_results": {
                "total_queries": len(test_queries),
                "successful_analyses": len(valid_results),
                "total_time_seconds": round(total_time, 3),
                "queries_per_second": round(len(test_queries) / total_time, 2) if total_time > 0 else 0,
                "avg_time_per_query_ms": round((total_time / len(test_queries)) * 1000, 2) if test_queries else 0,
                "used_cache": use_cache
            },
            "performance_stats": optimizer.get_performance_stats()
        }

    except Exception as e:
        logger.error(f"性能基準測試失敗: {e}")
        raise HTTPException(status_code=500, detail=f"性能基準測試失敗: {str(e)}")

# 監控相關端點
@router.get("/monitoring/health")
async def get_system_health():
    """
    獲取系統健康狀態
    """
    try:
        from app.services.monitoring_system import monitoring

        health_status = monitoring.get_health_status()
        return health_status

    except Exception as e:
        logger.error(f"獲取系統健康狀態失敗: {e}")
        raise HTTPException(status_code=500, detail=f"獲取系統健康狀態失敗: {str(e)}")

@router.get("/monitoring/metrics")
async def get_detailed_metrics(time_range_hours: int = 24):
    """
    獲取詳細監控指標
    """
    try:
        from app.services.monitoring_system import monitoring

        if time_range_hours > 168:  # 最多7天
            time_range_hours = 168

        metrics = monitoring.get_detailed_metrics(time_range_hours)
        return metrics

    except Exception as e:
        logger.error(f"獲取監控指標失敗: {e}")
        raise HTTPException(status_code=500, detail=f"獲取監控指標失敗: {str(e)}")

@router.get("/monitoring/prometheus")
async def get_prometheus_metrics():
    """
    獲取Prometheus格式的指標
    """
    try:
        from app.services.monitoring_system import monitoring

        prometheus_metrics = monitoring.get_prometheus_metrics()

        # 返回純文本格式
        from fastapi.responses import PlainTextResponse
        return PlainTextResponse(prometheus_metrics, media_type="text/plain")

    except Exception as e:
        logger.error(f"獲取Prometheus指標失敗: {e}")
        raise HTTPException(status_code=500, detail=f"獲取Prometheus指標失敗: {str(e)}")

class PredictionRecord(BaseModel):
    predicted_class: str
    actual_class: Optional[str] = None
    confidence: float = 0.0

@router.post("/monitoring/record-prediction")
async def record_prediction_accuracy(prediction: PredictionRecord):
    """
    記錄預測準確度指標
    """
    try:
        from app.services.monitoring_system import record_prediction_metric

        record_prediction_metric(prediction.predicted_class, prediction.actual_class, prediction.confidence)

        return {
            "status": "success",
            "message": "預測指標已記錄",
            "predicted_class": prediction.predicted_class,
            "actual_class": prediction.actual_class,
            "confidence": prediction.confidence
        }

    except Exception as e:
        logger.error(f"記錄預測指標失敗: {e}")
        raise HTTPException(status_code=500, detail=f"記錄預測指標失敗: {str(e)}")

@router.post("/monitoring/save-metrics")
async def save_metrics_to_file():
    """
    手動保存指標到文件
    """
    try:
        from app.services.monitoring_system import monitoring

        await monitoring.save_metrics_to_file()

        return {
            "status": "success",
            "message": "指標已保存到文件"
        }

    except Exception as e:
        logger.error(f"保存指標失敗: {e}")
        raise HTTPException(status_code=500, detail=f"保存指標失敗: {str(e)}")

@router.post("/monitoring/reset")
async def reset_monitoring_metrics():
    """
    重置所有監控指標
    """
    try:
        from app.services.monitoring_system import monitoring

        monitoring.reset_metrics()

        return {
            "status": "success",
            "message": "所有監控指標已重置"
        }

    except Exception as e:
        logger.error(f"重置監控指標失敗: {e}")
        raise HTTPException(status_code=500, detail=f"重置監控指標失敗: {str(e)}")

@router.get("/monitoring/dashboard")
async def get_monitoring_dashboard():
    """
    獲取監控儀表板數據
    """
    try:
        from app.services.monitoring_system import monitoring

        # 獲取各種時間範圍的數據
        health = monitoring.get_health_status()
        metrics_1h = monitoring.get_detailed_metrics(1)
        metrics_24h = monitoring.get_detailed_metrics(24)

        dashboard_data = {
            "health_status": health,
            "current_metrics": {
                "last_hour": metrics_1h,
                "last_24_hours": metrics_24h
            },
            "summary": {
                "system_status": health["status"],
                "uptime": health["uptime_human"],
                "total_requests": health["total_requests"],
                "error_rate": health["error_rate_percent"],
                "avg_response_time": health["avg_response_time_ms"],
                "prediction_accuracy": health["prediction_accuracy"]
            }
        }

        return dashboard_data

    except Exception as e:
        logger.error(f"獲取監控儀表板失敗: {e}")
        raise HTTPException(status_code=500, detail=f"獲取監控儀表板失敗: {str(e)}")
