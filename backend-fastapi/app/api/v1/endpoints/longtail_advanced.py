"""
長尾查詢識別系統 - 高級API端點
集成數據管道和智能分類器的完整API
"""

import asyncio
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
from fastapi import APIRouter, HTTPException, BackgroundTasks, Depends
from pydantic import BaseModel, Field

from app.services.query_data_pipeline import QueryDataPipeline, QueryEvent, QueryFeatures
from app.services.longtail_classifier import LongTailClassifier, ClassificationResult, TrainingData

# 配置日誌
logger = logging.getLogger(__name__)

# 創建路由器
router = APIRouter()

# 全局實例
pipeline = None
classifier = None

# 數據模型
class QuerySubmission(BaseModel):
    """查詢提交請求"""
    query: str = Field(..., min_length=1, max_length=500)
    user_id: str = Field(..., min_length=1)
    session_id: str = Field(..., min_length=1)
    source: str = Field(default="api")
    user_agent: str = Field(default="")
    ip_address: str = Field(default="127.0.0.1")
    referer: Optional[str] = None

class AdvancedAnalysisRequest(BaseModel):
    """高級分析請求"""
    query: str = Field(..., min_length=1, max_length=500)
    include_features: bool = True
    include_classification: bool = True
    include_optimization: bool = True
    metadata: Optional[Dict[str, Any]] = None

class AdvancedAnalysisResponse(BaseModel):
    """高級分析響應"""
    query: str
    features: Optional[Dict[str, Any]] = None
    classification: Optional[Dict[str, Any]] = None
    optimization_suggestions: List[str] = []
    processing_time: float
    timestamp: datetime

class BatchAnalysisRequest(BaseModel):
    """批量分析請求"""
    queries: List[str] = Field(..., min_items=1, max_items=100)
    include_features: bool = True
    include_classification: bool = True
    session_name: Optional[str] = None

class BatchAnalysisResponse(BaseModel):
    """批量分析響應"""
    session_id: str
    total_queries: int
    results: List[AdvancedAnalysisResponse]
    summary: Dict[str, Any]
    processing_time: float

class TrainingRequest(BaseModel):
    """訓練請求"""
    training_data: List[Dict[str, Any]]
    validation_split: float = Field(default=0.2, ge=0.1, le=0.5)
    save_model: bool = True

class PipelineStatus(BaseModel):
    """管道狀態"""
    is_running: bool
    processed_queries: int
    longtail_queries: int
    last_processed: Optional[datetime] = None
    error_count: int

def get_pipeline():
    """獲取數據管道實例"""
    global pipeline
    if pipeline is None:
        config = {
            'longtail_threshold': 0.1,
            'complexity_threshold': 0.6,
            'redis': {
                'host': 'localhost',
                'port': 6379,
                'db': 0,
                'decode_responses': True
            },
            'postgres': {
                'host': 'localhost',
                'port': 5432,
                'database': 'aiseo',
                'user': 'postgres',
                'password': 'password'
            }
        }
        pipeline = QueryDataPipeline(config)
    return pipeline

def get_classifier():
    """獲取分類器實例"""
    global classifier
    if classifier is None:
        config = {
            'bert_model': 'bert-base-multilingual-cased'
        }
        classifier = LongTailClassifier(config=config)
    return classifier

@router.post("/submit", response_model=Dict[str, str])
async def submit_query(
    request: QuerySubmission,
    background_tasks: BackgroundTasks,
    pipeline: QueryDataPipeline = Depends(get_pipeline)
):
    """提交查詢到數據管道"""
    try:
        # 創建查詢事件
        query_event = QueryEvent(
            query=request.query,
            user_id=request.user_id,
            session_id=request.session_id,
            timestamp=datetime.utcnow(),
            source=request.source,
            user_agent=request.user_agent,
            ip_address=request.ip_address,
            referer=request.referer
        )
        
        # 後台處理
        background_tasks.add_task(pipeline.process_query_event, query_event)
        
        return {
            "status": "submitted",
            "query_id": f"query_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}",
            "message": "查詢已提交處理"
        }
        
    except Exception as e:
        logger.error(f"提交查詢失敗: {e}")
        raise HTTPException(status_code=500, detail=f"提交查詢失敗: {str(e)}")

@router.post("/analyze/advanced", response_model=AdvancedAnalysisResponse)
async def advanced_analysis(
    request: AdvancedAnalysisRequest,
    pipeline: QueryDataPipeline = Depends(get_pipeline),
    classifier: LongTailClassifier = Depends(get_classifier)
):
    """高級查詢分析"""
    start_time = datetime.utcnow()
    
    try:
        result = AdvancedAnalysisResponse(
            query=request.query,
            processing_time=0.0,
            timestamp=start_time
        )
        
        # 提取特徵
        if request.include_features:
            features = pipeline.extract_features(request.query)
            result.features = {
                'word_count': features.word_count,
                'char_count': features.char_count,
                'entity_count': features.entity_count,
                'complexity_score': features.complexity_score,
                'has_question_words': features.has_question_words,
                'has_brand_names': features.has_brand_names,
                'has_location': features.has_location,
                'has_numbers': features.has_numbers,
                'language': features.language,
                'intent_scores': features.intent_score,
                'technical_terms': features.technical_terms,
                'frequency_score': features.frequency_score
            }
        
        # 分類
        if request.include_classification:
            classification_result = classifier.classify(
                request.query, 
                request.metadata or {}
            )
            result.classification = {
                'category': classification_result.category,
                'confidence': classification_result.confidence,
                'probabilities': classification_result.probabilities
            }
        
        # 優化建議
        if request.include_optimization:
            result.optimization_suggestions = _generate_optimization_suggestions(
                request.query,
                result.features,
                result.classification
            )
        
        # 計算處理時間
        result.processing_time = (datetime.utcnow() - start_time).total_seconds()
        
        return result
        
    except Exception as e:
        logger.error(f"高級分析失敗: {e}")
        raise HTTPException(status_code=500, detail=f"分析失敗: {str(e)}")

@router.post("/analyze/batch", response_model=BatchAnalysisResponse)
async def batch_analysis(
    request: BatchAnalysisRequest,
    pipeline: QueryDataPipeline = Depends(get_pipeline),
    classifier: LongTailClassifier = Depends(get_classifier)
):
    """批量查詢分析"""
    start_time = datetime.utcnow()
    
    try:
        results = []
        
        for query in request.queries:
            analysis_request = AdvancedAnalysisRequest(
                query=query,
                include_features=request.include_features,
                include_classification=request.include_classification
            )
            
            result = await advanced_analysis(analysis_request, pipeline, classifier)
            results.append(result)
        
        # 生成統計
        total_queries = len(results)
        longtail_count = 0
        category_counts = {}
        
        for result in results:
            if result.classification:
                category = result.classification['category']
                category_counts[category] = category_counts.get(category, 0) + 1
                
                if 'longtail' in category:
                    longtail_count += 1
        
        summary = {
            'total_queries': total_queries,
            'longtail_queries': longtail_count,
            'longtail_percentage': (longtail_count / total_queries * 100) if total_queries > 0 else 0,
            'category_distribution': category_counts,
            'avg_processing_time': sum(r.processing_time for r in results) / len(results) if results else 0
        }
        
        processing_time = (datetime.utcnow() - start_time).total_seconds()
        
        return BatchAnalysisResponse(
            session_id=f"batch_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}",
            total_queries=total_queries,
            results=results,
            summary=summary,
            processing_time=processing_time
        )
        
    except Exception as e:
        logger.error(f"批量分析失敗: {e}")
        raise HTTPException(status_code=500, detail=f"批量分析失敗: {str(e)}")

@router.post("/train")
async def train_classifier(
    request: TrainingRequest,
    background_tasks: BackgroundTasks,
    classifier: LongTailClassifier = Depends(get_classifier)
):
    """訓練分類器"""
    try:
        # 轉換訓練數據
        training_data = []
        for item in request.training_data:
            training_data.append(TrainingData(**item))
        
        # 後台訓練
        background_tasks.add_task(
            _train_classifier_background,
            classifier,
            training_data,
            request.validation_split,
            request.save_model
        )
        
        return {
            "status": "training_started",
            "message": f"開始訓練，數據量: {len(training_data)}",
            "estimated_time": "5-10分鐘"
        }
        
    except Exception as e:
        logger.error(f"訓練請求失敗: {e}")
        raise HTTPException(status_code=500, detail=f"訓練失敗: {str(e)}")

@router.get("/pipeline/status", response_model=PipelineStatus)
async def get_pipeline_status(pipeline: QueryDataPipeline = Depends(get_pipeline)):
    """獲取數據管道狀態"""
    try:
        # 這裡應該從實際的管道狀態獲取數據
        return PipelineStatus(
            is_running=pipeline.running,
            processed_queries=1000,  # 模擬數據
            longtail_queries=600,    # 模擬數據
            last_processed=datetime.utcnow() - timedelta(minutes=1),
            error_count=0
        )
        
    except Exception as e:
        logger.error(f"獲取管道狀態失敗: {e}")
        raise HTTPException(status_code=500, detail=f"獲取狀態失敗: {str(e)}")

@router.post("/pipeline/start")
async def start_pipeline(
    background_tasks: BackgroundTasks,
    pipeline: QueryDataPipeline = Depends(get_pipeline)
):
    """啟動數據管道"""
    try:
        if not pipeline.running:
            background_tasks.add_task(pipeline.start_processing)
            return {"status": "started", "message": "數據管道已啟動"}
        else:
            return {"status": "already_running", "message": "數據管道已在運行"}
            
    except Exception as e:
        logger.error(f"啟動管道失敗: {e}")
        raise HTTPException(status_code=500, detail=f"啟動失敗: {str(e)}")

@router.post("/pipeline/stop")
async def stop_pipeline(pipeline: QueryDataPipeline = Depends(get_pipeline)):
    """停止數據管道"""
    try:
        pipeline.stop_processing()
        return {"status": "stopped", "message": "數據管道已停止"}
        
    except Exception as e:
        logger.error(f"停止管道失敗: {e}")
        raise HTTPException(status_code=500, detail=f"停止失敗: {str(e)}")

@router.get("/classifier/info")
async def get_classifier_info(classifier: LongTailClassifier = Depends(get_classifier)):
    """獲取分類器信息"""
    try:
        feature_importance = classifier.get_feature_importance()
        
        return {
            "categories": classifier.categories,
            "model_components": {
                "bert_model": "bert-base-multilingual-cased",
                "random_forest": "available" if classifier.rf_classifier else "not_loaded",
                "gradient_boosting": "available" if classifier.gb_classifier else "not_loaded",
                "scaler": "available" if classifier.scaler else "not_loaded"
            },
            "feature_importance": feature_importance,
            "status": "ready"
        }
        
    except Exception as e:
        logger.error(f"獲取分類器信息失敗: {e}")
        raise HTTPException(status_code=500, detail=f"獲取信息失敗: {str(e)}")

def _generate_optimization_suggestions(
    query: str,
    features: Optional[Dict],
    classification: Optional[Dict]
) -> List[str]:
    """生成優化建議"""
    suggestions = []
    
    if not features or not classification:
        return suggestions
    
    category = classification.get('category', '')
    confidence = classification.get('confidence', 0)
    
    # 基於分類結果的建議
    if 'longtail' in category:
        suggestions.append("這是長尾查詢，競爭較低，適合精準定位")
        suggestions.append("建議創建專門針對此查詢的內容頁面")
        
        if confidence > 0.8:
            suggestions.append("分類置信度很高，可以重點優化")
    
    # 基於特徵的建議
    if features.get('has_question_words'):
        suggestions.append("問題型查詢，建議使用FAQ格式回答")
    
    if features.get('has_location'):
        suggestions.append("包含地理位置，建議優化本地SEO")
    
    if features.get('complexity_score', 0) > 0.7:
        suggestions.append("查詢複雜度較高，建議提供詳細的分步指南")
    
    # 基於意圖的建議
    intent_scores = features.get('intent_scores', {})
    max_intent = max(intent_scores.items(), key=lambda x: x[1]) if intent_scores else None
    
    if max_intent:
        intent, score = max_intent
        if score > 0.5:
            if intent == 'commercial':
                suggestions.append("具有商業意圖，建議優化產品頁面和購買流程")
            elif intent == 'transactional':
                suggestions.append("具有交易意圖，建議優化轉換漏斗")
            elif intent == 'local':
                suggestions.append("本地搜索意圖，建議優化Google My Business")
    
    return suggestions

async def _train_classifier_background(
    classifier: LongTailClassifier,
    training_data: List[TrainingData],
    validation_split: float,
    save_model: bool
):
    """後台訓練分類器"""
    try:
        logger.info("開始後台訓練分類器")
        classifier.train(training_data, validation_split)
        
        if save_model:
            classifier.save_model()
            
        logger.info("分類器訓練完成")
        
    except Exception as e:
        logger.error(f"後台訓練失敗: {e}")

@router.get("/health")
async def health_check():
    """健康檢查"""
    return {
        "status": "healthy",
        "service": "長尾查詢識別系統 - 高級API",
        "timestamp": datetime.utcnow(),
        "components": {
            "pipeline": "available",
            "classifier": "available"
        }
    }
