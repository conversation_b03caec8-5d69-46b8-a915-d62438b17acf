"""
增強長尾查詢分析API端點
集成向量搜索、概念擴展和少樣本學習
"""

import logging
from typing import List, Dict, Optional, Any
from fastapi import APIRouter, HTTPException, Depends, BackgroundTasks
from pydantic import BaseModel, Field
from datetime import datetime

# 項目依賴
try:
    from app.services.enhanced_longtail_analyzer import EnhancedLongTailAnalyzer
    ENHANCED_ANALYZER_AVAILABLE = True
except ImportError as e:
    logger.warning(f"增強分析器導入失敗，將使用基礎分析器: {e}")
    from app.api.v1.endpoints.longtail_simple import SimpleLongTailAnalyzer
    ENHANCED_ANALYZER_AVAILABLE = False

logger = logging.getLogger(__name__)

# 創建路由器
router = APIRouter(prefix="/longtail-enhanced", tags=["增強長尾查詢分析"])

# 全局分析器實例
if ENHANCED_ANALYZER_AVAILABLE:
    enhanced_analyzer = EnhancedLongTailAnalyzer()
else:
    enhanced_analyzer = SimpleLongTailAnalyzer()

# Pydantic 模型
class EnhancedQueryAnalysisRequest(BaseModel):
    """增強查詢分析請求"""
    query: str = Field(..., min_length=1, max_length=500, description="要分析的查詢")
    include_similar: bool = Field(True, description="是否包含相似查詢")
    include_suggestions: bool = Field(True, description="是否包含優化建議")
    similarity_threshold: Optional[float] = Field(0.7, ge=0.0, le=1.0, description="相似度閾值")

class SimilarQuery(BaseModel):
    """相似查詢"""
    id: str
    query: str
    score: float
    query_type: str
    intent: str
    semantic_category: str
    longtail_score: float

class RelatedConcept(BaseModel):
    """相關概念"""
    concept: str
    relevance_score: float

class EnhancedPrediction(BaseModel):
    """增強預測結果"""
    predicted_type: str
    confidence: float
    support_queries_count: int

class EnhancedAnalysisResponse(BaseModel):
    """增強分析響應"""
    # 基礎分析結果
    query: str
    query_type: str
    intent: str
    complexity: str
    features: Dict[str, Any]
    longtail_score: float
    confidence: float
    keywords: List[str]
    semantic_category: str
    search_volume_estimate: int
    competition_level: str
    optimization_suggestions: List[str]
    timestamp: datetime
    
    # 增強功能
    similar_queries: List[SimilarQuery]
    related_concepts: List[RelatedConcept]
    enhanced_predictions: Optional[EnhancedPrediction]
    analysis_method: str

class BatchEnhancedAnalysisRequest(BaseModel):
    """批量增強分析請求"""
    queries: List[str] = Field(..., min_items=1, max_items=100, description="查詢列表")
    include_similar: bool = Field(True, description="是否包含相似查詢")
    include_suggestions: bool = Field(True, description="是否包含優化建議")

class BatchEnhancedAnalysisResponse(BaseModel):
    """批量增強分析響應"""
    total_queries: int
    processed_queries: int
    results: List[EnhancedAnalysisResponse]
    processing_time: float
    analysis_summary: Dict[str, Any]

class IndexRebuildRequest(BaseModel):
    """索引重建請求"""
    force: bool = Field(False, description="是否強制重建")

class AnalyzerStatsResponse(BaseModel):
    """分析器統計響應"""
    initialized: bool
    matcher_available: bool
    few_shot_model_available: bool
    index_size: int
    similarity_threshold: float
    max_similar_queries: int

# 啟動事件
@router.on_event("startup")
async def startup_event():
    """啟動時初始化增強分析器"""
    try:
        if ENHANCED_ANALYZER_AVAILABLE and hasattr(enhanced_analyzer, 'initialize'):
            await enhanced_analyzer.initialize()
            logger.info("增強長尾查詢分析器啟動完成")
        else:
            logger.info("使用基礎分析器，無需初始化")
    except Exception as e:
        logger.error(f"增強分析器啟動失敗: {e}")

# API 端點
@router.post("/analyze", response_model=EnhancedAnalysisResponse)
async def analyze_query_enhanced(request: EnhancedQueryAnalysisRequest):
    """
    增強查詢分析
    
    使用向量搜索、概念擴展和少樣本學習進行高級分析
    """
    try:
        # 執行分析
        if ENHANCED_ANALYZER_AVAILABLE and hasattr(enhanced_analyzer, 'analyze_query_enhanced'):
            # 設置相似度閾值
            if request.similarity_threshold:
                enhanced_analyzer.similarity_threshold = request.similarity_threshold

            # 執行增強分析
            result = await enhanced_analyzer.analyze_query_enhanced(
                query=request.query,
                include_similar=request.include_similar,
                include_suggestions=request.include_suggestions
            )
        else:
            # 使用基礎分析器
            base_result = enhanced_analyzer.analyze_query(request.query)
            result = {
                **base_result.dict(),
                'similar_queries': [],
                'related_concepts': [],
                'enhanced_predictions': None,
                'analysis_method': 'basic_fallback'
            }
        
        # 轉換相似查詢格式
        similar_queries = [
            SimilarQuery(
                id=sq['id'],
                query=sq['query'],
                score=sq['score'],
                query_type=sq['query_type'],
                intent=sq['intent'],
                semantic_category=sq['semantic_category'],
                longtail_score=sq['longtail_score']
            )
            for sq in result.get('similar_queries', [])
        ]
        
        # 轉換相關概念格式
        related_concepts = [
            RelatedConcept(concept=concept, relevance_score=score)
            for concept, score in result.get('related_concepts', [])
        ]
        
        # 轉換增強預測格式
        enhanced_predictions = None
        if result.get('enhanced_predictions'):
            ep = result['enhanced_predictions']
            enhanced_predictions = EnhancedPrediction(
                predicted_type=ep['predicted_type'],
                confidence=ep['confidence'],
                support_queries_count=ep['support_queries_count']
            )
        
        # 構建響應
        response = EnhancedAnalysisResponse(
            query=result['query'],
            query_type=result['query_type'],
            intent=result['intent'],
            complexity=result['complexity'],
            features=result['features'],
            longtail_score=result['longtail_score'],
            confidence=result['confidence'],
            keywords=result['keywords'],
            semantic_category=result['semantic_category'],
            search_volume_estimate=result['search_volume_estimate'],
            competition_level=result['competition_level'],
            optimization_suggestions=result['optimization_suggestions'],
            timestamp=result['timestamp'],
            similar_queries=similar_queries,
            related_concepts=related_concepts,
            enhanced_predictions=enhanced_predictions,
            analysis_method=result['analysis_method']
        )
        
        return response
        
    except Exception as e:
        logger.error(f"增強查詢分析失敗: {request.query}, 錯誤: {e}")
        raise HTTPException(status_code=500, detail=f"增強查詢分析失敗: {str(e)}")

@router.post("/analyze/batch", response_model=BatchEnhancedAnalysisResponse)
async def analyze_batch_enhanced(request: BatchEnhancedAnalysisRequest):
    """
    批量增強查詢分析
    """
    try:
        start_time = datetime.now()
        
        # 執行批量分析
        results = await enhanced_analyzer.batch_analyze_enhanced(request.queries)
        
        end_time = datetime.now()
        processing_time = (end_time - start_time).total_seconds()
        
        # 生成分析摘要
        analysis_summary = {
            'total_queries': len(request.queries),
            'successful_analyses': len(results),
            'failed_analyses': len(request.queries) - len(results),
            'avg_longtail_score': sum(r['longtail_score'] for r in results) / len(results) if results else 0,
            'avg_confidence': sum(r['confidence'] for r in results) / len(results) if results else 0,
            'query_type_distribution': {},
            'intent_distribution': {}
        }
        
        # 統計分布
        for result in results:
            qt = result['query_type']
            intent = result['intent']
            analysis_summary['query_type_distribution'][qt] = analysis_summary['query_type_distribution'].get(qt, 0) + 1
            analysis_summary['intent_distribution'][intent] = analysis_summary['intent_distribution'].get(intent, 0) + 1
        
        # 轉換結果格式
        formatted_results = []
        for result in results:
            similar_queries = [
                SimilarQuery(
                    id=sq['id'],
                    query=sq['query'],
                    score=sq['score'],
                    query_type=sq['query_type'],
                    intent=sq['intent'],
                    semantic_category=sq['semantic_category'],
                    longtail_score=sq['longtail_score']
                )
                for sq in result.get('similar_queries', [])
            ]
            
            related_concepts = [
                RelatedConcept(concept=concept, relevance_score=score)
                for concept, score in result.get('related_concepts', [])
            ]
            
            enhanced_predictions = None
            if result.get('enhanced_predictions'):
                ep = result['enhanced_predictions']
                enhanced_predictions = EnhancedPrediction(
                    predicted_type=ep['predicted_type'],
                    confidence=ep['confidence'],
                    support_queries_count=ep['support_queries_count']
                )
            
            formatted_result = EnhancedAnalysisResponse(
                query=result['query'],
                query_type=result['query_type'],
                intent=result['intent'],
                complexity=result['complexity'],
                features=result['features'],
                longtail_score=result['longtail_score'],
                confidence=result['confidence'],
                keywords=result['keywords'],
                semantic_category=result['semantic_category'],
                search_volume_estimate=result['search_volume_estimate'],
                competition_level=result['competition_level'],
                optimization_suggestions=result['optimization_suggestions'],
                timestamp=result['timestamp'],
                similar_queries=similar_queries,
                related_concepts=related_concepts,
                enhanced_predictions=enhanced_predictions,
                analysis_method=result['analysis_method']
            )
            formatted_results.append(formatted_result)
        
        return BatchEnhancedAnalysisResponse(
            total_queries=len(request.queries),
            processed_queries=len(results),
            results=formatted_results,
            processing_time=processing_time,
            analysis_summary=analysis_summary
        )
        
    except Exception as e:
        logger.error(f"批量增強分析失敗: {e}")
        raise HTTPException(status_code=500, detail=f"批量增強分析失敗: {str(e)}")

@router.post("/index/rebuild")
async def rebuild_index(request: IndexRebuildRequest, background_tasks: BackgroundTasks):
    """
    重建向量索引
    """
    try:
        if request.force:
            # 在背景任務中重建索引
            background_tasks.add_task(enhanced_analyzer.rebuild_index)
            return {"message": "索引重建已開始，將在背景執行"}
        else:
            # 同步重建
            await enhanced_analyzer.rebuild_index()
            return {"message": "索引重建完成"}
            
    except Exception as e:
        logger.error(f"索引重建失敗: {e}")
        raise HTTPException(status_code=500, detail=f"索引重建失敗: {str(e)}")

@router.get("/stats", response_model=AnalyzerStatsResponse)
async def get_analyzer_stats():
    """
    獲取分析器統計信息
    """
    try:
        stats = enhanced_analyzer.get_stats()
        return AnalyzerStatsResponse(**stats)
        
    except Exception as e:
        logger.error(f"獲取統計信息失敗: {e}")
        raise HTTPException(status_code=500, detail=f"獲取統計信息失敗: {str(e)}")

@router.get("/health")
async def health_check():
    """
    健康檢查
    """
    try:
        stats = enhanced_analyzer.get_stats()
        return {
            "status": "healthy" if stats['initialized'] else "initializing",
            "timestamp": datetime.now(),
            "components": {
                "matcher": "available" if stats['matcher_available'] else "unavailable",
                "few_shot_model": "available" if stats['few_shot_model_available'] else "unavailable",
                "index_size": stats['index_size']
            }
        }
        
    except Exception as e:
        logger.error(f"健康檢查失敗: {e}")
        return {
            "status": "unhealthy",
            "timestamp": datetime.now(),
            "error": str(e)
        }
