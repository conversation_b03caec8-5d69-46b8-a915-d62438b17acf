"""
長尾查詢識別系統 - 簡化版API路由
不依賴ML模型的基礎版本，用於快速測試
"""

import asyncio
import logging
import re
from typing import List, Dict, Any, Optional
from datetime import datetime
from fastapi import APIRouter, HTTPException
from pydantic import BaseModel, Field

# 配置日誌
logger = logging.getLogger(__name__)

# 創建路由器
router = APIRouter()

# 簡化的數據模型
class QueryFeaturesSimple(BaseModel):
    """簡化的查詢特徵"""
    length: int
    word_count: int
    character_count: int
    has_question_words: bool
    has_brand_names: bool
    has_location: bool
    has_numbers: bool
    has_special_chars: bool
    language: str
    specificity_score: float
    commercial_intent_score: float

class LongTailAnalysisResultSimple(BaseModel):
    """簡化的長尾查詢分析結果"""
    query: str
    query_type: str  # head, middle, long_tail
    intent: str  # informational, navigational, transactional, commercial, local
    complexity: str  # simple, moderate, complex
    features: QueryFeaturesSimple
    longtail_score: float = Field(..., ge=0.0, le=1.0)
    confidence: float = Field(..., ge=0.0, le=1.0)
    keywords: List[str]
    semantic_category: str
    search_volume_estimate: int = Field(..., ge=0)
    competition_level: str
    optimization_suggestions: List[str]
    timestamp: datetime

class QueryAnalysisRequestSimple(BaseModel):
    """簡化的查詢分析請求"""
    query: str = Field(..., min_length=1, max_length=500)
    include_keywords: bool = True
    include_suggestions: bool = True

class BatchAnalysisRequestSimple(BaseModel):
    """簡化的批量分析請求"""
    queries: List[str] = Field(..., min_items=1, max_items=100)
    session_name: Optional[str] = None
    description: Optional[str] = None

class BatchAnalysisResponseSimple(BaseModel):
    """簡化的批量分析響應"""
    session_id: str
    total_queries: int
    processed_queries: int
    longtail_queries: int
    longtail_percentage: float
    results: List[LongTailAnalysisResultSimple]
    summary: Dict[str, Any]
    recommendations: List[str]

class SimpleLongTailAnalyzer:
    """簡化的長尾查詢分析器"""
    
    def __init__(self):
        self.question_words_zh = {
            '什麼', '如何', '怎麼', '為什麼', '哪裡', '何時', '誰', '哪個', '多少'
        }
        self.question_words_en = {
            'what', 'how', 'why', 'where', 'when', 'who', 'which', 'how much', 'how many'
        }
        
        self.commercial_keywords_zh = {
            '買', '購買', '價格', '便宜', '優惠', '折扣', '比較', '評價', '推薦'
        }
        self.commercial_keywords_en = {
            'buy', 'purchase', 'price', 'cheap', 'discount', 'compare', 'review', 'best'
        }
        
        self.location_keywords_zh = {
            '附近', '台北', '台中', '高雄', '台南', '桃園', '新竹', '台灣'
        }
        self.location_keywords_en = {
            'near', 'nearby', 'location', 'address', 'taipei', 'taiwan'
        }

    def analyze_query(self, query: str) -> LongTailAnalysisResultSimple:
        """分析單個查詢"""
        try:
            # 提取基礎特徵
            features = self._extract_features(query)
            
            # 分類查詢類型
            query_type = self._classify_query_type(query, features)
            
            # 識別查詢意圖
            intent = self._detect_intent(query, features)
            
            # 評估複雜度
            complexity = self._assess_complexity(features)
            
            # 計算長尾分數
            longtail_score = self._calculate_longtail_score(query, features)
            
            # 提取關鍵詞
            keywords = self._extract_keywords(query)
            
            # 語義分類
            semantic_category = self._categorize_semantically(query, keywords)
            
            # 估計搜索量和競爭程度
            search_volume_estimate = self._estimate_search_volume(query, features)
            competition_level = self._assess_competition(query, features)
            
            # 生成優化建議
            optimization_suggestions = self._generate_suggestions(
                query, query_type, intent, features
            )
            
            # 計算置信度
            confidence = self._calculate_confidence(features, longtail_score)
            
            return LongTailAnalysisResultSimple(
                query=query,
                query_type=query_type,
                intent=intent,
                complexity=complexity,
                features=features,
                longtail_score=longtail_score,
                confidence=confidence,
                keywords=keywords,
                semantic_category=semantic_category,
                search_volume_estimate=search_volume_estimate,
                competition_level=competition_level,
                optimization_suggestions=optimization_suggestions,
                timestamp=datetime.utcnow()
            )
            
        except Exception as e:
            logger.error(f"查詢分析失敗: {query}, 錯誤: {e}")
            raise

    def _extract_features(self, query: str) -> QueryFeaturesSimple:
        """提取查詢特徵"""
        # 基礎統計
        length = len(query)
        words = query.split()
        word_count = len(words)
        character_count = len(query.replace(' ', ''))
        
        # 語言檢測
        language = self._detect_language(query)
        
        # 特徵檢測
        has_question_words = self._has_question_words(query, language)
        has_brand_names = self._has_brand_names(query)
        has_location = self._has_location(query, language)
        has_numbers = bool(re.search(r'\d', query))
        has_special_chars = bool(re.search(r'[!@#$%^&*(),.?":{}|<>]', query))
        
        # 計算特異性分數
        specificity_score = self._calculate_specificity(query, words)
        
        # 計算商業意圖分數
        commercial_intent_score = self._calculate_commercial_intent(query, language)
        
        return QueryFeaturesSimple(
            length=length,
            word_count=word_count,
            character_count=character_count,
            has_question_words=has_question_words,
            has_brand_names=has_brand_names,
            has_location=has_location,
            has_numbers=has_numbers,
            has_special_chars=has_special_chars,
            language=language,
            specificity_score=specificity_score,
            commercial_intent_score=commercial_intent_score
        )

    def _detect_language(self, query: str) -> str:
        """檢測查詢語言"""
        chinese_chars = len(re.findall(r'[\u4e00-\u9fff]', query))
        total_chars = len(query.replace(' ', ''))
        
        if total_chars == 0:
            return 'unknown'
        
        chinese_ratio = chinese_chars / total_chars
        return 'zh' if chinese_ratio > 0.5 else 'en'

    def _has_question_words(self, query: str, language: str) -> bool:
        """檢查是否包含疑問詞"""
        query_lower = query.lower()
        
        if language == 'zh':
            return any(word in query for word in self.question_words_zh)
        else:
            return any(word in query_lower for word in self.question_words_en)

    def _has_brand_names(self, query: str) -> bool:
        """檢查是否包含品牌名"""
        common_brands = {
            'apple', 'google', 'microsoft', 'amazon', 'facebook',
            '蘋果', '谷歌', '微軟', '亞馬遜', '臉書'
        }
        query_lower = query.lower()
        return any(brand in query_lower for brand in common_brands)

    def _has_location(self, query: str, language: str) -> bool:
        """檢查是否包含地理位置"""
        query_lower = query.lower()
        
        if language == 'zh':
            return any(loc in query for loc in self.location_keywords_zh)
        else:
            return any(loc in query_lower for loc in self.location_keywords_en)

    def _calculate_specificity(self, query: str, words: List[str]) -> float:
        """計算特異性分數"""
        unique_words = len(set(words))
        total_words = len(words)
        
        if total_words == 0:
            return 0.0
        
        diversity = unique_words / total_words
        length_factor = min(total_words / 8.0, 1.0)
        
        return (diversity + length_factor) / 2.0

    def _calculate_commercial_intent(self, query: str, language: str) -> float:
        """計算商業意圖分數"""
        query_lower = query.lower()
        score = 0.0
        
        if language == 'zh':
            commercial_words = self.commercial_keywords_zh
        else:
            commercial_words = self.commercial_keywords_en
        
        for word in commercial_words:
            if word in query_lower:
                score += 0.2
        
        return min(score, 1.0)

    def _classify_query_type(self, query: str, features: QueryFeaturesSimple) -> str:
        """分類查詢類型"""
        if features.word_count <= 2 and features.specificity_score < 0.3:
            return "head"
        elif features.word_count <= 4 and features.specificity_score < 0.6:
            return "middle"
        else:
            return "long_tail"

    def _detect_intent(self, query: str, features: QueryFeaturesSimple) -> str:
        """檢測查詢意圖"""
        query_lower = query.lower()
        
        if features.has_question_words:
            return "informational"
        
        if features.commercial_intent_score > 0.5:
            return "commercial"
        
        transaction_patterns = ['買', '購買', '訂購', 'buy', 'purchase', 'order']
        if any(pattern in query_lower for pattern in transaction_patterns):
            return "transactional"
        
        if features.has_location:
            return "local"
        
        navigation_patterns = ['登入', '註冊', '首頁', 'login', 'register', 'home']
        if any(pattern in query_lower for pattern in navigation_patterns):
            return "navigational"
        
        return "informational"

    def _assess_complexity(self, features: QueryFeaturesSimple) -> str:
        """評估查詢複雜度"""
        if features.word_count <= 2:
            return "simple"
        elif features.word_count <= 5:
            return "moderate"
        else:
            return "complex"

    def _calculate_longtail_score(self, query: str, features: QueryFeaturesSimple) -> float:
        """計算長尾分數"""
        score = 0.0
        
        # 長度因子
        length_factor = min(features.word_count / 10.0, 1.0)
        score += length_factor * 0.3
        
        # 特異性因子
        score += features.specificity_score * 0.4
        
        # 複雜性因子
        if features.has_question_words:
            score += 0.1
        if features.has_numbers:
            score += 0.1
        if features.has_location:
            score += 0.1
        
        return min(score, 1.0)

    def _extract_keywords(self, query: str) -> List[str]:
        """提取關鍵詞"""
        words = re.findall(r'\b\w+\b', query.lower())
        
        stop_words = {
            '的', '是', '在', '有', '和', '或', '但', '與', '或者',
            'the', 'is', 'in', 'and', 'or', 'but', 'with', 'for', 'a', 'an'
        }
        
        keywords = [word for word in words if word not in stop_words and len(word) > 1]
        return keywords[:10]

    def _categorize_semantically(self, query: str, keywords: List[str]) -> str:
        """語義分類"""
        categories = {
            'technology': ['電腦', '手機', '軟體', 'computer', 'phone', 'software', 'AI', 'ai'],
            'shopping': ['買', '購買', '商品', 'buy', 'shop', 'product'],
            'health': ['健康', '醫療', '藥物', 'health', 'medical', 'medicine'],
            'education': ['學習', '教育', '課程', 'learn', 'education', 'course'],
            'travel': ['旅遊', '酒店', '機票', 'travel', 'hotel', 'flight']
        }
        
        query_lower = query.lower()
        for category, terms in categories.items():
            if any(term in query_lower for term in terms):
                return category
        
        return 'general'

    def _estimate_search_volume(self, query: str, features: QueryFeaturesSimple) -> int:
        """估計搜索量"""
        base_volume = 1000
        
        if features.word_count > 4:
            base_volume //= 2
        if features.word_count > 6:
            base_volume //= 2
        
        volume_reduction = int(base_volume * features.specificity_score)
        estimated_volume = max(base_volume - volume_reduction, 10)
        
        return estimated_volume

    def _assess_competition(self, query: str, features: QueryFeaturesSimple) -> str:
        """評估競爭程度"""
        if features.commercial_intent_score > 0.7:
            return 'high'
        elif features.commercial_intent_score > 0.3:
            return 'medium'
        else:
            return 'low'

    def _generate_suggestions(self, query: str, query_type: str, intent: str, features: QueryFeaturesSimple) -> List[str]:
        """生成優化建議"""
        suggestions = []
        
        if query_type == "long_tail":
            suggestions.append("這是一個長尾查詢，競爭較低，適合精準定位")
            suggestions.append("建議創建專門針對此查詢的內容頁面")
        
        if intent == "commercial":
            suggestions.append("具有商業意圖，建議優化產品頁面和購買流程")
        
        if features.has_location:
            suggestions.append("包含地理位置，建議優化本地SEO")
        
        if features.has_question_words:
            suggestions.append("問題型查詢，建議使用FAQ格式回答")
        
        return suggestions

    def _calculate_confidence(self, features: QueryFeaturesSimple, longtail_score: float) -> float:
        """計算置信度"""
        return 0.85  # 簡化版本固定置信度

# 創建分析器實例
analyzer = SimpleLongTailAnalyzer()

@router.post("/analyze", response_model=LongTailAnalysisResultSimple)
async def analyze_single_query(request: QueryAnalysisRequestSimple):
    """分析單個查詢"""
    try:
        result = analyzer.analyze_query(request.query)
        return result
    except Exception as e:
        logger.error(f"查詢分析失敗: {e}")
        raise HTTPException(status_code=500, detail=f"查詢分析失敗: {str(e)}")

@router.post("/analyze/batch", response_model=BatchAnalysisResponseSimple)
async def analyze_batch_queries(request: BatchAnalysisRequestSimple):
    """批量分析查詢"""
    try:
        results = []
        for query in request.queries:
            result = analyzer.analyze_query(query)
            results.append(result)
        
        # 生成統計
        total_queries = len(results)
        longtail_queries = len([r for r in results if r.query_type == "long_tail"])
        longtail_percentage = (longtail_queries / total_queries * 100) if total_queries > 0 else 0
        
        # 生成建議
        recommendations = []
        if longtail_percentage > 60:
            recommendations.append("長尾查詢比例較高，建議重點優化長尾關鍵詞策略")
        
        commercial_queries = [r for r in results if r.features.commercial_intent_score > 0.5]
        if len(commercial_queries) > total_queries * 0.3:
            recommendations.append("商業意圖查詢較多，建議優化產品頁面和轉換流程")
        
        return BatchAnalysisResponseSimple(
            session_id=f"session_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}",
            total_queries=total_queries,
            processed_queries=len(results),
            longtail_queries=longtail_queries,
            longtail_percentage=round(longtail_percentage, 2),
            results=results,
            summary={
                "total_queries": total_queries,
                "longtail_queries": longtail_queries,
                "longtail_percentage": round(longtail_percentage, 2),
                "avg_longtail_score": round(sum(r.longtail_score for r in results) / len(results), 3) if results else 0,
                "avg_confidence": round(sum(r.confidence for r in results) / len(results), 3) if results else 0
            },
            recommendations=recommendations
        )
        
    except Exception as e:
        logger.error(f"批量分析失敗: {e}")
        raise HTTPException(status_code=500, detail=f"批量分析失敗: {str(e)}")

@router.get("/search")
async def search_queries(
    query_text: Optional[str] = None,
    query_type: Optional[str] = None,
    intent: Optional[str] = None,
    semantic_category: Optional[str] = None,
    min_longtail_score: Optional[float] = None,
    max_longtail_score: Optional[float] = None,
    limit: int = 50
):
    """搜索查詢（模擬數據）"""
    # 生成一些模擬的查詢數據
    sample_queries = [
        {
            "query": "AI工具推薦",
            "query_type": "middle",
            "intent": "informational",
            "longtail_score": 0.45,
            "confidence": 0.82,
            "semantic_category": "technology",
            "search_volume_estimate": 2500,
            "competition_level": "medium",
            "timestamp": "2024-01-01T10:00:00Z"
        },
        {
            "query": "如何選擇最適合小型企業的AI客服系統並進行有效整合",
            "query_type": "long_tail",
            "intent": "informational",
            "longtail_score": 0.89,
            "confidence": 0.94,
            "semantic_category": "technology",
            "search_volume_estimate": 120,
            "competition_level": "low",
            "timestamp": "2024-01-01T11:00:00Z"
        },
        {
            "query": "購買最便宜的AI寫作軟體",
            "query_type": "long_tail",
            "intent": "commercial",
            "longtail_score": 0.76,
            "confidence": 0.88,
            "semantic_category": "technology",
            "search_volume_estimate": 350,
            "competition_level": "high",
            "timestamp": "2024-01-01T12:00:00Z"
        },
        {
            "query": "台北附近的AI培訓課程",
            "query_type": "long_tail",
            "intent": "local",
            "longtail_score": 0.72,
            "confidence": 0.85,
            "semantic_category": "education",
            "search_volume_estimate": 180,
            "competition_level": "medium",
            "timestamp": "2024-01-01T13:00:00Z"
        },
        {
            "query": "AI",
            "query_type": "head",
            "intent": "informational",
            "longtail_score": 0.15,
            "confidence": 0.75,
            "semantic_category": "technology",
            "search_volume_estimate": 50000,
            "competition_level": "high",
            "timestamp": "2024-01-01T14:00:00Z"
        }
    ]

    # 簡單的過濾邏輯
    filtered_queries = sample_queries

    if query_text:
        filtered_queries = [q for q in filtered_queries if query_text.lower() in q["query"].lower()]

    if query_type:
        filtered_queries = [q for q in filtered_queries if q["query_type"] == query_type]

    if intent:
        filtered_queries = [q for q in filtered_queries if q["intent"] == intent]

    if semantic_category:
        filtered_queries = [q for q in filtered_queries if q["semantic_category"] == semantic_category]

    if min_longtail_score is not None:
        filtered_queries = [q for q in filtered_queries if q["longtail_score"] >= min_longtail_score]

    if max_longtail_score is not None:
        filtered_queries = [q for q in filtered_queries if q["longtail_score"] <= max_longtail_score]

    # 限制結果數量
    filtered_queries = filtered_queries[:limit]

    return {
        "total": len(filtered_queries),
        "queries": filtered_queries,
        "aggregations": {
            "query_types": {"head": 1, "middle": 1, "long_tail": 3},
            "intents": {"informational": 2, "commercial": 1, "local": 1},
            "categories": {"technology": 4, "education": 1}
        }
    }

@router.get("/stats")
async def get_query_statistics():
    """獲取查詢統計信息（模擬數據）"""
    return {
        "total_queries": 1250,
        "longtail_queries": 750,
        "longtail_percentage": 60.0,
        "intent_distribution": {
            "informational": 500,
            "commercial": 300,
            "transactional": 200,
            "local": 150,
            "navigational": 100
        },
        "complexity_distribution": {
            "simple": 400,
            "moderate": 500,
            "complex": 350
        },
        "category_distribution": {
            "technology": 400,
            "shopping": 300,
            "health": 200,
            "education": 150,
            "travel": 100,
            "general": 100
        },
        "language_distribution": {
            "zh": 800,
            "en": 450
        },
        "avg_longtail_score": 0.65,
        "avg_confidence": 0.85
    }
