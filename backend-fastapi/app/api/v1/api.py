"""
API v1 主路由
"""

from fastapi import APIRouter

from app.api.v1.endpoints import seo, links, reports, auth, health, monitoring, vector_search, vector_pipeline, search, data_pipeline, pipeline_dashboard, longtail_simple, longtail_advanced
# from app.api.v1.endpoints import analytics  # Temporarily disabled due to schema issues
from app.api.v1.endpoints.admin import openai_settings_router, measure_router, optimization
from app.api.v1 import upload, background_tasks

api_router = APIRouter()

# 包含各個端點路由
api_router.include_router(
    health.router,
    prefix="/health",
    tags=["健康檢查"]
)

api_router.include_router(
    monitoring.router,
    prefix="/monitoring",
    tags=["系統監控"]
)

api_router.include_router(
    auth.router,
    prefix="/auth",
    tags=["認證"]
)

api_router.include_router(
    seo.router,
    prefix="/seo",
    tags=["SEO 分析"]
)

api_router.include_router(
    links.router,
    prefix="/links",
    tags=["連結驗證"]
)

api_router.include_router(
    reports.router,
    prefix="/reports",
    tags=["報告生成"]
)

api_router.include_router(
    vector_search.router,
    prefix="/vector-search",
    tags=["向量搜索"]
)

api_router.include_router(
    vector_pipeline.router,
    prefix="/vector-pipeline",
    tags=["向量管道"]
)

api_router.include_router(
    search.router,
    prefix="/search",
    tags=["Elasticsearch 搜索"]
)

api_router.include_router(
    data_pipeline.router,
    prefix="/pipeline",
    tags=["數據收集管道"]
)

api_router.include_router(
    pipeline_dashboard.router,
    prefix="/pipeline/dashboard",
    tags=["管道監控儀表板"]
)

api_router.include_router(
    upload.router,
    prefix="/upload",
    tags=["文件上傳"]
)

api_router.include_router(
    background_tasks.router,
    prefix="/background-tasks",
    tags=["背景任務"]
)

# api_router.include_router(
#     users.router,
#     prefix="/users",
#     tags=["用戶管理"]
# )

# api_router.include_router(
#     test.router,
#     prefix="/test",
#     tags=["系統測試"]
# )

# 管理員路由
api_router.include_router(
    openai_settings_router,
    prefix="/admin/openai",
    tags=["管理員 - OpenAI 設置"]
)

api_router.include_router(
    optimization.router,
    prefix="/admin/optimize",
    tags=["管理員 - 優化功能"]
)

api_router.include_router(
    measure_router,
    prefix="/admin/measure",
    tags=["管理員 - 測量功能"]
)

# 長尾查詢識別系統路由
api_router.include_router(
    longtail_simple.router,
    prefix="/longtail",
    tags=["長尾查詢識別"]
)

# 長尾查詢識別系統 - 高級功能
api_router.include_router(
    longtail_advanced.router,
    prefix="/longtail/advanced",
    tags=["長尾查詢識別 - 高級功能"]
)

# 添加新的分析路由
# api_router.include_router(analytics.router, prefix="/analytics", tags=["analytics"])  # Temporarily disabled due to schema issues
