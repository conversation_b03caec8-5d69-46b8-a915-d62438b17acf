"""
長尾查詢識別系統 - API路由
提供查詢分析、批量處理、實時識別等API端點
"""

import asyncio
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime
from fastapi import APIRouter, HTTPException, Depends, BackgroundTasks, Query
from fastapi.responses import JSONResponse
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_, or_

from ..models.longtail_models import (
    LongTailQuery, QueryAnalysisSession, QueryKeywordMapping,
    QueryAnalysisRequest, BatchAnalysisRequest, BatchAnalysisResponse,
    QuerySearchRequest, QuerySearchResponse, QueryStatsResponse,
    SimilarQueriesRequest, SimilarQueriesResponse,
    LongTailAnalysisResultModel
)
from ..services.longtail_query_analyzer import (
    LongTailQueryAnalyzer, BatchLongTailAnalyzer
)
from ..services.ml_models import LongTailMLService
from ..config.redis_longtail_config import LongTailCacheManager
from ..core.database import get_db
from ..core.redis_client import get_redis

# 配置日誌
logger = logging.getLogger(__name__)

# 創建路由器
router = APIRouter(prefix="/api/longtail", tags=["長尾查詢識別"])

# 全局服務實例
longtail_analyzer = LongTailQueryAnalyzer()
ml_service = LongTailMLService()
cache_manager = None

@router.on_event("startup")
async def startup_event():
    """啟動事件"""
    global cache_manager
    try:
        # 初始化ML服務
        await ml_service.initialize()
        logger.info("長尾查詢ML服務初始化完成")
    except Exception as e:
        logger.error(f"ML服務初始化失敗: {e}")

@router.post("/analyze", response_model=LongTailAnalysisResultModel)
async def analyze_single_query(
    request: QueryAnalysisRequest,
    db: AsyncSession = Depends(get_db),
    redis = Depends(get_redis)
):
    """
    分析單個查詢
    """
    try:
        # 初始化緩存管理器
        global cache_manager
        if cache_manager is None:
            cache_manager = LongTailCacheManager(redis)
        
        # 檢查緩存
        cached_result = await cache_manager.get_cached_query_analysis(request.query)
        if cached_result:
            logger.info(f"從緩存返回查詢分析結果: {request.query}")
            return LongTailAnalysisResultModel(**cached_result)
        
        # 執行分析
        analysis_result = await longtail_analyzer.analyze_query(request.query)
        
        # 如果需要，獲取ML增強特徵
        if ml_service.initialized:
            try:
                # 獲取查詢嵌入
                embeddings = await ml_service.get_query_embeddings(request.query)
                
                # 分類意圖
                intent, intent_confidence = await ml_service.classify_query_intent(request.query)
                
                # 更新分析結果
                if intent_confidence > analysis_result.confidence:
                    analysis_result.intent = intent
                    analysis_result.confidence = max(analysis_result.confidence, intent_confidence)
                
            except Exception as e:
                logger.warning(f"ML增強失敗: {e}")
        
        # 保存到數據庫
        await _save_analysis_to_db(db, analysis_result)
        
        # 緩存結果
        result_dict = analysis_result.__dict__.copy()
        result_dict['timestamp'] = result_dict['timestamp'].isoformat()
        await cache_manager.cache_query_analysis(request.query, result_dict)
        
        return analysis_result
        
    except Exception as e:
        logger.error(f"查詢分析失敗: {e}")
        raise HTTPException(status_code=500, detail=f"查詢分析失敗: {str(e)}")

@router.post("/analyze/batch", response_model=BatchAnalysisResponse)
async def analyze_batch_queries(
    request: BatchAnalysisRequest,
    background_tasks: BackgroundTasks,
    db: AsyncSession = Depends(get_db),
    redis = Depends(get_redis)
):
    """
    批量分析查詢
    """
    try:
        # 創建分析會話
        session = QueryAnalysisSession(
            session_name=request.session_name or f"批量分析_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}",
            description=request.description,
            total_queries=len(request.queries),
            status="processing"
        )
        
        db.add(session)
        await db.commit()
        await db.refresh(session)
        
        # 創建批量分析器
        batch_analyzer = BatchLongTailAnalyzer(longtail_analyzer)
        
        # 執行批量分析
        async def progress_callback(progress: float, completed: int):
            logger.info(f"批量分析進度: {progress:.2%}, 已完成: {completed}")
        
        results = await batch_analyzer.analyze_batch(
            request.queries,
            progress_callback=progress_callback
        )
        
        # 生成報告
        batch_report = batch_analyzer.generate_batch_report(results)
        
        # 更新會話狀態
        session.longtail_queries = batch_report["summary"]["longtail_queries"]
        session.longtail_percentage = batch_report["summary"]["longtail_percentage"]
        session.analysis_summary = batch_report
        session.status = "completed"
        session.completed_at = datetime.utcnow()
        
        await db.commit()
        
        # 後台任務：保存詳細結果到數據庫
        background_tasks.add_task(_save_batch_results_to_db, db, results)
        
        # 緩存結果
        if cache_manager is None:
            cache_manager = LongTailCacheManager(redis)
        
        await cache_manager.cache_batch_analysis(str(session.id), batch_report)
        
        return BatchAnalysisResponse(
            session_id=str(session.id),
            total_queries=len(request.queries),
            processed_queries=len(results),
            longtail_queries=batch_report["summary"]["longtail_queries"],
            longtail_percentage=batch_report["summary"]["longtail_percentage"],
            results=[_convert_to_pydantic_model(r) for r in results],
            summary=batch_report["summary"],
            recommendations=batch_report["recommendations"]
        )
        
    except Exception as e:
        logger.error(f"批量分析失敗: {e}")
        # 更新會話狀態為失敗
        if 'session' in locals():
            session.status = "failed"
            await db.commit()
        
        raise HTTPException(status_code=500, detail=f"批量分析失敗: {str(e)}")

@router.get("/search", response_model=QuerySearchResponse)
async def search_queries(
    request: QuerySearchRequest = Depends(),
    db: AsyncSession = Depends(get_db)
):
    """
    搜索查詢
    """
    try:
        # 構建查詢條件
        query = select(LongTailQuery)
        conditions = []
        
        if request.query_text:
            conditions.append(LongTailQuery.query_text.ilike(f"%{request.query_text}%"))
        
        if request.query_type:
            conditions.append(LongTailQuery.query_type == request.query_type.value)
        
        if request.intent:
            conditions.append(LongTailQuery.intent == request.intent.value)
        
        if request.semantic_category:
            conditions.append(LongTailQuery.semantic_category == request.semantic_category)
        
        if request.min_longtail_score is not None:
            conditions.append(LongTailQuery.longtail_score >= request.min_longtail_score)
        
        if request.max_longtail_score is not None:
            conditions.append(LongTailQuery.longtail_score <= request.max_longtail_score)
        
        if request.language:
            conditions.append(LongTailQuery.language == request.language)
        
        if conditions:
            query = query.where(and_(*conditions))
        
        # 計算總數
        count_query = select(func.count()).select_from(LongTailQuery)
        if conditions:
            count_query = count_query.where(and_(*conditions))
        
        total_result = await db.execute(count_query)
        total = total_result.scalar()
        
        # 執行查詢
        query = query.offset(request.offset).limit(request.limit)
        query = query.order_by(LongTailQuery.longtail_score.desc())
        
        result = await db.execute(query)
        queries = result.scalars().all()
        
        # 生成聚合統計
        aggregations = await _generate_search_aggregations(db, conditions)
        
        return QuerySearchResponse(
            total=total,
            queries=[_convert_db_to_pydantic(q) for q in queries],
            aggregations=aggregations
        )
        
    except Exception as e:
        logger.error(f"查詢搜索失敗: {e}")
        raise HTTPException(status_code=500, detail=f"查詢搜索失敗: {str(e)}")

@router.get("/stats", response_model=QueryStatsResponse)
async def get_query_statistics(
    db: AsyncSession = Depends(get_db),
    redis = Depends(get_redis)
):
    """
    獲取查詢統計信息
    """
    try:
        # 檢查緩存
        if cache_manager is None:
            cache_manager = LongTailCacheManager(redis)
        
        cached_stats = await cache_manager.get_cached_query_stats("general")
        if cached_stats:
            return QueryStatsResponse(**cached_stats)
        
        # 基本統計
        total_queries_result = await db.execute(select(func.count()).select_from(LongTailQuery))
        total_queries = total_queries_result.scalar()
        
        longtail_queries_result = await db.execute(
            select(func.count()).select_from(LongTailQuery).where(
                LongTailQuery.query_type == "long_tail"
            )
        )
        longtail_queries = longtail_queries_result.scalar()
        
        longtail_percentage = (longtail_queries / total_queries * 100) if total_queries > 0 else 0
        
        # 分布統計
        intent_dist = await _get_distribution_stats(db, LongTailQuery.intent)
        complexity_dist = await _get_distribution_stats(db, LongTailQuery.complexity)
        category_dist = await _get_distribution_stats(db, LongTailQuery.semantic_category)
        language_dist = await _get_distribution_stats(db, LongTailQuery.language)
        
        # 平均分數
        avg_scores_result = await db.execute(
            select(
                func.avg(LongTailQuery.longtail_score),
                func.avg(LongTailQuery.confidence)
            )
        )
        avg_longtail_score, avg_confidence = avg_scores_result.first()
        
        stats = QueryStatsResponse(
            total_queries=total_queries,
            longtail_queries=longtail_queries,
            longtail_percentage=round(longtail_percentage, 2),
            intent_distribution=intent_dist,
            complexity_distribution=complexity_dist,
            category_distribution=category_dist,
            language_distribution=language_dist,
            avg_longtail_score=round(float(avg_longtail_score or 0), 3),
            avg_confidence=round(float(avg_confidence or 0), 3)
        )
        
        # 緩存統計結果
        await cache_manager.cache_query_stats("general", stats.dict())
        
        return stats
        
    except Exception as e:
        logger.error(f"獲取統計信息失敗: {e}")
        raise HTTPException(status_code=500, detail=f"獲取統計信息失敗: {str(e)}")

@router.post("/similar", response_model=SimilarQueriesResponse)
async def find_similar_queries(
    request: SimilarQueriesRequest,
    db: AsyncSession = Depends(get_db),
    redis = Depends(get_redis)
):
    """
    查找相似查詢
    """
    try:
        # 檢查緩存
        if cache_manager is None:
            cache_manager = LongTailCacheManager(redis)
        
        cached_similar = await cache_manager.get_cached_similar_queries(
            request.query, request.similarity_threshold
        )
        if cached_similar:
            return SimilarQueriesResponse(
                query=request.query,
                similar_queries=cached_similar,
                total_found=len(cached_similar)
            )
        
        # 獲取候選查詢
        candidate_queries_result = await db.execute(
            select(LongTailQuery.query_text).limit(1000)
        )
        candidate_queries = [row[0] for row in candidate_queries_result.fetchall()]
        
        # 使用ML服務查找相似查詢
        similar_queries = []
        if ml_service.initialized:
            similar_results = await ml_service.find_similar_queries(
                request.query,
                candidate_queries,
                request.similarity_threshold,
                request.limit
            )
            
            similar_queries = [
                {
                    "query": query,
                    "similarity_score": score,
                    "query_type": "unknown",  # 可以從數據庫查詢
                    "longtail_score": 0.0     # 可以從數據庫查詢
                }
                for query, score in similar_results
            ]
        
        # 緩存結果
        await cache_manager.cache_similar_queries(
            request.query, similar_queries, request.similarity_threshold
        )
        
        return SimilarQueriesResponse(
            query=request.query,
            similar_queries=similar_queries,
            total_found=len(similar_queries)
        )
        
    except Exception as e:
        logger.error(f"查找相似查詢失敗: {e}")
        raise HTTPException(status_code=500, detail=f"查找相似查詢失敗: {str(e)}")

# 輔助函數
async def _save_analysis_to_db(db: AsyncSession, analysis_result):
    """保存分析結果到數據庫"""
    try:
        # 檢查是否已存在
        query_hash = hash(analysis_result.query)
        existing = await db.execute(
            select(LongTailQuery).where(LongTailQuery.query_hash == str(query_hash))
        )
        
        if existing.scalar():
            return  # 已存在，跳過
        
        # 創建新記錄
        db_query = LongTailQuery(
            query_text=analysis_result.query,
            query_hash=str(query_hash),
            query_type=analysis_result.query_type.value,
            intent=analysis_result.intent.value,
            complexity=analysis_result.complexity.value,
            length=analysis_result.features.length,
            word_count=analysis_result.features.word_count,
            character_count=analysis_result.features.character_count,
            language=analysis_result.features.language,
            longtail_score=analysis_result.longtail_score,
            confidence=analysis_result.confidence,
            specificity_score=analysis_result.features.specificity_score,
            commercial_intent_score=analysis_result.features.commercial_intent_score,
            has_question_words=analysis_result.features.has_question_words,
            has_brand_names=analysis_result.features.has_brand_names,
            has_location=analysis_result.features.has_location,
            has_numbers=analysis_result.features.has_numbers,
            has_special_chars=analysis_result.features.has_special_chars,
            keywords=analysis_result.keywords,
            semantic_category=analysis_result.semantic_category,
            search_volume_estimate=analysis_result.search_volume_estimate,
            competition_level=analysis_result.competition_level,
            analyzed_by="longtail_analyzer_v1"
        )
        
        db.add(db_query)
        await db.commit()
        
    except Exception as e:
        logger.error(f"保存分析結果失敗: {e}")
        await db.rollback()

async def _save_batch_results_to_db(db: AsyncSession, results: List):
    """後台任務：保存批量結果到數據庫"""
    try:
        for result in results:
            await _save_analysis_to_db(db, result)
        logger.info(f"批量保存完成，共 {len(results)} 條記錄")
    except Exception as e:
        logger.error(f"批量保存失敗: {e}")

def _convert_to_pydantic_model(analysis_result) -> LongTailAnalysisResultModel:
    """轉換為Pydantic模型"""
    return LongTailAnalysisResultModel(
        query=analysis_result.query,
        query_type=analysis_result.query_type,
        intent=analysis_result.intent,
        complexity=analysis_result.complexity,
        features=analysis_result.features,
        longtail_score=analysis_result.longtail_score,
        confidence=analysis_result.confidence,
        keywords=analysis_result.keywords,
        semantic_category=analysis_result.semantic_category,
        search_volume_estimate=analysis_result.search_volume_estimate,
        competition_level=analysis_result.competition_level,
        optimization_suggestions=analysis_result.optimization_suggestions,
        timestamp=analysis_result.timestamp
    )

def _convert_db_to_pydantic(db_query: LongTailQuery) -> LongTailAnalysisResultModel:
    """轉換數據庫記錄為Pydantic模型"""
    from ..services.longtail_query_analyzer import QueryFeatures
    
    features = QueryFeatures(
        length=db_query.length,
        word_count=db_query.word_count,
        character_count=db_query.character_count,
        has_question_words=db_query.has_question_words,
        has_brand_names=db_query.has_brand_names,
        has_location=db_query.has_location,
        has_numbers=db_query.has_numbers,
        has_special_chars=db_query.has_special_chars,
        language=db_query.language,
        specificity_score=db_query.specificity_score,
        commercial_intent_score=db_query.commercial_intent_score
    )
    
    return LongTailAnalysisResultModel(
        query=db_query.query_text,
        query_type=db_query.query_type,
        intent=db_query.intent,
        complexity=db_query.complexity,
        features=features,
        longtail_score=db_query.longtail_score,
        confidence=db_query.confidence,
        keywords=db_query.keywords or [],
        semantic_category=db_query.semantic_category,
        search_volume_estimate=db_query.search_volume_estimate,
        competition_level=db_query.competition_level,
        optimization_suggestions=[],  # 可以從其他地方獲取
        timestamp=db_query.created_at
    )

async def _generate_search_aggregations(db: AsyncSession, conditions: List) -> Dict[str, Any]:
    """生成搜索聚合統計"""
    # 簡化版本，實際可以更複雜
    return {
        "query_types": {},
        "intents": {},
        "categories": {}
    }

async def _get_distribution_stats(db: AsyncSession, column) -> Dict[str, int]:
    """獲取分布統計"""
    try:
        result = await db.execute(
            select(column, func.count()).group_by(column)
        )
        return {str(row[0]): row[1] for row in result.fetchall()}
    except Exception:
        return {}
