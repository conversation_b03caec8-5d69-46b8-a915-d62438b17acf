from typing import List, Dict, Any, Optional
from fastapi import APIRouter, Depends, HTTPException
from pydantic import BaseModel

from app.services.processors.intent_classification_processor import IntentClassificationProcessor
from app.services.simple_model_trainer import BasicModelTrainer

router = APIRouter(
    prefix="/api/commercial-intent",
    tags=["commercial-intent"],
    responses={404: {"description": "Not found"}},
)

# 依賴注入
def get_intent_processor():
    return IntentClassificationProcessor()

def get_model_trainer():
    return BasicModelTrainer()

# 請求模型
class CommercialIntentRequest(BaseModel):
    queries: List[str]
    apply_commercial_boost: bool = True
    include_details: bool = True

# 回應模型
class CommercialIntentResponse(BaseModel):
    results: List[Dict[str, Any]]
    commercial_value_boost_applied: bool
    status: str

@router.post("/analyze", response_model=CommercialIntentResponse)
async def analyze_commercial_intent(
    request: CommercialIntentRequest,
    intent_processor: IntentClassificationProcessor = Depends(get_intent_processor),
    model_trainer: BasicModelTrainer = Depends(get_model_trainer)
):
    """
    分析查詢的商業意圖和商業價值，根據需要應用商業價值權重加強分類效果
    """
    try:
        # 批量預測意圖和商業價值
        results = []
        
        # 使用統計模型預測
        model_predictions = await model_trainer.predict(
            request.queries, 
            apply_commercial_boost=request.apply_commercial_boost
        )
        
        # 使用規則式引擎進行意圖分類
        for query in request.queries:
            # 意圖處理器分析
            processed_result = intent_processor.process(query)
            
            # 獲取模型預測結果
            model_result = next(
                (item for item in model_predictions.get("results", []) if item.get("text") == query), 
                {"predicted_label": "unknown", "confidence": 0.0, "commercial_value": 0.0}
            )
            
            # 合併結果
            combined_result = {
                "query": query,
                "rule_based_intent": processed_result.get("intent", "unknown"),
                "rule_confidence": processed_result.get("confidence", 0.0),
                "model_intent": model_result.get("predicted_label", "unknown"),
                "model_confidence": model_result.get("confidence", 0.0),
                "commercial_value": processed_result.get("commercial_value", 
                                     model_result.get("commercial_value", 0.0)),
                "is_commercial": processed_result.get("is_commercial_intent", False),
                "intent_category": processed_result.get("intent_category", "unknown"),
            }
            
            # 附加額外詳情
            if request.include_details:
                combined_result.update({
                    "seo_strategies": processed_result.get("seo_strategies", []),
                    "improvement_suggestions": processed_result.get("improvement_suggestions", 
                                               model_result.get("improvement_suggestions", [])),
                    "model_probabilities": model_result.get("probabilities", {}),
                })
            
            results.append(combined_result)
        
        return {
            "results": results,
            "commercial_value_boost_applied": request.apply_commercial_boost,
            "status": "success"
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"分析失敗: {str(e)}")

@router.post("/train-commercial-model")
async def train_commercial_model(
    commercial_value_weight: float = 1.5,
    force_retrain: bool = False,
    model_trainer: BasicModelTrainer = Depends(get_model_trainer)
):
    """
    訓練或重新訓練商業意圖模型，可指定商業價值權重
    """
    try:
        result = await model_trainer.train_model(
            force_retrain=force_retrain, 
            commercial_value_weight=commercial_value_weight
        )
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"模型訓練失敗: {str(e)}")

@router.get("/commercial-metrics")
async def get_commercial_metrics(
    intent_processor: IntentClassificationProcessor = Depends(get_intent_processor),
    model_trainer: BasicModelTrainer = Depends(get_model_trainer)
):
    """
    獲取商業意圖統計指標和模型特徵重要性分析
    """
    try:
        # 取得意圖統計
        intent_stats = intent_processor.get_intent_statistics()
        
        # 取得模型統計
        model_stats = await model_trainer.get_model_stats()
        
        # 取得特徵重要性
        feature_importance = model_trainer._calculate_feature_importance()
        
        # 篩選商業相關特徵
        commercial_features = {k: v for k, v in feature_importance.items() 
                              if 'commercial' in k or 'purchase' in k or 'comparison' in k}
        
        return {
            "intent_stats": intent_stats,
            "model_stats": model_stats,
            "top_commercial_features": commercial_features,
            "total_commercial_intents": intent_stats.get("commercial_intents", 0),
            "status": "success"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"獲取商業指標失敗: {str(e)}")
