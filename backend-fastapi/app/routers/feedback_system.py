from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks
from pydantic import BaseModel
from typing import List, Dict, Any, Optional
import json
from datetime import datetime
import asyncio
from pathlib import Path
import logging

# 設置日誌
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

router = APIRouter(
    prefix="/api/feedback",
    tags=["feedback"],
    responses={404: {"description": "Not found"}}
)

# 數據儲存路徑
FEEDBACK_DIR = Path("./data/feedback")
FEEDBACK_DIR.mkdir(exist_ok=True, parents=True)

# 反饋數據模型
class IntentFeedback(BaseModel):
    query: str
    original_intent: str
    corrected_intent: str
    original_commercial_value: float
    corrected_commercial_value: Optional[float] = None
    user_id: Optional[str] = None
    feedback_notes: Optional[str] = None
    
class BatchIntentFeedback(BaseModel):
    feedback_items: List[IntentFeedback]

class FeedbackSummaryResponse(BaseModel):
    total_feedback: int
    recent_corrections: List[Dict[str, Any]]
    top_corrected_intents: Dict[str, int]
    status: str

def get_feedback_storage_path() -> Path:
    """獲取反饋儲存檔案路徑"""
    return FEEDBACK_DIR / "intent_feedback.json"

def load_feedback_data() -> List[Dict[str, Any]]:
    """載入反饋數據"""
    file_path = get_feedback_storage_path()
    if file_path.exists():
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"載入反饋數據失敗: {e}")
            return []
    else:
        return []

def save_feedback_data(feedback_data: List[Dict[str, Any]]) -> bool:
    """保存反饋數據"""
    file_path = get_feedback_storage_path()
    try:
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(feedback_data, f, ensure_ascii=False, indent=2)
        return True
    except Exception as e:
        logger.error(f"保存反饋數據失敗: {e}")
        return False

async def retrain_model_with_feedback(model_trainer) -> Dict[str, Any]:
    """使用反饋數據重新訓練模型"""
    try:
        feedback_data = load_feedback_data()
        if not feedback_data:
            logger.info("沒有反饋數據可用於重訓模型")
            return {"status": "success", "message": "沒有反饋數據可用於重訓模型"}
        
        # 將反饋資料轉換為訓練資料格式
        # 這裡假設 model_trainer 有個 train_with_feedback 方法可以接收這些資料
        training_samples = []
        for item in feedback_data:
            if item.get("applied_to_model", False):
                continue
                
            training_samples.append({
                "text": item["query"],
                "intent": item["corrected_intent"],
                "commercial_value": item.get("corrected_commercial_value", 
                                          item.get("original_commercial_value", 0.5))
            })
        
        if not training_samples:
            logger.info("沒有新的反饋資料需要應用到模型")
            return {"status": "success", "message": "沒有新的反饋資料需要應用到模型"}
        
        # 使用反饋資料重訓模型
        result = await model_trainer.train_with_feedback(training_samples)
        
        # 更新反饋資料，標記為已應用到模型
        for item in feedback_data:
            if not item.get("applied_to_model", False):
                item["applied_to_model"] = True
                item["applied_timestamp"] = datetime.now().isoformat()
        
        save_feedback_data(feedback_data)
        
        return {
            "status": "success",
            "message": f"已使用 {len(training_samples)} 條反饋資料重訓模型",
            "model_result": result
        }
    except Exception as e:
        logger.error(f"使用反饋資料重訓模型失敗: {e}")
        return {"status": "error", "message": str(e)}

@router.post("/intent-correction", response_model=Dict[str, Any])
async def submit_intent_correction(
    feedback: IntentFeedback,
    background_tasks: BackgroundTasks
) -> Dict[str, Any]:
    """
    提交意圖分類校正反饋
    """
    try:
        # 載入現有反饋數據
        feedback_data = load_feedback_data()
        
        # 建立反饋項目
        feedback_item = {
            "id": len(feedback_data) + 1,
            "timestamp": datetime.now().isoformat(),
            "query": feedback.query,
            "original_intent": feedback.original_intent,
            "corrected_intent": feedback.corrected_intent,
            "original_commercial_value": feedback.original_commercial_value,
            "corrected_commercial_value": feedback.corrected_commercial_value,
            "user_id": feedback.user_id,
            "feedback_notes": feedback.feedback_notes,
            "applied_to_model": False
        }
        
        # 添加到反饋數據
        feedback_data.append(feedback_item)
        
        # 保存更新後的反饋數據
        if save_feedback_data(feedback_data):
            return {
                "status": "success",
                "message": "反饋已成功提交",
                "feedback_id": feedback_item["id"]
            }
        else:
            raise HTTPException(status_code=500, detail="保存反饋數據失敗")
    except Exception as e:
        logger.error(f"提交反饋失敗: {e}")
        raise HTTPException(status_code=500, detail=f"提交反饋失敗: {str(e)}")

@router.post("/batch-corrections", response_model=Dict[str, Any])
async def submit_batch_corrections(
    batch: BatchIntentFeedback,
    background_tasks: BackgroundTasks
) -> Dict[str, Any]:
    """
    批量提交意圖分類校正反饋
    """
    try:
        # 載入現有反饋數據
        feedback_data = load_feedback_data()
        next_id = len(feedback_data) + 1
        
        # 處理每個反饋項目
        added_items = 0
        for item in batch.feedback_items:
            feedback_item = {
                "id": next_id,
                "timestamp": datetime.now().isoformat(),
                "query": item.query,
                "original_intent": item.original_intent,
                "corrected_intent": item.corrected_intent,
                "original_commercial_value": item.original_commercial_value,
                "corrected_commercial_value": item.corrected_commercial_value,
                "user_id": item.user_id,
                "feedback_notes": item.feedback_notes,
                "applied_to_model": False
            }
            
            # 添加到反饋數據
            feedback_data.append(feedback_item)
            next_id += 1
            added_items += 1
        
        # 保存更新後的反饋數據
        if save_feedback_data(feedback_data):
            return {
                "status": "success",
                "message": f"已成功提交 {added_items} 條反饋",
                "items_processed": added_items
            }
        else:
            raise HTTPException(status_code=500, detail="保存反饋數據失敗")
    except Exception as e:
        logger.error(f"批量提交反饋失敗: {e}")
        raise HTTPException(status_code=500, detail=f"批量提交反饋失敗: {str(e)}")

@router.get("/summary", response_model=FeedbackSummaryResponse)
async def get_feedback_summary() -> FeedbackSummaryResponse:
    """
    獲取反饋數據摘要
    """
    try:
        feedback_data = load_feedback_data()
        
        # 計算被糾正最多的意圖
        corrections = {}
        for item in feedback_data:
            original = item["original_intent"]
            corrected = item["corrected_intent"]
            if original != corrected:
                key = f"{original} -> {corrected}"
                corrections[key] = corrections.get(key, 0) + 1
        
        # 取前10個最常見的校正
        top_corrections = dict(sorted(corrections.items(), key=lambda x: x[1], reverse=True)[:10])
        
        # 獲取最近的10條校正
        recent = sorted(feedback_data, key=lambda x: x["timestamp"], reverse=True)[:10]
        
        return {
            "total_feedback": len(feedback_data),
            "recent_corrections": recent,
            "top_corrected_intents": top_corrections,
            "status": "success"
        }
    except Exception as e:
        logger.error(f"獲取反饋摘要失敗: {e}")
        raise HTTPException(status_code=500, detail=f"獲取反饋摘要失敗: {str(e)}")

@router.post("/retrain-model")
async def trigger_model_retraining(
    background_tasks: BackgroundTasks
) -> Dict[str, Any]:
    """
    觸發使用反饋數據重新訓練模型
    """
    try:
        # 導入模型訓練器
        from app.services.simple_model_trainer import BasicModelTrainer
        model_trainer = BasicModelTrainer()
        
        # 在背景任務中執行重訓
        background_tasks.add_task(retrain_model_with_feedback, model_trainer)
        
        return {
            "status": "success",
            "message": "模型重訓已在背景啟動"
        }
    except Exception as e:
        logger.error(f"啟動模型重訓失敗: {e}")
        raise HTTPException(status_code=500, detail=f"啟動模型重訓失敗: {str(e)}")

@router.get("/export", response_model=Dict[str, Any])
async def export_feedback_data() -> Dict[str, Any]:
    """
    導出所有反饋數據
    """
    try:
        feedback_data = load_feedback_data()
        return {
            "status": "success",
            "data": feedback_data,
            "count": len(feedback_data),
            "export_time": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"導出反饋數據失敗: {e}")
        raise HTTPException(status_code=500, detail=f"導出反饋數據失敗: {str(e)}")

@router.post("/clear", response_model=Dict[str, Any])
async def clear_feedback_data(
    confirm: bool = False
) -> Dict[str, Any]:
    """
    清除所有反饋數據（需確認）
    """
    if not confirm:
        raise HTTPException(status_code=400, detail="需要確認才能清除所有反饋數據")
    
    try:
        # 備份當前數據
        feedback_data = load_feedback_data()
        backup_file = FEEDBACK_DIR / f"intent_feedback_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        with open(backup_file, 'w', encoding='utf-8') as f:
            json.dump(feedback_data, f, ensure_ascii=False, indent=2)
        
        # 清除數據
        save_feedback_data([])
        
        return {
            "status": "success",
            "message": "所有反饋數據已清除",
            "backup_file": str(backup_file)
        }
    except Exception as e:
        logger.error(f"清除反饋數據失敗: {e}")
        raise HTTPException(status_code=500, detail=f"清除反饋數據失敗: {str(e)}")
