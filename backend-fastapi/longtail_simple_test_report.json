{"test_timestamp": "2025-07-01T13:46:24.596150", "test_type": "simplified_test", "system_components": {"data_pipeline": "✅ 可用", "feature_extraction": "✅ 可用", "language_detection": "✅ 可用", "intent_detection": "✅ 可用", "longtail_classification": "✅ 可用"}, "test_statistics": {"total_queries": 11, "longtail_queries": 0, "longtail_percentage": 0.0, "language_detection_accuracy": 71.43, "classification_accuracy": 54.55, "avg_complexity_score": 0.627, "processed_events": 5}, "performance_metrics": {"feature_extraction": "快速", "language_detection": "71.4% 準確率", "classification": "54.5% 準確率", "processing_speed": "< 1秒/查詢"}, "test_results": {"feature_extraction": "✅ 正常", "language_detection": "⚠️ 需要改進", "complexity_calculation": "✅ 正常", "intent_detection": "✅ 正常", "longtail_classification": "⚠️ 需要改進", "event_processing": "✅ 正常"}, "recommendations": ["基礎功能運行正常", "特徵提取算法工作良好", "建議改進語言檢測算法", "建議優化分類閾值", "建議安裝完整的ML庫以獲得更好的分類性能", "建議使用真實數據進行訓練以提升準確性"]}