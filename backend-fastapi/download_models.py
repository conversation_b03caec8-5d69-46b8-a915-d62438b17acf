#!/usr/bin/env python3
"""
長尾查詢識別系統 - 模型下載腳本
下載所需的預訓練模型用於長尾查詢分析
"""

import os
import sys
import logging
from pathlib import Path
from typing import List, Dict, Any

# 設置日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def create_models_directory() -> Path:
    """創建模型存儲目錄"""
    models_dir = Path("models")
    models_dir.mkdir(exist_ok=True)
    
    # 創建子目錄
    subdirs = [
        "language_models",
        "sentence_encoders", 
        "classification_models",
        "embeddings",
        "tokenizers"
    ]
    
    for subdir in subdirs:
        (models_dir / subdir).mkdir(exist_ok=True)
    
    logger.info(f"模型目錄創建完成: {models_dir.absolute()}")
    return models_dir

def download_language_models(models_dir: Path) -> None:
    """下載語言模型"""
    try:
        from transformers import AutoModel, AutoTokenizer
        
        language_models = [
            "bert-base-multilingual-cased",  # 多語言BERT
            "xlm-roberta-base",              # XLM-RoBERTa
            "distilbert-base-multilingual-cased",  # 輕量級多語言BERT
        ]
        
        lang_models_dir = models_dir / "language_models"
        
        for model_name in language_models:
            logger.info(f"下載語言模型: {model_name}")
            
            # 下載模型
            model = AutoModel.from_pretrained(
                model_name,
                cache_dir=str(lang_models_dir)
            )
            
            # 下載分詞器
            tokenizer = AutoTokenizer.from_pretrained(
                model_name,
                cache_dir=str(lang_models_dir)
            )
            
            logger.info(f"✓ {model_name} 下載完成")
            
    except ImportError:
        logger.error("transformers 庫未安裝，請先安裝依賴")
        sys.exit(1)
    except Exception as e:
        logger.error(f"下載語言模型失敗: {e}")
        sys.exit(1)

def download_sentence_encoders(models_dir: Path) -> None:
    """下載句子編碼模型"""
    try:
        from sentence_transformers import SentenceTransformer
        
        sentence_models = [
            "all-mpnet-base-v2",           # 英文句子編碼
            "paraphrase-multilingual-mpnet-base-v2",  # 多語言句子編碼
            "distiluse-base-multilingual-cased",      # 輕量級多語言編碼
        ]
        
        encoders_dir = models_dir / "sentence_encoders"
        
        for model_name in sentence_models:
            logger.info(f"下載句子編碼模型: {model_name}")
            
            model = SentenceTransformer(
                model_name,
                cache_folder=str(encoders_dir)
            )
            
            logger.info(f"✓ {model_name} 下載完成")
            
    except ImportError:
        logger.error("sentence-transformers 庫未安裝，請先安裝依賴")
        sys.exit(1)
    except Exception as e:
        logger.error(f"下載句子編碼模型失敗: {e}")
        sys.exit(1)

def download_nlp_resources() -> None:
    """下載NLP資源"""
    try:
        import nltk
        import spacy
        
        # 下載NLTK資源
        logger.info("下載NLTK資源...")
        nltk_resources = [
            'punkt',
            'stopwords', 
            'wordnet',
            'averaged_perceptron_tagger',
            'vader_lexicon'
        ]
        
        for resource in nltk_resources:
            try:
                nltk.download(resource, quiet=True)
                logger.info(f"✓ NLTK {resource} 下載完成")
            except Exception as e:
                logger.warning(f"NLTK {resource} 下載失敗: {e}")
        
        # 下載spaCy模型
        logger.info("下載spaCy模型...")
        spacy_models = [
            "zh_core_web_sm",  # 中文模型
            "en_core_web_sm",  # 英文模型
        ]
        
        for model in spacy_models:
            try:
                os.system(f"python -m spacy download {model}")
                logger.info(f"✓ spaCy {model} 下載完成")
            except Exception as e:
                logger.warning(f"spaCy {model} 下載失敗: {e}")
                
    except ImportError as e:
        logger.error(f"NLP庫未安裝: {e}")
        sys.exit(1)

def create_model_config(models_dir: Path) -> None:
    """創建模型配置文件"""
    config = {
        "language_models": {
            "bert_multilingual": "bert-base-multilingual-cased",
            "xlm_roberta": "xlm-roberta-base", 
            "distilbert_multilingual": "distilbert-base-multilingual-cased"
        },
        "sentence_encoders": {
            "mpnet_base": "all-mpnet-base-v2",
            "paraphrase_multilingual": "paraphrase-multilingual-mpnet-base-v2",
            "distiluse_multilingual": "distiluse-base-multilingual-cased"
        },
        "model_paths": {
            "language_models": str(models_dir / "language_models"),
            "sentence_encoders": str(models_dir / "sentence_encoders"),
            "classification_models": str(models_dir / "classification_models"),
            "embeddings": str(models_dir / "embeddings")
        },
        "default_models": {
            "query_classification": "bert-base-multilingual-cased",
            "sentence_embedding": "paraphrase-multilingual-mpnet-base-v2",
            "intent_detection": "xlm-roberta-base"
        }
    }
    
    import json
    config_file = models_dir / "models.config"
    with open(config_file, 'w', encoding='utf-8') as f:
        json.dump(config, f, indent=2, ensure_ascii=False)
    
    logger.info(f"模型配置文件創建完成: {config_file}")

def main():
    """主函數"""
    logger.info("開始下載長尾查詢識別系統所需模型...")
    
    # 創建模型目錄
    models_dir = create_models_directory()
    
    # 下載各類模型
    download_language_models(models_dir)
    download_sentence_encoders(models_dir)
    download_nlp_resources()
    
    # 創建配置文件
    create_model_config(models_dir)
    
    logger.info("所有模型下載完成！")
    logger.info(f"模型存儲位置: {models_dir.absolute()}")
    
    # 顯示磁盤使用情況
    total_size = sum(f.stat().st_size for f in models_dir.rglob('*') if f.is_file())
    logger.info(f"模型總大小: {total_size / (1024**3):.2f} GB")

if __name__ == "__main__":
    main()
