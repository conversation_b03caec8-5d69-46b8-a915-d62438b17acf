# 長尾查詢識別系統 - 完整使用指南

## 🎯 系統概覽

長尾查詢識別系統是一個基於AI技術的智能SEO工具，能夠自動識別和分析長尾關鍵詞，幫助優化搜索引擎策略。

### 核心功能
- **智能查詢分析**: 使用BERT多語言模型分析查詢意圖和特徵
- **長尾識別**: 自動識別長尾查詢並計算長尾分數
- **批量處理**: 支持大規模查詢批量分析
- **語義搜索**: 基於向量相似度的查詢搜索
- **實時監控**: Prometheus + Grafana監控系統
- **多語言支持**: 支持中文和英文查詢分析

## 🚀 快速開始

### 1. 系統要求
- Docker 20.10+
- Docker Compose 2.0+
- 至少 4GB 可用內存
- 至少 10GB 可用磁盤空間

### 2. 安裝部署

```bash
# 1. 克隆項目
git clone <repository-url>
cd AISEOking

# 2. 設置環境變量
cp .env.example .env
# 編輯 .env 文件，設置 OPENAI_API_KEY

# 3. 運行部署腳本
chmod +x scripts/deploy-longtail-system.sh
./scripts/deploy-longtail-system.sh

# 4. 驗證部署
curl http://localhost:8001/health
```

### 3. 訪問系統
- **API服務**: http://localhost:8001
- **API文檔**: http://localhost:8001/docs
- **管理界面**: http://localhost:3000/admin/longtail-analysis
- **監控面板**: http://localhost:3001 (admin/admin123)

## 📊 功能使用

### 單個查詢分析

```bash
# API調用示例
curl -X POST "http://localhost:8001/api/longtail/analyze" \
  -H "Content-Type: application/json" \
  -d '{
    "query": "如何選擇最適合小型企業的AI客服系統",
    "include_keywords": true,
    "include_suggestions": true
  }'
```

**響應示例**:
```json
{
  "query": "如何選擇最適合小型企業的AI客服系統",
  "query_type": "long_tail",
  "intent": "informational",
  "complexity": "complex",
  "longtail_score": 0.85,
  "confidence": 0.92,
  "keywords": ["選擇", "小型企業", "AI", "客服系統"],
  "semantic_category": "technology",
  "search_volume_estimate": 320,
  "competition_level": "medium",
  "optimization_suggestions": [
    "這是一個長尾查詢，競爭較低，適合精準定位",
    "問題型查詢，建議使用FAQ格式回答"
  ]
}
```

### 批量查詢分析

```bash
curl -X POST "http://localhost:8001/api/longtail/analyze/batch" \
  -H "Content-Type: application/json" \
  -d '{
    "queries": [
      "AI工具",
      "如何選擇AI寫作軟體",
      "人工智能在醫療領域的應用前景"
    ],
    "session_name": "測試批量分析",
    "description": "示例批量分析"
  }'
```

### 查詢搜索

```bash
# 搜索長尾查詢
curl "http://localhost:8001/api/longtail/search?query_type=long_tail&min_longtail_score=0.7&limit=10"

# 搜索特定意圖的查詢
curl "http://localhost:8001/api/longtail/search?intent=commercial&limit=20"
```

### 相似查詢查找

```bash
curl -X POST "http://localhost:8001/api/longtail/similar" \
  -H "Content-Type: application/json" \
  -d '{
    "query": "AI寫作工具推薦",
    "similarity_threshold": 0.7,
    "limit": 10
  }'
```

## 🎛️ 前端界面使用

### 1. 實時分析界面
訪問 `/admin/longtail-analysis` 頁面：

- **單個查詢分析**: 輸入查詢內容，獲得詳細分析結果
- **批量查詢分析**: 上傳查詢列表，生成批量分析報告
- **結果可視化**: 查看長尾分數、置信度、特徵分布等

### 2. 數據概覽
- **統計儀表板**: 查看總查詢數、長尾比例、意圖分布
- **趨勢分析**: 觀察查詢類型和分數的時間趨勢
- **性能指標**: 監控系統響應時間和準確度

### 3. 查詢搜索
- **高級篩選**: 按類型、意圖、分數範圍篩選查詢
- **關鍵詞搜索**: 基於內容搜索相關查詢
- **結果導出**: 導出搜索結果為CSV或Excel

## 📈 監控與運維

### 1. 系統監控
訪問 Grafana 儀表板 (http://localhost:3001):

- **系統概覽**: 請求速率、響應時間、資源使用
- **分析指標**: 長尾分數分布、置信度分布
- **性能監控**: 內存使用、CPU使用率、緩存命中率

### 2. 日誌查看
```bash
# 查看API日誌
docker logs longtail-api

# 查看所有服務狀態
docker-compose -f docker-compose.longtail.yml ps

# 查看特定服務日誌
docker-compose -f docker-compose.longtail.yml logs longtail-api
```

### 3. 健康檢查
```bash
# API健康檢查
curl http://localhost:8001/health

# 獲取系統指標
curl http://localhost:8001/metrics

# 檢查數據庫連接
curl http://localhost:8001/api/longtail/stats
```

## 🔧 配置與優化

### 1. 環境配置
編輯 `.env` 文件：

```env
# OpenAI API配置
OPENAI_API_KEY=your_api_key_here

# 數據庫配置
DATABASE_URL=postgresql://postgres:password@localhost:5433/aiseo_longtail

# Redis緩存配置
REDIS_URL=redis://localhost:6380/2

# 日誌級別
LOG_LEVEL=INFO

# 性能調優
BATCH_SIZE=100
CACHE_TTL=3600
MAX_WORKERS=4
```

### 2. 性能優化
- **緩存策略**: 調整Redis緩存TTL設置
- **批量大小**: 根據內存情況調整批量處理大小
- **工作進程**: 根據CPU核心數調整worker數量
- **模型優化**: 使用量化模型減少內存使用

### 3. 擴展配置
```yaml
# docker-compose.longtail.yml
services:
  longtail-api:
    deploy:
      replicas: 3  # 水平擴展
      resources:
        limits:
          memory: 4G
          cpus: '2'
```

## 🧪 測試

### 1. 運行單元測試
```bash
cd backend-fastapi
python -m pytest tests/test_longtail_analyzer.py -v
```

### 2. 運行集成測試
```bash
python -m pytest tests/test_longtail_integration.py -v
```

### 3. 性能測試
```bash
# 使用Artillery進行負載測試
artillery run performance/longtail-load-test.yml
```

## 🔍 故障排除

### 常見問題

1. **模型載入失敗**
   ```bash
   # 檢查模型文件
   ls -la backend-fastapi/models/
   
   # 重新下載模型
   cd backend-fastapi && python download_models.py
   ```

2. **內存不足**
   ```bash
   # 檢查內存使用
   docker stats
   
   # 調整Docker內存限制
   docker-compose -f docker-compose.longtail.yml down
   # 編輯docker-compose.longtail.yml，調整memory限制
   docker-compose -f docker-compose.longtail.yml up -d
   ```

3. **API響應慢**
   ```bash
   # 檢查緩存狀態
   curl http://localhost:8001/api/longtail/cache/info
   
   # 清理緩存
   curl -X DELETE http://localhost:8001/api/longtail/cache/clear
   ```

### 日誌分析
```bash
# 查看錯誤日誌
docker logs longtail-api 2>&1 | grep ERROR

# 查看性能日誌
docker logs longtail-api 2>&1 | grep "analysis_duration"

# 實時監控日誌
docker logs -f longtail-api
```

## 📚 API參考

### 核心端點
- `POST /api/longtail/analyze` - 單個查詢分析
- `POST /api/longtail/analyze/batch` - 批量查詢分析
- `GET /api/longtail/search` - 查詢搜索
- `POST /api/longtail/similar` - 相似查詢查找
- `GET /api/longtail/stats` - 統計信息
- `GET /health` - 健康檢查
- `GET /metrics` - Prometheus指標

### 響應格式
所有API響應都遵循統一格式：
```json
{
  "status": "success|error",
  "data": {...},
  "message": "描述信息",
  "timestamp": "2024-01-01T00:00:00Z"
}
```

## 🤝 貢獻指南

1. Fork 項目
2. 創建功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 創建 Pull Request

## 📄 許可證

本項目採用 MIT 許可證 - 查看 [LICENSE](LICENSE) 文件了解詳情。

## 📞 支持

如有問題或建議，請：
1. 查看 [FAQ](FAQ.md)
2. 提交 [Issue](https://github.com/your-repo/issues)
3. 聯繫技術支持團隊
