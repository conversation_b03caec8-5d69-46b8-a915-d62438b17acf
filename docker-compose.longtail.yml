version: '3.8'

services:
  # 長尾查詢識別系統 - FastAPI後端
  longtail-api:
    build:
      context: ./backend-fastapi
      dockerfile: Dockerfile.longtail
    container_name: longtail-api
    ports:
      - "8001:8000"
    environment:
      - DATABASE_URL=********************************************/aiseo_longtail
      - REDIS_URL=redis://redis:6379/2
      - ELASTICSEARCH_URL=http://elasticsearch:9200
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - ENVIRONMENT=production
      - LOG_LEVEL=INFO
    volumes:
      - ./backend-fastapi/models:/app/models
      - ./backend-fastapi/logs:/app/logs
    depends_on:
      - postgres
      - redis
      - elasticsearch
    networks:
      - longtail-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # PostgreSQL 數據庫
  postgres:
    image: postgres:15-alpine
    container_name: longtail-postgres
    environment:
      - POSTGRES_DB=aiseo_longtail
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/longtail_init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5433:5432"
    networks:
      - longtail-network
    restart: unless-stopped

  # Redis 緩存
  redis:
    image: redis:7-alpine
    container_name: longtail-redis
    command: redis-server --appendonly yes --maxmemory 512mb --maxmemory-policy allkeys-lru
    volumes:
      - redis_data:/data
    ports:
      - "6380:6379"
    networks:
      - longtail-network
    restart: unless-stopped

  # Elasticsearch 搜索引擎
  elasticsearch:
    image: elasticsearch:8.11.1
    container_name: longtail-elasticsearch
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms1g -Xmx1g"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    ports:
      - "9201:9200"
    networks:
      - longtail-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:9200/_cluster/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 5

  # Prometheus 監控
  prometheus:
    image: prom/prometheus:latest
    container_name: longtail-prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    volumes:
      - ./monitoring/prometheus-longtail.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    ports:
      - "9091:9090"
    networks:
      - longtail-network
    restart: unless-stopped

  # Grafana 儀表板
  grafana:
    image: grafana/grafana:latest
    container_name: longtail-grafana
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin123
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/longtail-dashboard.json:/etc/grafana/provisioning/dashboards/longtail.json
      - ./monitoring/grafana/dashboard.yml:/etc/grafana/provisioning/dashboards/dashboard.yml
      - ./monitoring/grafana/datasource.yml:/etc/grafana/provisioning/datasources/datasource.yml
    ports:
      - "3001:3000"
    networks:
      - longtail-network
    restart: unless-stopped
    depends_on:
      - prometheus

  # 模型服務 (可選)
  model-server:
    build:
      context: ./pytorch-serve
      dockerfile: Dockerfile.longtail
    container_name: longtail-model-server
    ports:
      - "8081:8080"
    environment:
      - MODEL_STORE=/models
    volumes:
      - ./backend-fastapi/models:/models
    networks:
      - longtail-network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 4G
        reservations:
          memory: 2G

  # Nginx 反向代理
  nginx:
    image: nginx:alpine
    container_name: longtail-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/longtail.conf:/etc/nginx/conf.d/default.conf
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - longtail-api
    networks:
      - longtail-network
    restart: unless-stopped

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  elasticsearch_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

networks:
  longtail-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
