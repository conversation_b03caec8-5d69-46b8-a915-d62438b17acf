#!/bin/bash

# 長尾查詢識別系統部署腳本
# 用於部署完整的長尾查詢識別系統

set -e

# 顏色定義
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日誌函數
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 檢查依賴
check_dependencies() {
    log_info "檢查系統依賴..."
    
    # 檢查Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安裝，請先安裝 Docker"
        exit 1
    fi
    
    # 檢查Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose 未安裝，請先安裝 Docker Compose"
        exit 1
    fi
    
    # 檢查可用內存
    available_memory=$(free -m | awk 'NR==2{printf "%.0f", $7}')
    if [ "$available_memory" -lt 4096 ]; then
        log_warning "可用內存少於4GB，可能影響ML模型性能"
    fi
    
    log_success "依賴檢查完成"
}

# 創建必要的目錄
create_directories() {
    log_info "創建必要的目錄..."
    
    mkdir -p data/postgres
    mkdir -p data/redis
    mkdir -p data/elasticsearch
    mkdir -p data/prometheus
    mkdir -p data/grafana
    mkdir -p logs/longtail
    mkdir -p models/longtail
    mkdir -p nginx/ssl
    
    log_success "目錄創建完成"
}

# 設置環境變量
setup_environment() {
    log_info "設置環境變量..."
    
    # 檢查 .env 文件
    if [ ! -f .env ]; then
        log_warning ".env 文件不存在，創建默認配置..."
        cat > .env << EOF
# 長尾查詢識別系統環境配置
OPENAI_API_KEY=your_openai_api_key_here
DATABASE_URL=postgresql://postgres:password@localhost:5433/aiseo_longtail
REDIS_URL=redis://localhost:6380/2
ELASTICSEARCH_URL=http://localhost:9201
ENVIRONMENT=production
LOG_LEVEL=INFO

# 監控配置
PROMETHEUS_URL=http://localhost:9091
GRAFANA_URL=http://localhost:3001
GRAFANA_ADMIN_PASSWORD=admin123

# 安全配置
JWT_SECRET_KEY=$(openssl rand -hex 32)
ENCRYPTION_KEY=$(openssl rand -hex 32)
EOF
        log_warning "請編輯 .env 文件並設置正確的 OPENAI_API_KEY"
    fi
    
    # 載入環境變量
    source .env
    
    log_success "環境變量設置完成"
}

# 初始化數據庫
init_database() {
    log_info "初始化數據庫..."
    
    # 創建數據庫初始化腳本
    cat > database/longtail_init.sql << EOF
-- 長尾查詢識別系統數據庫初始化

-- 創建數據庫
CREATE DATABASE aiseo_longtail;

-- 連接到新數據庫
\c aiseo_longtail;

-- 創建擴展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";
CREATE EXTENSION IF NOT EXISTS "btree_gin";

-- 創建長尾查詢表
CREATE TABLE IF NOT EXISTS longtail_queries (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    query_text TEXT NOT NULL,
    query_hash VARCHAR(64) UNIQUE NOT NULL,
    query_type VARCHAR(20) NOT NULL,
    intent VARCHAR(20) NOT NULL,
    complexity VARCHAR(20) NOT NULL,
    length INTEGER NOT NULL,
    word_count INTEGER NOT NULL,
    character_count INTEGER NOT NULL,
    language VARCHAR(10) NOT NULL,
    longtail_score FLOAT NOT NULL,
    confidence FLOAT NOT NULL,
    specificity_score FLOAT NOT NULL,
    commercial_intent_score FLOAT NOT NULL,
    has_question_words BOOLEAN DEFAULT FALSE,
    has_brand_names BOOLEAN DEFAULT FALSE,
    has_location BOOLEAN DEFAULT FALSE,
    has_numbers BOOLEAN DEFAULT FALSE,
    has_special_chars BOOLEAN DEFAULT FALSE,
    keywords TEXT[],
    semantic_category VARCHAR(50) NOT NULL,
    search_volume_estimate INTEGER NOT NULL,
    competition_level VARCHAR(20) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    analyzed_by VARCHAR(100)
);

-- 創建索引
CREATE INDEX idx_longtail_queries_type ON longtail_queries(query_type);
CREATE INDEX idx_longtail_queries_intent ON longtail_queries(intent);
CREATE INDEX idx_longtail_queries_score ON longtail_queries(longtail_score);
CREATE INDEX idx_longtail_queries_category ON longtail_queries(semantic_category);
CREATE INDEX idx_longtail_queries_created ON longtail_queries(created_at);
CREATE INDEX idx_longtail_queries_text_gin ON longtail_queries USING gin(query_text gin_trgm_ops);

-- 創建分析會話表
CREATE TABLE IF NOT EXISTS query_analysis_sessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    session_name VARCHAR(200) NOT NULL,
    description TEXT,
    total_queries INTEGER DEFAULT 0,
    longtail_queries INTEGER DEFAULT 0,
    longtail_percentage FLOAT DEFAULT 0.0,
    analysis_summary JSONB,
    status VARCHAR(20) DEFAULT 'pending',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE
);

-- 創建關鍵詞映射表
CREATE TABLE IF NOT EXISTS query_keyword_mappings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    query_id UUID NOT NULL,
    keyword VARCHAR(100) NOT NULL,
    relevance_score FLOAT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 創建相似度表
CREATE TABLE IF NOT EXISTS query_similarities (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    query1_id UUID NOT NULL,
    query2_id UUID NOT NULL,
    similarity_score FLOAT NOT NULL,
    similarity_type VARCHAR(20) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 插入示例數據
INSERT INTO longtail_queries (
    query_text, query_hash, query_type, intent, complexity,
    length, word_count, character_count, language,
    longtail_score, confidence, specificity_score, commercial_intent_score,
    has_question_words, semantic_category, search_volume_estimate, competition_level
) VALUES 
('AI工具', 'hash1', 'head', 'informational', 'simple', 4, 1, 4, 'zh', 0.2, 0.8, 0.3, 0.1, false, 'technology', 10000, 'high'),
('如何選擇最好的AI寫作軟體', 'hash2', 'long_tail', 'informational', 'complex', 12, 6, 12, 'zh', 0.8, 0.9, 0.7, 0.4, true, 'technology', 500, 'medium');
EOF
    
    log_success "數據庫初始化腳本創建完成"
}

# 配置Nginx
setup_nginx() {
    log_info "配置Nginx..."
    
    cat > nginx/longtail.conf << EOF
upstream longtail_api {
    server longtail-api:8000;
}

upstream grafana {
    server longtail-grafana:3000;
}

server {
    listen 80;
    server_name localhost;
    
    # API代理
    location /api/longtail/ {
        proxy_pass http://longtail_api;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_read_timeout 300s;
        proxy_connect_timeout 75s;
    }
    
    # 監控代理
    location /monitoring/ {
        proxy_pass http://grafana/;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }
    
    # 健康檢查
    location /health {
        proxy_pass http://longtail_api/health;
    }
    
    # 靜態文件
    location / {
        root /usr/share/nginx/html;
        index index.html;
        try_files \$uri \$uri/ /index.html;
    }
}
EOF
    
    log_success "Nginx配置完成"
}

# 配置Prometheus
setup_prometheus() {
    log_info "配置Prometheus..."
    
    cat > monitoring/prometheus-longtail.yml << EOF
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "longtail_alert_rules.yml"

scrape_configs:
  - job_name: 'longtail-api'
    static_configs:
      - targets: ['longtail-api:8000']
    metrics_path: '/metrics'
    scrape_interval: 30s

  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093
EOF
    
    log_success "Prometheus配置完成"
}

# 部署系統
deploy_system() {
    log_info "開始部署長尾查詢識別系統..."
    
    # 停止現有服務
    log_info "停止現有服務..."
    docker-compose -f docker-compose.longtail.yml down
    
    # 構建鏡像
    log_info "構建Docker鏡像..."
    docker-compose -f docker-compose.longtail.yml build
    
    # 啟動服務
    log_info "啟動服務..."
    docker-compose -f docker-compose.longtail.yml up -d
    
    # 等待服務啟動
    log_info "等待服務啟動..."
    sleep 30
    
    # 檢查服務狀態
    check_services
    
    log_success "長尾查詢識別系統部署完成！"
}

# 檢查服務狀態
check_services() {
    log_info "檢查服務狀態..."
    
    services=("longtail-api" "longtail-postgres" "longtail-redis" "longtail-elasticsearch" "longtail-prometheus" "longtail-grafana")
    
    for service in "${services[@]}"; do
        if docker ps | grep -q "$service"; then
            log_success "$service 運行正常"
        else
            log_error "$service 未運行"
        fi
    done
    
    # 檢查API健康狀態
    log_info "檢查API健康狀態..."
    if curl -f http://localhost:8001/health > /dev/null 2>&1; then
        log_success "API健康檢查通過"
    else
        log_warning "API健康檢查失敗，請檢查日誌"
    fi
}

# 顯示訪問信息
show_access_info() {
    log_info "系統訪問信息："
    echo "=================================="
    echo "長尾查詢API: http://localhost:8001"
    echo "API文檔: http://localhost:8001/docs"
    echo "Grafana監控: http://localhost:3001 (admin/admin123)"
    echo "Prometheus: http://localhost:9091"
    echo "Elasticsearch: http://localhost:9201"
    echo "=================================="
}

# 主函數
main() {
    log_info "開始部署長尾查詢識別系統..."
    
    check_dependencies
    create_directories
    setup_environment
    init_database
    setup_nginx
    setup_prometheus
    deploy_system
    show_access_info
    
    log_success "部署完成！"
}

# 執行主函數
main "$@"
