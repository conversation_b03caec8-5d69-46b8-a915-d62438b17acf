'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  Search, 
  TrendingUp, 
  BarChart3, 
  Target, 
  Brain,
  Filter,
  Download,
  RefreshCw
} from 'lucide-react';
import { toast } from 'sonner';
import LongTailQueryAnalyzer from '@/components/longtail/LongTailQueryAnalyzer';

// 類型定義
interface QueryStats {
  total_queries: number;
  longtail_queries: number;
  longtail_percentage: number;
  intent_distribution: Record<string, number>;
  complexity_distribution: Record<string, number>;
  category_distribution: Record<string, number>;
  language_distribution: Record<string, number>;
  avg_longtail_score: number;
  avg_confidence: number;
}

interface QuerySearchResult {
  total: number;
  queries: Array<{
    query: string;
    query_type: string;
    intent: string;
    longtail_score: number;
    confidence: number;
    semantic_category: string;
    search_volume_estimate: number;
    competition_level: string;
    timestamp: string;
  }>;
  aggregations: Record<string, any>;
}

const LongTailAnalysisPage: React.FC = () => {
  const [stats, setStats] = useState<QueryStats | null>(null);
  const [searchResults, setSearchResults] = useState<QuerySearchResult | null>(null);
  const [loading, setLoading] = useState(false);
  const [searchLoading, setSearchLoading] = useState(false);
  
  // 搜索參數
  const [searchQuery, setSearchQuery] = useState('');
  const [queryType, setQueryType] = useState('');
  const [intent, setIntent] = useState('');
  const [category, setCategory] = useState('');
  const [minScore, setMinScore] = useState('');
  const [maxScore, setMaxScore] = useState('');

  // 載入統計數據
  const loadStats = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/longtail/stats');
      if (!response.ok) {
        throw new Error('載入統計數據失敗');
      }
      const data: QueryStats = await response.json();
      setStats(data);
    } catch (error) {
      console.error('載入統計數據失敗:', error);
      toast.error('載入統計數據失敗');
    } finally {
      setLoading(false);
    }
  };

  // 搜索查詢
  const searchQueries = async () => {
    setSearchLoading(true);
    try {
      const params = new URLSearchParams();
      if (searchQuery) params.append('query_text', searchQuery);
      if (queryType) params.append('query_type', queryType);
      if (intent) params.append('intent', intent);
      if (category) params.append('semantic_category', category);
      if (minScore) params.append('min_longtail_score', minScore);
      if (maxScore) params.append('max_longtail_score', maxScore);
      params.append('limit', '50');

      const response = await fetch(`/api/longtail/search?${params}`);
      if (!response.ok) {
        throw new Error('搜索失敗');
      }
      const data: QuerySearchResult = await response.json();
      setSearchResults(data);
    } catch (error) {
      console.error('搜索失敗:', error);
      toast.error('搜索失敗');
    } finally {
      setSearchLoading(false);
    }
  };

  // 清除搜索條件
  const clearSearch = () => {
    setSearchQuery('');
    setQueryType('');
    setIntent('');
    setCategory('');
    setMinScore('');
    setMaxScore('');
    setSearchResults(null);
  };

  // 導出數據
  const exportData = async () => {
    try {
      toast.info('導出功能開發中...');
    } catch (error) {
      toast.error('導出失敗');
    }
  };

  // 獲取查詢類型顏色
  const getQueryTypeColor = (type: string) => {
    switch (type) {
      case 'head': return 'bg-red-100 text-red-800';
      case 'middle': return 'bg-yellow-100 text-yellow-800';
      case 'long_tail': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  // 獲取意圖顏色
  const getIntentColor = (intent: string) => {
    switch (intent) {
      case 'informational': return 'bg-blue-100 text-blue-800';
      case 'navigational': return 'bg-purple-100 text-purple-800';
      case 'transactional': return 'bg-green-100 text-green-800';
      case 'commercial': return 'bg-orange-100 text-orange-800';
      case 'local': return 'bg-pink-100 text-pink-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  // 獲取競爭程度顏色
  const getCompetitionColor = (level: string) => {
    switch (level) {
      case 'high': return 'bg-red-100 text-red-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'low': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  useEffect(() => {
    loadStats();
  }, []);

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">長尾查詢分析管理</h1>
          <p className="text-gray-600 mt-1">
            管理和分析長尾查詢數據，優化SEO策略
          </p>
        </div>
        <div className="flex gap-2">
          <Button onClick={loadStats} variant="outline" disabled={loading}>
            <RefreshCw className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            刷新
          </Button>
          <Button onClick={exportData} variant="outline">
            <Download className="w-4 h-4 mr-2" />
            導出
          </Button>
        </div>
      </div>

      <Tabs defaultValue="overview" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="overview">數據概覽</TabsTrigger>
          <TabsTrigger value="search">查詢搜索</TabsTrigger>
          <TabsTrigger value="analyzer">實時分析</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {stats && (
            <>
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <Card>
                  <CardContent className="p-6 text-center">
                    <div className="text-3xl font-bold text-blue-600">
                      {stats.total_queries.toLocaleString()}
                    </div>
                    <div className="text-sm text-gray-600 mt-1">總查詢數</div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-6 text-center">
                    <div className="text-3xl font-bold text-green-600">
                      {stats.longtail_queries.toLocaleString()}
                    </div>
                    <div className="text-sm text-gray-600 mt-1">長尾查詢</div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-6 text-center">
                    <div className="text-3xl font-bold text-purple-600">
                      {stats.longtail_percentage.toFixed(1)}%
                    </div>
                    <div className="text-sm text-gray-600 mt-1">長尾比例</div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-6 text-center">
                    <div className="text-3xl font-bold text-orange-600">
                      {stats.avg_longtail_score.toFixed(2)}
                    </div>
                    <div className="text-sm text-gray-600 mt-1">平均長尾分數</div>
                  </CardContent>
                </Card>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle>查詢意圖分布</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {Object.entries(stats.intent_distribution).map(([intent, count]) => (
                        <div key={intent} className="flex justify-between items-center">
                          <Badge className={getIntentColor(intent)}>
                            {intent === 'informational' ? '信息型' :
                             intent === 'navigational' ? '導航型' :
                             intent === 'transactional' ? '交易型' :
                             intent === 'commercial' ? '商業型' : '本地型'}
                          </Badge>
                          <span className="font-medium">{count}</span>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>複雜度分布</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {Object.entries(stats.complexity_distribution).map(([complexity, count]) => (
                        <div key={complexity} className="flex justify-between items-center">
                          <Badge variant="outline">
                            {complexity === 'simple' ? '簡單' :
                             complexity === 'moderate' ? '中等' : '複雜'}
                          </Badge>
                          <span className="font-medium">{count}</span>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>語義類別分布</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2 max-h-48 overflow-y-auto">
                      {Object.entries(stats.category_distribution)
                        .sort(([,a], [,b]) => b - a)
                        .slice(0, 10)
                        .map(([category, count]) => (
                        <div key={category} className="flex justify-between items-center">
                          <span className="text-sm">{category}</span>
                          <Badge variant="secondary">{count}</Badge>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>語言分布</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {Object.entries(stats.language_distribution).map(([language, count]) => (
                        <div key={language} className="flex justify-between items-center">
                          <Badge variant="outline">
                            {language === 'zh' ? '中文' : language === 'en' ? '英文' : language}
                          </Badge>
                          <span className="font-medium">{count}</span>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </div>
            </>
          )}
        </TabsContent>

        <TabsContent value="search" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Filter className="w-5 h-5" />
                搜索條件
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Input
                  placeholder="搜索查詢內容..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
                
                <Select value={queryType} onValueChange={setQueryType}>
                  <SelectTrigger>
                    <SelectValue placeholder="查詢類型" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">全部類型</SelectItem>
                    <SelectItem value="head">頭部查詢</SelectItem>
                    <SelectItem value="middle">中部查詢</SelectItem>
                    <SelectItem value="long_tail">長尾查詢</SelectItem>
                  </SelectContent>
                </Select>

                <Select value={intent} onValueChange={setIntent}>
                  <SelectTrigger>
                    <SelectValue placeholder="查詢意圖" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">全部意圖</SelectItem>
                    <SelectItem value="informational">信息型</SelectItem>
                    <SelectItem value="navigational">導航型</SelectItem>
                    <SelectItem value="transactional">交易型</SelectItem>
                    <SelectItem value="commercial">商業型</SelectItem>
                    <SelectItem value="local">本地型</SelectItem>
                  </SelectContent>
                </Select>

                <Input
                  placeholder="最小長尾分數 (0-1)"
                  value={minScore}
                  onChange={(e) => setMinScore(e.target.value)}
                  type="number"
                  min="0"
                  max="1"
                  step="0.1"
                />

                <Input
                  placeholder="最大長尾分數 (0-1)"
                  value={maxScore}
                  onChange={(e) => setMaxScore(e.target.value)}
                  type="number"
                  min="0"
                  max="1"
                  step="0.1"
                />

                <Input
                  placeholder="語義類別"
                  value={category}
                  onChange={(e) => setCategory(e.target.value)}
                />
              </div>

              <div className="flex gap-2 mt-4">
                <Button onClick={searchQueries} disabled={searchLoading}>
                  <Search className="w-4 h-4 mr-2" />
                  搜索
                </Button>
                <Button onClick={clearSearch} variant="outline">
                  清除條件
                </Button>
              </div>
            </CardContent>
          </Card>

          {searchResults && (
            <Card>
              <CardHeader>
                <CardTitle>搜索結果 ({searchResults.total} 條)</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {searchResults.queries.map((query, index) => (
                    <div key={index} className="border rounded-lg p-4 space-y-2">
                      <div className="flex justify-between items-start">
                        <div className="flex-1">
                          <div className="font-medium text-lg">{query.query}</div>
                          <div className="text-sm text-gray-600 mt-1">
                            {new Date(query.timestamp).toLocaleString()}
                          </div>
                        </div>
                        <div className="flex gap-2">
                          <Badge className={getQueryTypeColor(query.query_type)}>
                            {query.query_type === 'long_tail' ? '長尾' : 
                             query.query_type === 'head' ? '頭部' : '中部'}
                          </Badge>
                          <Badge className={getIntentColor(query.intent)}>
                            {query.intent === 'informational' ? '信息型' :
                             query.intent === 'navigational' ? '導航型' :
                             query.intent === 'transactional' ? '交易型' :
                             query.intent === 'commercial' ? '商業型' : '本地型'}
                          </Badge>
                        </div>
                      </div>
                      
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                        <div>
                          <span className="text-gray-600">長尾分數:</span>
                          <span className="ml-1 font-medium">
                            {(query.longtail_score * 100).toFixed(1)}%
                          </span>
                        </div>
                        <div>
                          <span className="text-gray-600">置信度:</span>
                          <span className="ml-1 font-medium">
                            {(query.confidence * 100).toFixed(1)}%
                          </span>
                        </div>
                        <div>
                          <span className="text-gray-600">搜索量:</span>
                          <span className="ml-1 font-medium">
                            {query.search_volume_estimate.toLocaleString()}
                          </span>
                        </div>
                        <div>
                          <span className="text-gray-600">競爭:</span>
                          <Badge className={getCompetitionColor(query.competition_level)} variant="outline">
                            {query.competition_level === 'high' ? '高' :
                             query.competition_level === 'medium' ? '中' : '低'}
                          </Badge>
                        </div>
                      </div>
                      
                      <div className="text-sm">
                        <span className="text-gray-600">語義類別:</span>
                        <Badge variant="secondary" className="ml-2">
                          {query.semantic_category}
                        </Badge>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="analyzer">
          <LongTailQueryAnalyzer />
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default LongTailAnalysisPage;
