'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Loader2, Search, TrendingUp, BarChart3 } from 'lucide-react';
import { toast } from 'sonner';
import LongTailQueryAnalyzer from '@/components/longtail/LongTailQueryAnalyzer';

// 類型定義
interface QueryStats {
  total_queries: number;
  longtail_queries: number;
  longtail_percentage: number;
  avg_longtail_score: number;
  avg_confidence: number;
}

const LongTailAnalysisPage: React.FC = () => {
  const [stats, setStats] = useState<QueryStats | null>(null);
  const [loading, setLoading] = useState(false);

  // 載入統計數據
  const loadStats = async () => {
    setLoading(true);
    try {
      // 模擬API調用
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setStats({
        total_queries: 1250,
        longtail_queries: 875,
        longtail_percentage: 70.0,
        avg_longtail_score: 0.75,
        avg_confidence: 0.85
      });
      
      toast.success('統計數據載入成功');
    } catch (error) {
      console.error('載入統計數據失敗:', error);
      toast.error('載入統計數據失敗');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadStats();
  }, []);

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">長尾查詢分析管理</h1>
          <p className="text-gray-600 mt-1">
            管理和分析長尾查詢數據，優化SEO策略
          </p>
        </div>
        <Button onClick={loadStats} disabled={loading}>
          {loading ? (
            <Loader2 className="w-4 h-4 animate-spin mr-2" />
          ) : (
            <BarChart3 className="w-4 h-4 mr-2" />
          )}
          刷新數據
        </Button>
      </div>

      {/* 統計卡片 */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">總查詢數</CardTitle>
              <Search className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.total_queries.toLocaleString()}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">長尾查詢數</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.longtail_queries.toLocaleString()}</div>
              <p className="text-xs text-muted-foreground">
                佔比 {stats.longtail_percentage.toFixed(1)}%
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">平均長尾分數</CardTitle>
              <BarChart3 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{(stats.avg_longtail_score * 100).toFixed(1)}%</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">平均置信度</CardTitle>
              <BarChart3 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{(stats.avg_confidence * 100).toFixed(1)}%</div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* 主要內容區域 */}
      <Tabs defaultValue="analyzer" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="analyzer">查詢分析器</TabsTrigger>
          <TabsTrigger value="search">查詢搜索</TabsTrigger>
        </TabsList>

        <TabsContent value="analyzer" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>長尾查詢分析器</CardTitle>
              <CardDescription>
                使用AI技術分析查詢類型、意圖和長尾特徵
              </CardDescription>
            </CardHeader>
            <CardContent>
              <LongTailQueryAnalyzer />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="search" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>查詢搜索</CardTitle>
              <CardDescription>
                搜索和篩選已分析的查詢數據
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8 text-gray-500">
                查詢搜索功能開發中...
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default LongTailAnalysisPage;
