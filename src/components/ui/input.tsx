import * as React from "react"

import { cn } from "@/lib/utils"

function Input({ className, type, value, ...props }: React.ComponentProps<"input">) {
  // 簡化的值處理邏輯 - 只處理真正需要處理的情況
  const safeValue = React.useMemo(() => {
    // 對於undefined或null，返回空字符串
    if (value === undefined || value === null) {
      return '';
    }

    // 對於數字類型輸入，只在值確實是數字且無效時處理
    if (type === 'number' && typeof value === 'number') {
      if (isNaN(value) || !isFinite(value)) {
        return '';
      }
      return String(value);
    }

    // 對於字符串類型，直接返回，不做額外處理
    return value;
  }, [type, value]);

  // 使用固定的 className 避免 Hydration 不匹配
  const fixedClassName = React.useMemo(() => {
    // 強制使用統一的樣式 - 避免 shadcn/ui 自動樣式變更
    const baseStyles = [
      "flex",
      "h-10", 
      "w-full",
      "rounded-lg",
      "border", 
      "border-gray-300",
      "bg-white",
      "px-3",
      "py-2",
      "text-sm",
      "ring-offset-background",
      "file:border-0",
      "file:bg-transparent", 
      "file:text-sm",
      "file:font-medium",
      "placeholder:text-gray-500",
      "selection:bg-blue-100",
      "selection:text-blue-900",
      "focus-visible:outline-none",
      "focus-visible:ring-2",
      "focus-visible:ring-blue-500",
      "focus-visible:ring-offset-2",
      "hover:border-gray-400",
      "transition-colors",
      "duration-200",
      "disabled:cursor-not-allowed",
      "disabled:opacity-50",
      "aria-invalid:ring-red-500/20",
      "aria-invalid:border-red-500"
    ];
    
    return cn(baseStyles.join(" "), className);
  }, [className]);

  return (
    <input
      type={type}
      value={safeValue}
      data-slot="input"
      className={fixedClassName}
      {...props}
    />
  )
}

export { Input }
