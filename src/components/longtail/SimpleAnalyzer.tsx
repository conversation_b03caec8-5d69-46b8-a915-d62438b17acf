'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

export default function SimpleAnalyzer() {
  const [query, setQuery] = useState('');
  const [result, setResult] = useState<any>(null);
  const [loading, setLoading] = useState(false);

  const handleAnalyze = () => {
    console.log('handleAnalyze called with query:', query);
    console.log('Query length:', query.length);
    console.log('Query trimmed:', query.trim());
    alert(`準備分析查詢: "${query}" (長度: ${query.length})`);

    if (!query || !query.trim()) {
      alert(`請輸入查詢內容 (當前值: "${query}")`);
      return;
    }

    setLoading(true);

    // 使用setTimeout來模擬異步操作，避免async/await問題
    setTimeout(async () => {
      try {
        console.log('開始發送API請求...');
        const response = await fetch('http://localhost:8001/api/v1/longtail/analyze', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            query: query.trim(),
            include_keywords: true,
            include_suggestions: true,
          }),
        });

        console.log('API響應狀態:', response.status);

        if (!response.ok) {
          throw new Error(`API錯誤: ${response.status}`);
        }

        const data = await response.json();
        console.log('API響應數據:', data);
        setResult(data);
        alert('分析完成！');
      } catch (error) {
        console.error('分析失敗:', error);
        alert(`分析失敗: ${error}`);
      } finally {
        setLoading(false);
      }
    }, 100);
  };

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle>簡化版查詢分析器</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex gap-2">
          <Input
            placeholder="輸入查詢內容..."
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            className="flex-1"
          />
          <Button 
            onClick={handleAnalyze}
            disabled={loading}
          >
            {loading ? '分析中...' : '分析'}
          </Button>
        </div>
        
        <div className="flex gap-2">
          <Button
            onClick={() => alert('測試按鈕工作正常！')}
            variant="outline"
          >
            測試按鈕
          </Button>

          <Button
            onClick={() => {
              console.log('Debug - Current query state:', query);
              alert(`調試信息:\n查詢值: "${query}"\n長度: ${query.length}\n類型: ${typeof query}`);
            }}
            variant="outline"
          >
            調試狀態
          </Button>
        </div>

        {result && (
          <div className="mt-4 p-4 bg-gray-100 rounded">
            <h3 className="font-bold">分析結果:</h3>
            <pre className="text-sm mt-2 overflow-auto">
              {JSON.stringify(result, null, 2)}
            </pre>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
