'use client';

import React, { useState, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, Search, TrendingUp, Target, Brain, BarChart3 } from 'lucide-react';
import { toast } from 'sonner';

// 類型定義
interface QueryFeatures {
  length: number;
  word_count: number;
  character_count: number;
  has_question_words: boolean;
  has_brand_names: boolean;
  has_location: boolean;
  has_numbers: boolean;
  has_special_chars: boolean;
  language: string;
  specificity_score: number;
  commercial_intent_score: number;
}

interface LongTailAnalysisResult {
  query: string;
  query_type: 'head' | 'middle' | 'long_tail';
  intent: 'informational' | 'navigational' | 'transactional' | 'commercial' | 'local';
  complexity: 'simple' | 'moderate' | 'complex';
  features: QueryFeatures;
  longtail_score: number;
  confidence: number;
  keywords: string[];
  semantic_category: string;
  search_volume_estimate: number;
  competition_level: string;
  optimization_suggestions: string[];
  timestamp: string;
}

interface BatchAnalysisResult {
  session_id: string;
  total_queries: number;
  processed_queries: number;
  longtail_queries: number;
  longtail_percentage: number;
  results: LongTailAnalysisResult[];
  summary: {
    total_queries: number;
    longtail_queries: number;
    longtail_percentage: number;
    avg_longtail_score: number;
    avg_confidence: number;
  };
  recommendations: string[];
}

const LongTailQueryAnalyzer: React.FC = () => {
  const [singleQuery, setSingleQuery] = useState('');
  const [batchQueries, setBatchQueries] = useState('');
  const [singleResult, setSingleResult] = useState<LongTailAnalysisResult | null>(null);
  const [batchResult, setBatchResult] = useState<BatchAnalysisResult | null>(null);
  const [loading, setLoading] = useState(false);
  const [batchLoading, setBatchLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('single');

  // 單個查詢分析
  const analyzeSingleQuery = useCallback(async () => {
    console.log('analyzeSingleQuery called');
    console.log('singleQuery value:', singleQuery);
    console.log('singleQuery length:', singleQuery.length);
    console.log('singleQuery trimmed:', singleQuery.trim());

    if (!singleQuery.trim()) {
      console.log('Query is empty, showing error');
      toast.error(`請輸入查詢內容 (當前值: "${singleQuery}")`);
      return;
    }

    setLoading(true);

    try {
      const response = await fetch('http://localhost:8001/api/v1/longtail/analyze', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          query: singleQuery.trim(),
          include_keywords: true,
          include_suggestions: true,
        }),
      });

      if (!response.ok) {
        throw new Error('分析請求失敗');
      }

      const result: LongTailAnalysisResult = await response.json();
      setSingleResult(result);
      toast.success('查詢分析完成');
    } catch (error) {
      console.error('分析失敗:', error);
      toast.error('分析失敗，請稍後重試');
    } finally {
      setLoading(false);
    }
  }, [singleQuery]);

  // 批量查詢分析
  const analyzeBatchQueries = useCallback(async () => {
    const queries = batchQueries
      .split('\n')
      .map(q => q.trim())
      .filter(q => q.length > 0);

    if (queries.length === 0) {
      toast.error('請輸入查詢列表');
      return;
    }

    if (queries.length > 100) {
      toast.error('批量分析最多支持100個查詢');
      return;
    }

    setBatchLoading(true);
    try {
      const response = await fetch('http://localhost:8001/api/v1/longtail/analyze/batch', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          queries,
          session_name: `批量分析_${new Date().toLocaleString()}`,
          description: '前端批量查詢分析',
          include_similarities: false,
        }),
      });

      if (!response.ok) {
        throw new Error('批量分析請求失敗');
      }

      const result: BatchAnalysisResult = await response.json();
      setBatchResult(result);
      toast.success(`批量分析完成，共處理 ${result.processed_queries} 個查詢`);
    } catch (error) {
      console.error('批量分析失敗:', error);
      toast.error('批量分析失敗，請稍後重試');
    } finally {
      setBatchLoading(false);
    }
  }, [batchQueries]);

  // 獲取查詢類型顏色
  const getQueryTypeColor = (type: string) => {
    switch (type) {
      case 'head': return 'bg-red-100 text-red-800';
      case 'middle': return 'bg-yellow-100 text-yellow-800';
      case 'long_tail': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  // 獲取意圖顏色
  const getIntentColor = (intent: string) => {
    switch (intent) {
      case 'informational': return 'bg-blue-100 text-blue-800';
      case 'navigational': return 'bg-purple-100 text-purple-800';
      case 'transactional': return 'bg-green-100 text-green-800';
      case 'commercial': return 'bg-orange-100 text-orange-800';
      case 'local': return 'bg-pink-100 text-pink-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  // 獲取競爭程度顏色
  const getCompetitionColor = (level: string) => {
    switch (level) {
      case 'high': return 'bg-red-100 text-red-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'low': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold text-gray-900">長尾查詢識別系統</h1>
        <p className="text-gray-600">
          使用AI技術分析查詢類型、意圖和長尾特徵，優化SEO策略
        </p>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="single" className="flex items-center gap-2">
            <Search className="w-4 h-4" />
            單個查詢分析
          </TabsTrigger>
          <TabsTrigger value="batch" className="flex items-center gap-2">
            <BarChart3 className="w-4 h-4" />
            批量查詢分析
          </TabsTrigger>
        </TabsList>

        <TabsContent value="single" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Brain className="w-5 h-5" />
                查詢分析
              </CardTitle>
              <CardDescription>
                輸入查詢內容，系統將分析其長尾特徵、意圖和優化建議
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex gap-2">
                <Input
                  placeholder="輸入要分析的查詢..."
                  value={singleQuery}
                  onChange={(e) => {
                    console.log('Input onChange triggered:', e.target.value);
                    console.log('Event object:', e);
                    console.log('Current singleQuery state:', singleQuery);
                    setSingleQuery(e.target.value);
                    console.log('setSingleQuery called with:', e.target.value);
                  }}
                  onKeyPress={(e) => e.key === 'Enter' && analyzeSingleQuery()}
                  onFocus={() => console.log('Input focused')}
                  onBlur={() => console.log('Input blurred')}
                  onClick={() => console.log('Input clicked')}
                  className="flex-1"
                />
                <Button
                  onClick={analyzeSingleQuery}
                  disabled={loading}
                  className="px-6"
                >
                  {loading ? (
                    <Loader2 className="w-4 h-4 animate-spin" />
                  ) : (
                    <Search className="w-4 h-4" />
                  )}
                  分析
                </Button>
              </div>

              {singleResult && (
                <div className="space-y-4 mt-6">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <Card>
                      <CardContent className="p-4">
                        <div className="text-sm text-gray-600">查詢類型</div>
                        <Badge className={getQueryTypeColor(singleResult.query_type)}>
                          {singleResult.query_type === 'long_tail' ? '長尾查詢' : 
                           singleResult.query_type === 'head' ? '頭部查詢' : '中部查詢'}
                        </Badge>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardContent className="p-4">
                        <div className="text-sm text-gray-600">查詢意圖</div>
                        <Badge className={getIntentColor(singleResult.intent)}>
                          {singleResult.intent === 'informational' ? '信息型' :
                           singleResult.intent === 'navigational' ? '導航型' :
                           singleResult.intent === 'transactional' ? '交易型' :
                           singleResult.intent === 'commercial' ? '商業型' : '本地型'}
                        </Badge>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardContent className="p-4">
                        <div className="text-sm text-gray-600">競爭程度</div>
                        <Badge className={getCompetitionColor(singleResult.competition_level)}>
                          {singleResult.competition_level === 'high' ? '高' :
                           singleResult.competition_level === 'medium' ? '中' : '低'}
                        </Badge>
                      </CardContent>
                    </Card>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <Card>
                      <CardHeader>
                        <CardTitle className="text-lg">分析指標</CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-3">
                        <div>
                          <div className="flex justify-between text-sm">
                            <span>長尾分數</span>
                            <span>{(singleResult.longtail_score * 100).toFixed(1)}%</span>
                          </div>
                          <Progress value={singleResult.longtail_score * 100} className="mt-1" />
                        </div>
                        <div>
                          <div className="flex justify-between text-sm">
                            <span>置信度</span>
                            <span>{(singleResult.confidence * 100).toFixed(1)}%</span>
                          </div>
                          <Progress value={singleResult.confidence * 100} className="mt-1" />
                        </div>
                        <div>
                          <div className="flex justify-between text-sm">
                            <span>商業意圖分數</span>
                            <span>{(singleResult.features.commercial_intent_score * 100).toFixed(1)}%</span>
                          </div>
                          <Progress value={singleResult.features.commercial_intent_score * 100} className="mt-1" />
                        </div>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardHeader>
                        <CardTitle className="text-lg">查詢特徵</CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-2">
                        <div className="grid grid-cols-2 gap-2 text-sm">
                          <div>詞數: {singleResult.features.word_count}</div>
                          <div>字符數: {singleResult.features.character_count}</div>
                          <div>語言: {singleResult.features.language}</div>
                          <div>語義類別: {singleResult.semantic_category}</div>
                        </div>
                        <div className="flex flex-wrap gap-1 mt-2">
                          {singleResult.features.has_question_words && (
                            <Badge variant="outline">包含疑問詞</Badge>
                          )}
                          {singleResult.features.has_brand_names && (
                            <Badge variant="outline">包含品牌名</Badge>
                          )}
                          {singleResult.features.has_location && (
                            <Badge variant="outline">包含地理位置</Badge>
                          )}
                          {singleResult.features.has_numbers && (
                            <Badge variant="outline">包含數字</Badge>
                          )}
                        </div>
                      </CardContent>
                    </Card>
                  </div>

                  {singleResult.keywords.length > 0 && (
                    <Card>
                      <CardHeader>
                        <CardTitle className="text-lg">提取關鍵詞</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="flex flex-wrap gap-2">
                          {singleResult.keywords.map((keyword, index) => (
                            <Badge key={index} variant="secondary">
                              {keyword}
                            </Badge>
                          ))}
                        </div>
                      </CardContent>
                    </Card>
                  )}

                  {singleResult.optimization_suggestions.length > 0 && (
                    <Card>
                      <CardHeader>
                        <CardTitle className="text-lg flex items-center gap-2">
                          <Target className="w-5 h-5" />
                          優化建議
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <ul className="space-y-2">
                          {singleResult.optimization_suggestions.map((suggestion, index) => (
                            <li key={index} className="flex items-start gap-2">
                              <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0" />
                              <span className="text-sm">{suggestion}</span>
                            </li>
                          ))}
                        </ul>
                      </CardContent>
                    </Card>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="batch" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="w-5 h-5" />
                批量查詢分析
              </CardTitle>
              <CardDescription>
                輸入多個查詢（每行一個），系統將批量分析並生成統計報告
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Textarea
                placeholder="輸入查詢列表，每行一個查詢..."
                value={batchQueries}
                onChange={(e) => setBatchQueries(e.target.value)}
                rows={8}
                className="resize-none"
              />
              <Button 
                onClick={analyzeBatchQueries} 
                disabled={batchLoading}
                className="w-full"
              >
                {batchLoading ? (
                  <Loader2 className="w-4 h-4 animate-spin mr-2" />
                ) : (
                  <TrendingUp className="w-4 h-4 mr-2" />
                )}
                開始批量分析
              </Button>

              {batchResult && (
                <div className="space-y-4 mt-6">
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <Card>
                      <CardContent className="p-4 text-center">
                        <div className="text-2xl font-bold text-blue-600">
                          {batchResult.total_queries}
                        </div>
                        <div className="text-sm text-gray-600">總查詢數</div>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardContent className="p-4 text-center">
                        <div className="text-2xl font-bold text-green-600">
                          {batchResult.longtail_queries}
                        </div>
                        <div className="text-sm text-gray-600">長尾查詢</div>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardContent className="p-4 text-center">
                        <div className="text-2xl font-bold text-purple-600">
                          {batchResult.longtail_percentage.toFixed(1)}%
                        </div>
                        <div className="text-sm text-gray-600">長尾比例</div>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardContent className="p-4 text-center">
                        <div className="text-2xl font-bold text-orange-600">
                          {batchResult.summary.avg_longtail_score.toFixed(2)}
                        </div>
                        <div className="text-sm text-gray-600">平均長尾分數</div>
                      </CardContent>
                    </Card>
                  </div>

                  {batchResult.recommendations.length > 0 && (
                    <Alert>
                      <Target className="h-4 w-4" />
                      <AlertDescription>
                        <div className="font-medium mb-2">批量分析建議：</div>
                        <ul className="space-y-1">
                          {batchResult.recommendations.map((rec, index) => (
                            <li key={index} className="text-sm">• {rec}</li>
                          ))}
                        </ul>
                      </AlertDescription>
                    </Alert>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default LongTailQueryAnalyzer;

// 統計圖表組件
export const LongTailStatsChart: React.FC<{ data: BatchAnalysisResult }> = ({ data }) => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      <Card>
        <CardHeader>
          <CardTitle>查詢類型分布</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {data.results.reduce((acc, result) => {
              acc[result.query_type] = (acc[result.query_type] || 0) + 1;
              return acc;
            }, {} as Record<string, number>)}
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>意圖分布</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {data.results.reduce((acc, result) => {
              acc[result.intent] = (acc[result.intent] || 0) + 1;
              return acc;
            }, {} as Record<string, number>)}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
