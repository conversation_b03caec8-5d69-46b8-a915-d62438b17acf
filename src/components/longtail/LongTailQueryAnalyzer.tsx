'use client';

import React, { useState, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>L<PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, Search, TrendingUp, Target, Brain, BarChart3 } from 'lucide-react';
import { toast } from 'sonner';

// 類型定義
interface QueryFeatures {
  commercial_intent_score: number;
  word_count: number;
  character_count: number;
  language: string;
  has_question_words: boolean;
  has_brand_names: boolean;
  has_location: boolean;
  has_numbers: boolean;
  has_special_chars?: boolean;
  length?: number;
  specificity_score?: number;
  [key: string]: any;
}

interface LongTailAnalysisResult {
  query: string;
  query_type: string;
  intent: string;
  complexity: string;
  features: QueryFeatures;
  longtail_score: number;
  confidence: number;
  keywords: string[];
  semantic_category: string;
  competition_level: string;
  optimization_suggestions: string[];
  timestamp: string;
  similar_queries?: { query: string; score: number; features?: string[] }[];
  related_concepts?: { concept: string; relevance: number }[];
  few_shot_prediction?: {
    predicted_type: string;
    confidence: number;
    support_count: number;
  };
  commercial_intent?: {
    is_commercial: boolean;
    commercial_value: number;
    intent_category: string;
  };
}

interface SimpleBatchResult {
  query: string;
  status: string;
  message: string;
  timestamp: string;
}

interface BatchAnalysisResult {
  session_id: string;
  total_queries: number;
  processed_queries: number;
  results: SimpleBatchResult[];
  timestamp: string;
}

const LongTailQueryAnalyzer: React.FC = () => {
  const [singleQuery, setSingleQuery] = useState('');
  const [batchQueries, setBatchQueries] = useState('');
  const [singleResult, setSingleResult] = useState<LongTailAnalysisResult | null>(null);
  const [batchResult, setBatchResult] = useState<BatchAnalysisResult | null>(null);
  const [loading, setLoading] = useState(false);
  const [batchLoading, setBatchLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('single');

  // 單個查詢分析
  const analyzeSingleQuery = useCallback(async () => {
    setSingleResult(null);
    
    const trimmedQuery = singleQuery.trim();
    if (!trimmedQuery) {
      toast.error('請輸入查詢內容');
      return;
    }

    setLoading(true);

    try {
      const response = await fetch('http://localhost:8001/api/v1/longtail-enhanced-simple/analyze', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          query: trimmedQuery,
        }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('API返回錯誤:', response.status, errorText);
        throw new Error(`分析請求失敗 (${response.status})`);
      }

      const result: LongTailAnalysisResult = await response.json();
      setSingleResult(result);
      toast.success('查詢分析完成');
    } catch (error) {
      console.error('分析失敗:', error);
      toast.error('分析失敗，請稍後重試');
      setSingleResult(null);
    } finally {
      setLoading(false);
    }
  }, [singleQuery]);

  // 批量查詢分析
  const analyzeBatchQueries = useCallback(async () => {
    setBatchResult(null);
    
    const queries = batchQueries
      .split('\n')
      .map(q => q.trim())
      .filter(q => q.length > 0);

    if (queries.length === 0) {
      toast.error('請輸入查詢列表');
      return;
    }

    if (queries.length > 100) {
      toast.error('批量分析最多支持100個查詢');
      return;
    }

    setBatchLoading(true);
    try {
      const response = await fetch('http://localhost:8001/api/v1/longtail-enhanced-simple/analyze/batch', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          queries,
          session_name: `批量分析_${new Date().toLocaleString()}`,
          description: '前端批量查詢分析',
        }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('API返回錯誤:', response.status, errorText);
        throw new Error(`批量分析請求失敗 (${response.status})`);
      }

      const result: BatchAnalysisResult = await response.json();
      setBatchResult(result);
      toast.success(`批量分析完成，共處理 ${result.processed_queries} 個查詢`);
    } catch (error) {
      console.error('批量分析失敗:', error);
      toast.error('批量分析失敗，請稍後重試');
      setBatchResult(null);
    } finally {
      setBatchLoading(false);
    }
  }, [batchQueries]);

  // 獲取查詢類型顏色
  const getQueryTypeColor = (type: string) => {
    switch (type) {
      case 'head': return 'bg-red-100 text-red-800';
      case 'middle': return 'bg-yellow-100 text-yellow-800';
      case 'long_tail': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  // 獲取意圖顏色
  const getIntentColor = (intent: string) => {
    switch (intent) {
      case 'informational': return 'bg-blue-100 text-blue-800';
      case 'navigational': return 'bg-purple-100 text-purple-800';
      case 'transactional': return 'bg-green-100 text-green-800';
      case 'commercial': return 'bg-orange-100 text-orange-800';
      case 'local': return 'bg-pink-100 text-pink-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  // 獲取競爭程度顏色
  const getCompetitionColor = (level: string) => {
    switch (level) {
      case 'high': return 'bg-red-100 text-red-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'low': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold text-gray-900">長尾查詢識別系統</h1>
        <p className="text-gray-600">
          使用AI技術分析查詢類型、意圖和長尾特徵，優化SEO策略
        </p>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="single" className="flex items-center gap-2">
            <Search className="w-4 h-4" />
            單個查詢分析
          </TabsTrigger>
          <TabsTrigger value="batch" className="flex items-center gap-2">
            <TrendingUp className="w-4 h-4" />
            批量查詢分析
          </TabsTrigger>
        </TabsList>

        <TabsContent value="single" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>單個查詢分析</CardTitle>
              <CardDescription>
                輸入單個查詢進行詳細的長尾特徵分析
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex gap-2">
                <Input
                  placeholder="輸入要分析的查詢..."
                  value={singleQuery}
                  onChange={(e) => setSingleQuery(e.target.value)}
                  onKeyPress={(e) => {
                    if (e.key === 'Enter') {
                      analyzeSingleQuery();
                    }
                  }}
                  className="flex-1"
                />
                <Button
                  onClick={analyzeSingleQuery}
                  disabled={loading}
                  className="px-6"
                >
                  {loading ? (
                    <Loader2 className="w-4 h-4 animate-spin" />
                  ) : (
                    <Search className="w-4 h-4" />
                  )}
                  分析
                </Button>
              </div>

              {singleResult && (
                <div className="space-y-4 mt-6">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <Card>
                      <CardContent className="p-4">
                        <div className="text-sm text-gray-600">查詢類型</div>
                        <Badge className={getQueryTypeColor(singleResult.query_type)}>
                          {singleResult.query_type === 'long_tail' ? '長尾查詢' : 
                           singleResult.query_type === 'head' ? '頭部查詢' : '中部查詢'}
                        </Badge>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardContent className="p-4">
                        <div className="text-sm text-gray-600">查詢意圖</div>
                        <Badge className={getIntentColor(singleResult.intent)}>
                          {singleResult.intent === 'informational' ? '信息型' :
                           singleResult.intent === 'navigational' ? '導航型' :
                           singleResult.intent === 'transactional' ? '交易型' :
                           singleResult.intent === 'commercial' ? '商業型' : '本地型'}
                        </Badge>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardContent className="p-4">
                        <div className="text-sm text-gray-600">競爭程度</div>
                        <Badge className={getCompetitionColor(singleResult.competition_level)}>
                          {singleResult.competition_level === 'high' ? '高' :
                           singleResult.competition_level === 'medium' ? '中' : '低'}
                        </Badge>
                      </CardContent>
                    </Card>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="batch" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>批量查詢分析</CardTitle>
              <CardDescription>
                一次分析多個查詢，每行一個查詢（最多100個）
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Textarea
                placeholder="請輸入查詢列表，每行一個查詢..."
                value={batchQueries}
                onChange={(e) => setBatchQueries(e.target.value)}
                rows={8}
                className="min-h-[200px]"
              />
              
              <Button 
                onClick={analyzeBatchQueries} 
                disabled={batchLoading}
                className="w-full"
              >
                {batchLoading ? (
                  <Loader2 className="w-4 h-4 animate-spin mr-2" />
                ) : (
                  <TrendingUp className="w-4 h-4 mr-2" />
                )}
                開始批量分析
              </Button>

              {batchResult && (
                <div className="space-y-4 mt-6">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <Card>
                      <CardContent className="p-4 text-center">
                        <div className="text-2xl font-bold text-blue-600">
                          {batchResult.total_queries}
                        </div>
                        <div className="text-sm text-gray-600">總查詢數</div>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardContent className="p-4 text-center">
                        <div className="text-2xl font-bold text-green-600">
                          {batchResult.processed_queries}
                        </div>
                        <div className="text-sm text-gray-600">已處理</div>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardContent className="p-4 text-center">
                        <div className="text-2xl font-bold text-purple-600">
                          {batchResult.session_id.slice(0, 8)}...
                        </div>
                        <div className="text-sm text-gray-600">會話ID</div>
                      </CardContent>
                    </Card>
                  </div>

                  <Card>
                    <CardHeader>
                      <CardTitle>分析結果</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-2">
                        {batchResult.results.map((result, index) => (
                          <div key={index} className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                            <div className="flex-1">
                              <div className="font-medium">{result.query}</div>
                              <div className="text-sm text-gray-600">{result.message}</div>
                            </div>
                            <Badge className={result.status === 'success' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}>
                              {result.status === 'success' ? '成功' : '失敗'}
                            </Badge>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default LongTailQueryAnalyzer;
