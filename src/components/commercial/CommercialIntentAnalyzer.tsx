'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import { ShoppingBag, TrendingUp, BarChart3, Target, Brain, Filter, Check, AlertTriangle, BarChart } from 'lucide-react';
import { toast } from 'sonner';

interface CommercialIntentResult {
  query: string;
  rule_based_intent: string;
  rule_confidence: number;
  model_intent: string;
  model_confidence: number;
  commercial_value: number;
  is_commercial: boolean;
  intent_category: string;
  seo_strategies?: string[];
  improvement_suggestions?: string[];
  model_probabilities?: Record<string, number>;
}

interface CommercialIntentResponse {
  results: CommercialIntentResult[];
  commercial_value_boost_applied: boolean;
  status: string;
}

const CommercialIntentAnalyzer: React.FC = () => {
  const [query, setQuery] = useState('');
  const [batchQueries, setBatchQueries] = useState('');
  const [results, setResults] = useState<CommercialIntentResponse | null>(null);
  const [loading, setLoading] = useState(false);
  const [applyBoost, setApplyBoost] = useState(true);

  // 分析單一查詢的商業意圖
  const analyzeQuery = async () => {
    if (!query.trim()) {
      toast.error('請輸入查詢');
      return;
    }

    setLoading(true);
    try {
      const response = await fetch('http://localhost:8001/api/commercial-intent/analyze', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          queries: [query.trim()],
          apply_commercial_boost: applyBoost,
          include_details: true,
        }),
      });

      if (!response.ok) {
        throw new Error('分析失敗');
      }

      const data: CommercialIntentResponse = await response.json();
      setResults(data);
      toast.success('分析完成');
    } catch (error) {
      console.error('分析失敗:', error);
      toast.error('分析失敗');
    } finally {
      setLoading(false);
    }
  };

  // 分析批量查詢的商業意圖
  const analyzeBatchQueries = async () => {
    const queries = batchQueries
      .split('\n')
      .map(q => q.trim())
      .filter(q => q.length > 0);

    if (queries.length === 0) {
      toast.error('請輸入至少一個查詢');
      return;
    }

    setLoading(true);
    try {
      const response = await fetch('http://localhost:8001/api/commercial-intent/analyze', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          queries,
          apply_commercial_boost: applyBoost,
          include_details: true,
        }),
      });

      if (!response.ok) {
        throw new Error('批量分析失敗');
      }

      const data: CommercialIntentResponse = await response.json();
      setResults(data);
      toast.success(`已成功分析 ${queries.length} 個查詢`);
    } catch (error) {
      console.error('批量分析失敗:', error);
      toast.error('批量分析失敗');
    } finally {
      setLoading(false);
    }
  };

  // 獲取意圖顏色
  const getIntentColor = (intent: string): string => {
    switch (intent.toLowerCase()) {
      case 'informational':
        return 'bg-blue-100 text-blue-800 hover:bg-blue-200';
      case 'navigational':
        return 'bg-purple-100 text-purple-800 hover:bg-purple-200';
      case 'transactional':
        return 'bg-green-100 text-green-800 hover:bg-green-200';
      case 'commercial':
        return 'bg-orange-100 text-orange-800 hover:bg-orange-200';
      case 'local':
        return 'bg-indigo-100 text-indigo-800 hover:bg-indigo-200';
      default:
        return 'bg-gray-100 text-gray-800 hover:bg-gray-200';
    }
  };

  // 獲取商業值顏色
  const getCommercialValueColor = (value: number): string => {
    if (value >= 0.75) return 'text-green-600';
    if (value >= 0.5) return 'text-yellow-600';
    if (value >= 0.25) return 'text-orange-600';
    return 'text-red-600';
  };

  // 渲染意圖標籤
  const renderIntentLabel = (intent: string): string => {
    switch (intent.toLowerCase()) {
      case 'informational': return '信息型';
      case 'navigational': return '導航型';
      case 'transactional': return '交易型';
      case 'commercial': return '商業型';
      case 'local': return '本地型';
      default: return intent;
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <ShoppingBag className="w-5 h-5 mr-2" />
            商業意圖分析
          </CardTitle>
          <CardDescription>
            分析查詢的商業意圖和商業價值，提供SEO優化建議
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="single" className="space-y-4">
            <TabsList>
              <TabsTrigger value="single">單一查詢</TabsTrigger>
              <TabsTrigger value="batch">批量分析</TabsTrigger>
            </TabsList>

            <TabsContent value="single" className="space-y-4">
              <div className="flex items-center gap-2">
                <Input
                  value={query}
                  onChange={(e) => setQuery(e.target.value)}
                  placeholder="輸入查詢關鍵詞，例如：如何選擇最佳筆記型電腦"
                  className="flex-1"
                />
                <Button onClick={analyzeQuery} disabled={loading}>
                  {loading ? '分析中...' : '分析商業意圖'}
                </Button>
              </div>

              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="applyBoost"
                  checked={applyBoost}
                  onChange={(e) => setApplyBoost(e.target.checked)}
                  className="rounded border-gray-300"
                />
                <label htmlFor="applyBoost" className="text-sm text-gray-700">
                  應用商業價值增強（增強商業意圖分類效果）
                </label>
              </div>
            </TabsContent>

            <TabsContent value="batch" className="space-y-4">
              <Textarea
                value={batchQueries}
                onChange={(e) => setBatchQueries(e.target.value)}
                placeholder="每行輸入一個查詢關鍵詞"
                className="min-h-[150px]"
              />
              <div className="flex justify-between items-center">
                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="batchApplyBoost"
                    checked={applyBoost}
                    onChange={(e) => setApplyBoost(e.target.checked)}
                    className="rounded border-gray-300"
                  />
                  <label htmlFor="batchApplyBoost" className="text-sm text-gray-700">
                    應用商業價值增強
                  </label>
                </div>
                <Button onClick={analyzeBatchQueries} disabled={loading}>
                  {loading ? '分析中...' : '批量分析'}
                </Button>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {results && results.results.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>分析結果</CardTitle>
            <CardDescription>
              {results.commercial_value_boost_applied ? '已應用' : '未應用'} 商業價值增強
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {results.results.map((result, index) => (
              <div key={index} className="border rounded-lg p-4 space-y-4">
                <div className="flex flex-col sm:flex-row sm:justify-between sm:items-start gap-2">
                  <div>
                    <div className="font-medium text-lg">{result.query}</div>
                    <div className="flex flex-wrap gap-2 mt-1">
                      <Badge className={result.is_commercial ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}>
                        {result.is_commercial ? '商業意圖' : '非商業意圖'}
                      </Badge>
                      <Badge className={getIntentColor(result.rule_based_intent)}>
                        規則分類: {renderIntentLabel(result.rule_based_intent)}
                      </Badge>
                      <Badge className={getIntentColor(result.model_intent)}>
                        模型分類: {renderIntentLabel(result.model_intent)}
                      </Badge>
                    </div>
                  </div>
                  <div className="flex flex-col items-end">
                    <div className="font-semibold text-lg">
                      <span className="text-gray-600 mr-2">商業價值:</span>
                      <span className={getCommercialValueColor(result.commercial_value)}>
                        {(result.commercial_value * 100).toFixed(1)}%
                      </span>
                    </div>
                    <div className="text-sm">
                      <span className="text-gray-600 mr-2">意圖類別:</span>
                      <span>{result.intent_category}</span>
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                  <div>
                    <div className="font-medium mb-1">規則置信度</div>
                    <div className="flex items-center">
                      <div className="w-full bg-gray-200 rounded-full h-2 mr-2">
                        <div
                          className="bg-blue-600 h-2 rounded-full"
                          style={{ width: `${result.rule_confidence * 100}%` }}
                        ></div>
                      </div>
                      <span>{(result.rule_confidence * 100).toFixed(1)}%</span>
                    </div>
                  </div>
                  <div>
                    <div className="font-medium mb-1">模型置信度</div>
                    <div className="flex items-center">
                      <div className="w-full bg-gray-200 rounded-full h-2 mr-2">
                        <div
                          className="bg-green-600 h-2 rounded-full"
                          style={{ width: `${result.model_confidence * 100}%` }}
                        ></div>
                      </div>
                      <span>{(result.model_confidence * 100).toFixed(1)}%</span>
                    </div>
                  </div>
                  <div>
                    <div className="font-medium mb-1">商業價值分數</div>
                    <div className="flex items-center">
                      <div className="w-full bg-gray-200 rounded-full h-2 mr-2">
                        <div
                          className={`h-2 rounded-full ${
                            result.commercial_value >= 0.7
                              ? 'bg-green-600'
                              : result.commercial_value >= 0.4
                              ? 'bg-yellow-600'
                              : 'bg-red-600'
                          }`}
                          style={{ width: `${result.commercial_value * 100}%` }}
                        ></div>
                      </div>
                      <span>{(result.commercial_value * 100).toFixed(1)}%</span>
                    </div>
                  </div>
                </div>

                {result.model_probabilities && (
                  <div>
                    <div className="font-medium mb-2">意圖機率分布</div>
                    <div className="grid grid-cols-2 md:grid-cols-5 gap-2">
                      {Object.entries(result.model_probabilities).map(([intent, prob]) => (
                        <div key={intent} className="border rounded p-2">
                          <div className="text-xs text-gray-600">{renderIntentLabel(intent)}</div>
                          <div className="font-medium">{(prob * 100).toFixed(1)}%</div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {result.seo_strategies && result.seo_strategies.length > 0 && (
                  <div>
                    <div className="font-medium mb-2">SEO 策略建議</div>
                    <ul className="list-disc pl-5 space-y-1">
                      {result.seo_strategies.map((strategy, idx) => (
                        <li key={idx}>{strategy}</li>
                      ))}
                    </ul>
                  </div>
                )}

                {result.improvement_suggestions && result.improvement_suggestions.length > 0 && (
                  <div>
                    <div className="font-medium mb-2">內容優化建議</div>
                    <ul className="list-disc pl-5 space-y-1">
                      {result.improvement_suggestions.map((suggestion, idx) => (
                        <li key={idx}>{suggestion}</li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            ))}
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default CommercialIntentAnalyzer;
