{"dashboard": {"id": null, "title": "長尾查詢識別系統監控", "tags": ["longtail", "seo", "ai"], "style": "dark", "timezone": "browser", "refresh": "30s", "time": {"from": "now-1h", "to": "now"}, "panels": [{"id": 1, "title": "系統概覽", "type": "stat", "gridPos": {"h": 8, "w": 24, "x": 0, "y": 0}, "targets": [{"expr": "rate(longtail_analysis_requests_total[5m])", "legendFormat": "請求速率/秒"}, {"expr": "longtail_analysis_duration_seconds{quantile=\"0.95\"}", "legendFormat": "95%分位延遲"}, {"expr": "longtail_memory_usage_bytes / 1024 / 1024", "legendFormat": "內存使用(MB)"}, {"expr": "longtail_cpu_usage_percent", "legendFormat": "CPU使用率(%)"}], "fieldConfig": {"defaults": {"unit": "short", "min": 0}}}, {"id": 2, "title": "分析請求趨勢", "type": "graph", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}, "targets": [{"expr": "rate(longtail_analysis_requests_total[5m])", "legendFormat": "{{method}} - {{status}}"}], "yAxes": [{"label": "請求/秒", "min": 0}]}, {"id": 3, "title": "分析耗時分布", "type": "graph", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}, "targets": [{"expr": "histogram_quantile(0.50, rate(longtail_analysis_duration_seconds_bucket[5m]))", "legendFormat": "50%分位"}, {"expr": "histogram_quantile(0.95, rate(longtail_analysis_duration_seconds_bucket[5m]))", "legendFormat": "95%分位"}, {"expr": "histogram_quantile(0.99, rate(longtail_analysis_duration_seconds_bucket[5m]))", "legendFormat": "99%分位"}], "yAxes": [{"label": "秒", "min": 0}]}, {"id": 4, "title": "長尾分數分布", "type": "heatmap", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 16}, "targets": [{"expr": "rate(longtail_score_distribution_bucket[5m])", "legendFormat": "{{le}}"}], "heatmap": {"xBucketSize": null, "yBucketSize": null, "xAxis": {"show": true}, "yAxis": {"show": true, "label": "長尾分數"}}}, {"id": 5, "title": "置信度分布", "type": "heatmap", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 16}, "targets": [{"expr": "rate(longtail_confidence_distribution_bucket[5m])", "legendFormat": "{{le}}"}], "heatmap": {"xBucketSize": null, "yBucketSize": null, "xAxis": {"show": true}, "yAxis": {"show": true, "label": "置信度"}}}, {"id": 6, "title": "查詢類型分布", "type": "piechart", "gridPos": {"h": 8, "w": 8, "x": 0, "y": 24}, "targets": [{"expr": "longtail_query_types_total", "legendFormat": "{{query_type}}"}]}, {"id": 7, "title": "查詢意圖分布", "type": "piechart", "gridPos": {"h": 8, "w": 8, "x": 8, "y": 24}, "targets": [{"expr": "longtail_intents_total", "legendFormat": "{{intent}}"}]}, {"id": 8, "title": "語言分布", "type": "piechart", "gridPos": {"h": 8, "w": 8, "x": 16, "y": 24}, "targets": [{"expr": "longtail_languages_total", "legendFormat": "{{language}}"}]}, {"id": 9, "title": "緩存性能", "type": "graph", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 32}, "targets": [{"expr": "rate(longtail_cache_hits_total[5m])", "legendFormat": "緩存命中 - {{cache_type}}"}, {"expr": "rate(longtail_cache_misses_total[5m])", "legendFormat": "緩存未命中 - {{cache_type}}"}], "yAxes": [{"label": "次數/秒", "min": 0}]}, {"id": 10, "title": "系統資源使用", "type": "graph", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 32}, "targets": [{"expr": "longtail_memory_usage_bytes / 1024 / 1024", "legendFormat": "內存使用(MB)"}, {"expr": "longtail_cpu_usage_percent", "legendFormat": "CPU使用率(%)"}], "yAxes": [{"label": "使用量", "min": 0}]}, {"id": 11, "title": "批量分析大小分布", "type": "graph", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 40}, "targets": [{"expr": "histogram_quantile(0.50, rate(longtail_batch_size_bucket[5m]))", "legendFormat": "50%分位批量大小"}, {"expr": "histogram_quantile(0.95, rate(longtail_batch_size_bucket[5m]))", "legendFormat": "95%分位批量大小"}], "yAxes": [{"label": "批量大小", "min": 0}]}, {"id": 12, "title": "模型載入時間", "type": "stat", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 40}, "targets": [{"expr": "longtail_model_load_time_seconds", "legendFormat": "模型載入時間"}], "fieldConfig": {"defaults": {"unit": "s", "min": 0}}}], "templating": {"list": [{"name": "instance", "type": "query", "query": "label_values(longtail_analysis_requests_total, instance)", "refresh": 1, "includeAll": true, "multi": true}]}, "annotations": {"list": [{"name": "部署事件", "datasource": "prometheus", "expr": "changes(longtail_model_load_time_seconds[1h]) > 0", "titleFormat": "模型重新載入", "textFormat": "模型載入時間: {{value}}秒"}]}}}